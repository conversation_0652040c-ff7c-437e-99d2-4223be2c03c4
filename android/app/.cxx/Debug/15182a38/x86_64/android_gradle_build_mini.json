{"buildFiles": ["/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/supervisor copy 2/android/app/.cxx/Debug/15182a38/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/supervisor copy 2/android/app/.cxx/Debug/15182a38/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}