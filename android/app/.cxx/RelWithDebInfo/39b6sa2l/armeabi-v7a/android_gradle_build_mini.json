{"buildFiles": ["/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/supervisor copy 2/android/app/.cxx/RelWithDebInfo/39b6sa2l/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/supervisor copy 2/android/app/.cxx/RelWithDebInfo/39b6sa2l/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}