# Keep Amazon classes
-keep class com.amazon.android.Kiwi { *; }
-keep class com.amazon.android.framework.** { *; }
-keep class com.amazon.android.licensing.** { *; }
-keep class com.amazon.device.iap.** { *; }
-keep class com.amazon.venezia.** { *; }

# Keep Google Play Core classes
-keep class com.google.android.play.core.splitcompat.** { *; }
-keep class com.google.android.play.core.splitinstall.** { *; }
-keep class com.google.android.play.core.tasks.** { *; }
-keep class com.google.android.play.core.** { *; }

# Keep Huawei classes
-keep class com.huawei.android.os.BuildEx$VERSION { *; }
-keep class com.huawei.android.telephony.** { *; }
-keep class com.huawei.hianalytics.** { *; }
-keep class com.huawei.hms.** { *; }
-keep class com.huawei.libcore.io.** { *; }
-keep class com.huawei.secure.android.** { *; }

# Keep BouncyCastle crypto classes
-keep class org.bouncycastle.** { *; }
-keep class org.bouncycastle.crypto.** { *; }
-keep class org.bouncycastle.crypto.engines.** { *; }
-keep class org.bouncycastle.crypto.prng.** { *; }
-keep class org.bouncycastle.jsse.** { *; }
-keep class org.bouncycastle.jsse.provider.** { *; }

# Keep security providers and SSL related classes
-keep class org.conscrypt.** { *; }
-keep class org.openjsse.** { *; }
-keep class javax.net.ssl.** { *; }

# Keep OkHttp and its platform implementations
-keepclassmembers class okhttp3.** { *; }
-keep class okhttp3.internal.platform.** { *; }

# Keep Flutter specific classes
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Handle duplicate PluginRegistry class
-dontwarn io.flutter.plugin.common.PluginRegistry
-keep class io.flutter.plugin.common.PluginRegistry { *; }

# Keep Firebase classes
-keep class com.google.firebase.** { *; }

# Keep Google Play Billing classes
-keep class com.android.billingclient.** { *; }
-keep class com.android.vending.billing.** { *; }

# Don't warn about missing classes
-dontwarn com.amazon.**
-dontwarn com.huawei.**
-dontwarn org.bouncycastle.**
-dontwarn org.conscrypt.**
-dontwarn org.openjsse.**
-dontwarn com.google.android.play.core.**
-dontwarn okhttp3.internal.platform.**

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep Parcelable classes
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}