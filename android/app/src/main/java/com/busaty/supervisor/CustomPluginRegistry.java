package com.busaty.supervisor;

import android.app.Activity;
import android.content.Context;
import io.flutter.plugin.common.BinaryMessenger;

/**
 * Compatibility class to provide the Registrar interface that was removed in newer Flutter versions.
 * This is needed for plugins that still use the old plugin registration method.
 */
public interface CustomPluginRegistry {
    /**
     * Compatibility interface for the old plugin registration method.
     */
    interface Registrar {
        Activity activity();
        Context context();
        CustomPluginRegistry.Registrar addRequestPermissionsResultListener(
                CustomPluginRegistry.RequestPermissionsResultListener listener);
        CustomPluginRegistry.Registrar addActivityResultListener(
                CustomPluginRegistry.ActivityResultListener listener);
        BinaryMessenger messenger();
    }

    /**
     * Callback interface for handling activity results.
     */
    interface ActivityResultListener {
        boolean onActivityResult(int requestCode, int resultCode, android.content.Intent data);
    }

    /**
     * Callback interface for handling permission results.
     */
    interface RequestPermissionsResultListener {
        boolean onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults);
    }
}
