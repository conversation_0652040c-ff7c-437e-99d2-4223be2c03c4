package com.joutvhu.openfile.utils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

public class JsonUtil {
    public static String toJson(Map<String, Object> map) {
        try {
            JSONObject jsonObject = new JSONObject();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                jsonObject.put(entry.getKey(), entry.getValue());
            }
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return "{}";
        }
    }
}
