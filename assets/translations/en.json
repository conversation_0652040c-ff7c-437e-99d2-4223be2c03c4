{"Busaty - Bus": "Busaty - Bus", "home": "Home", "login": "<PERSON><PERSON>", "email": "Email", "username": "Username", "password": "Password", "forgetPassword": "Forget Password ? ", "remember": "Remember", "notHaveAccount": "Not Have Account ? ", "createAccount": "Create Account", "newPassword": "New Password", "againPassword": "Write Again Password", "changePassword": "Change Password", "signup": "Signup", "name": "Name", "phoneNumber": "Phone Number", "confirmPassword": "Confirm Password", "haveAccount": "Have Account ? ", "forget": "Forget Password", "sendCodeRegister": "code will be sent to the registered email to recover the account", "sendCodeAgain": "send code again", "passwordChangeSuccess": "Password Change Success", "goHome": "Go to Home", "oldPassword": "Old Password", "save": "Save", "sendCode": "Send Code", "next": "Next", "trips": "Trips", "busLocation": "Bus Current Location", "setting": "Setting", "myBus": "My Bus", "morningTrips": "Morning Trip", "eveningTrips": "Evening Trip", "busData": "Bus Data", "showStudentBus": "Show Bus students", "addressOnMap": "Address on the map", "parents": "Parents", "note": "notes", "profile": "Profile", "languages": "Languages", "help": "Help", "english": "English", "arabic": "Arabic", "busName": "Bus name", "notFound": "not Found", "updateProfile": "Update Profile", "add": "Add", "showStudent": "Show Student", "showParent": "Show Parent", "addressStudentOnMap": "Address Student on the map", "searchForStudent": "Search for student", "studentBus": "Bus Students", "address": "Address", "stage": "Stage", "show": "Show", "startTrip": "Start Trip", "sendNotification": "Send Notification", "students": "Students", "absence": "Absence", "endTrip": "End Trip", "studentData": "Student Data", "logout": "Logout", "driver": "Driver", "supervisor": "Supervisor", "good_morning": "Good Morning", "good_evening": "Good Evening", "line": "Line", "school": "School", "bus_number": "Bus Number", "students_not_found": "Students Not Found", "parents_not_found": "Parents Not Found", "blood_type": "Blood Type", "birth_date": "Birth Date", "code": "First Code", "secret_code": "Secret Code", "notification": "Notify", "call": "Call", "waiting": "Waiting", "absent_students": "Absent", "absent": "Absent", "on_bus": "On Bus", "move_to_present": "Move To Present", "move_to_waiting": "Move To Waiting", "move_to_bus": "Move To Bus", "student_address": "Student Address", "arrived_home": "Arrived Home", "student_arrived_home": "Arrived Home", "notifications": "Notifications", "sure_end_trip": "Are you sure to end the trip?", "sure_start_trip": "Are you sure to start the trip?", "yes": "Yes", "no": "No", "deleteAccountTitle": "Delete Account Confirmation", "deleteAccountConfirm": "Are you sure you want to delete your account permanently?", "deleteAccountNote": "All your data will be deleted from our system automatically after 30 days", "deleteAccount": "Delete Account", "cancel": "Cancel", "contact_us": "Contact Us", "get_in_touch": "Get in Touch", "wed_love_to_hear": "We'd love to hear from you", "enter_your_name": "Enter your name", "enter_your_email": "Enter your email", "describe_problem": "Describe your problem or feedback", "please_enter_name": "Please enter your name", "please_enter_email": "Please enter your email", "please_valid_email": "Please enter a valid email", "please_describe_problem": "Please describe your problem", "message_too_long": "Message cannot be longer than 1000 characters", "contact_directly": "Or contact us directly:", "copy": "Copy", "email_copied": "Email copied to clipboard", "sending": "Send<PERSON>", "email_sent": "<PERSON><PERSON> sent successfully!", "failed_to_send": "Failed to send email: {error}", "trip_closed_start_trip": "Trip is closed now, click start trip to show students", "classroom": "Classroom", "maintenance_title": "Under Maintenance", "maintenance_message": "The app is currently being updated. Please try again later.", "maintenance_checking": "Checking...", "maintenance_retry": "Try Again", "attendants": "Attendants", "app_updating": "App is updating"}