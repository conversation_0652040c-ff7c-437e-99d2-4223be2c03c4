import UIKit
import Flutter
import flutter_background_service_ios

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        SwiftFlutterBackgroundServicePlugin.taskIdentifier = "your.custom.task.identifier"
        
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let batteryChannel = FlutterMethodChannel(name: "battery_optimization",
                                                binaryMessenger: controller.binaryMessenger)
        
        batteryChannel.setMethodCallHandler({[weak self]
            (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            switch call.method {
            case "isIgnoringBatteryOptimizations":
                // iOS doesn't have direct battery optimization controls
                // Always return true to indicate that the app can run in background
                result(true)
            case "requestIgnoreBatteryOptimization":
                // On iOS, we can direct users to the Settings app
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
                        result(true)
                    } else {
                        result(FlutterError(code: "UNAVAILABLE",
                                            message: "Cannot open Settings",
                                            details: nil))
                    }
                } else {
                    result(false)
                }
            default:
                result(FlutterMethodNotImplemented)
            }
        })
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}
