import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/ads_model.dart';
import '../../../data/repo/ads_repo.dart';

part 'ads_state.dart';

class AdsCubit extends Cubit<AdsState> {
  AdsCubit() : super(AdsInitial());
  static AdsCubit get(context)=> BlocProvider.of(context);
  final _adsRepo = AdsRepo();

  AdsModel? ads;

  Future<void> getAds() async {
    emit(AdsLoading());
    try {
      final response = await _adsRepo.ads();
      if (response.status == true) {
        debugPrint(response.toString());
        ads = response;
      } else {
        debugPrint("ErrorState ${response.message}");
      }
    } catch (e , stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
    }
    emit(AdsInitial());
  }
}
