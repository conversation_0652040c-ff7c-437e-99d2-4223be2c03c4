import 'package:bus_driver/data/models/trips_model/evening_trip_model/evening_trip_status_model.dart';
import 'package:bus_driver/data/repo/evening_trip_repo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../data/models/trips_model/evening_trip_model/absent_evening_trip_model.dart';
import '../../../data/models/trips_model/evening_trip_model/arrived_evening_trip_model.dart';
import '../../../data/models/trips_model/evening_trip_model/end_evening_trip_model.dart';
import '../../../data/models/trips_model/evening_trip_model/on_bus_evening_trip_model.dart';
import '../../../data/models/trips_model/evening_trip_model/start_evening_trip_model.dart';
import '../../../views/custom_widgets/custom_text.dart';
import '../morning_trip_cubit/morning_trip_cubit.dart';

part 'evening_trip_state.dart';

class EveningTripCubit extends Cubit<EveningTripState> {
  EveningTripCubit() : super(EveningTripInitial());
  static EveningTripCubit get(context) => BlocProvider.of(context);
  final _eveningTripRepo = EveningTripRepo();
  final service = FlutterBackgroundService();

  EveningTripStatusModel? eveningTripStatusModel;
  StartEveningTripModel? startEveningTripModel;
  EndEveningTripModel? endEveningTripModel;
  OnBusEveningTripModel? onBusEveningTripModel;
  ArrivedEveningTripModel? arrivedEveningTripModel;
  AbsentEveningTripModel? absentEveningTripModel;
  bool isLoadingEveningTrips = false;

  Future<void> getEveningTripStatus() async {
    emit(EveningTripStatusLoadingState());
    try {
      final response = await _eveningTripRepo.eveningTripStatusRepo();
      if (response.status == true) {
        debugPrint(response.toString());
        eveningTripStatusModel = response;
        tripId = response.trip?.id;
        debugPrint("*status* tripId: $tripId");
        getOnBusEveningTrip(tripId: tripId);
        getArrivedEveningTrip();
        getAbsentEveningTrip();
        emit(StartEveningTripSuccessState());
        MorningTripCubit.get().initSocket();
        MorningTripCubit.get().startLocationTracking();
      } else {
        onBusEveningTripModel?.presentOnBus?.clear();
        arrivedEveningTripModel?.arrived?.clear();
        absentEveningTripModel?.absences?.clear();
        emit(EveningTripStatusErrorState(error: response.message));
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("getOnBusEveningTrip: catch error $e");
      emit(EveningTripStatusErrorState(error: e.toString()));
    }
  }

  Future<void> startEveningTrip({
    String? latitude,
    String? longitude,
    // required bool notify,
  }) async {
    emit(StartEveningTripLoadingState());
    try {
      final response = await _eveningTripRepo.startEveningTripRepo(
        latitude: latitude,
        longitude: longitude,
        // notify: notify
      );

      if (response.status == true) {
        MorningTripCubit.get().initSocket();
        debugPrint(response.toString());
        startEveningTripModel = response;
        tripId = response.data?.trip?.id;
        debugPrint("*start* tripId: $tripId");
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم بدأ الرحلة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        getOnBusEveningTrip(tripId: tripId);
        getArrivedEveningTrip();
        getAbsentEveningTrip();
        emit(StartEveningTripSuccessState());
        MorningTripCubit.get().startLocationTracking();
      } else {
        debugPrint("ErrorState: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.message,
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        emit(StartEveningTripErrorState());
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("**catch error $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "تعذر الوصول حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
      emit(StartEveningTripErrorState());
    }
  }

  Future<void> endEveningTrip({
    int? tripId,
  }) async {
    emit(EndEveningTripLoadingState());
    try {
      final response = await _eveningTripRepo.endEveningTripRepo(
        tripId: tripId,
      );
      if (response.status == true) {
        debugPrint(response.toString());
        endEveningTripModel = response;
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم إنهاء الرحلة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        onBusEveningTripModel?.presentOnBus?.clear();
        arrivedEveningTripModel?.arrived?.clear();
        absentEveningTripModel?.absences?.clear();
        emit(EndEveningTripSuccessState());
        MorningTripCubit.get().socket.dispose();
        bool isRunning = await service.isRunning();
        if (isRunning) {
          service.invoke('stopService');
        }
        MorningTripCubit.get().stopLocationTracking();
      } else {
        debugPrint("ErrorState: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.message,
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        emit(EndEveningTripErrorState());
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("**catch error $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "تعذر الوصول حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
      emit(EndEveningTripErrorState());
    }
  }

  Future<void> getOnBusEveningTrip({
    int? tripId,
  }) async {
    // emit(OnBusEveningTripLoadingState());
    try {
      final response = await _eveningTripRepo.onBusEveningTripRepo(
        tripId: tripId,
      );
      if (response.errors == false) {
        debugPrint(response.toString());
        onBusEveningTripModel = response;
        emit(OnBusEveningTripSuccessState(response.presentOnBus));
      } else {
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("getOnBusEveningTrip: catch error $e");
    }
  }

  Future<void> arrivedStudentEveningTrip({
    int? tripId,
    String? studentId,
  }) async {
    try {
      isLoadingEveningTrips = true;
      final response = await _eveningTripRepo.arrivedStudentEveningTripRepo(
        studentId: studentId,
      );
      if (response.status == false) {
        debugPrint(response.toString());
        onBusEveningTripModel?.presentOnBus
            ?.removeWhere((element) => element.studentId == studentId);
        getArrivedEveningTrip();
        isLoadingEveningTrips = false;
        emit(OnBusEveningTripSuccessState(onBusEveningTripModel?.presentOnBus));
      } else {
        isLoadingEveningTrips = false;
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      isLoadingEveningTrips = false;
      debugPrint("studentAbsentMorningTrip catch error $e");
    }
  }

  Future<void> getArrivedEveningTrip() async {
    // emit(ArrivedEveningTripLoadingState());
    try {
      final response = await _eveningTripRepo.arrivedEveningTripRepo();
      if (response.errors == false) {
        debugPrint(response.toString());
        arrivedEveningTripModel = response;
        emit(ArrivedEveningTripSuccessState(response.arrived));
      } else {
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("getOnBusEveningTrip: catch error $e");
    }
  }

  Future<void> removeArrivedEveningTrip({
    String? studentId,
  }) async {
    isLoadingEveningTrips = true;
    try {
      final response = await _eveningTripRepo.removeArrivedEveningTripRepo(
        studentId: studentId,
      );
      if (response.status == false) {
        debugPrint(response.toString());
        arrivedEveningTripModel?.arrived
            ?.removeWhere((element) => element.id == studentId);
        isLoadingEveningTrips = false;
        emit(ArrivedEveningTripSuccessState(arrivedEveningTripModel?.arrived));
        getOnBusEveningTrip(tripId: tripId);
      } else {
        isLoadingEveningTrips = false;
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      isLoadingEveningTrips = false;
      debugPrint("studentAbsentMorningTrip catch error $e");
    }
  }

  Future<void> studentAbsentEveningTrip({
    String? studentId,
  }) async {
    isLoadingEveningTrips = true;
    try {
      final response = await _eveningTripRepo.studentAbsentEveningTripRepo(
        studentId: studentId,
      );
      if (response.errors == false) {
        debugPrint(response.toString());
        onBusEveningTripModel?.presentOnBus
            ?.removeWhere((element) => element.studentId == studentId);
        getAbsentEveningTrip();
        isLoadingEveningTrips = false;
        emit(OnBusEveningTripSuccessState(onBusEveningTripModel?.presentOnBus));
      } else {
        isLoadingEveningTrips = false;
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      isLoadingEveningTrips = false;
      debugPrint("studentAbsentMorningTrip catch error $e");
    }
  }

  Future<void> getAbsentEveningTrip() async {
    try {
      final response = await _eveningTripRepo.absentEveningTripRepo();
      if (response.errors == false) {
        debugPrint(response.toString());
        absentEveningTripModel = response;
        emit(AbsentEveningTripSuccessState(response.absences));
      } else {
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("getAbsentEveningTrip: catch error $e");
    }
  }

  Future<void> removeAbsentEveningTrip({
    String? studentId,
  }) async {
    isLoadingEveningTrips = true;
    try {
      final response = await _eveningTripRepo.removeAbsentEveningTripRepo(
        studentId: studentId,
      );
      if (response.status == true) {
        debugPrint(response.toString());
        absentEveningTripModel?.absences
            ?.removeWhere((element) => element.id == studentId);
        getOnBusEveningTrip(tripId: tripId);
        isLoadingEveningTrips = false;
        emit(AbsentEveningTripSuccessState(absentEveningTripModel?.absences));
      } else {
        isLoadingEveningTrips = false;
        debugPrint("ErrorState: ${response.message}");
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      isLoadingEveningTrips = false;
      debugPrint("catch error $e");
    }
  }

  Future<void> sendMessageEveningTrip({
    String? studentId,
    int? messageId,
    // required String notificationsType,
  }) async {
    try {
      final response = await _eveningTripRepo.sendMessageEveningTripRepo(
        messageId: messageId,
        studentId: studentId,
        // notificationsType: notificationsType
      );
      if (response.errors == false) {
        debugPrint(response.toString());
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم إرسال الرسالة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        debugPrint("ErrorState: ${response.message}");
        // SnackBar snackBar = SnackBar(
        //   backgroundColor: TColor.redAccent,
        //   content: CustomText(
        //     text: response.message,
        //     color: TColor.white,
        //     maxLine: 3,
        //   ),
        // );
        // snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  Future<void> sendMessageForAllEveningTrip({
    required int staticMessageId,
  }) async {
    try {
      final response = await _eveningTripRepo.sendMessageForAllEveningTripRepo(
          staticMessageId: staticMessageId);
      if (response.errors == false) {
        debugPrint(response.toString());
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم إرسال الرسالة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        debugPrint("ErrorState: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.message ?? "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  // Future<bool> sendMessageForAllEveningTripBool({
  //   String? message,
  //   required String? notificationType,
  //   required String? message_en,
  // }) async {
  //   try {
  //     final response = await _eveningTripRepo.sendMessageForAllEveningTripRepo(
  //       message: message,
  //       message_en: message_en,
  //       notificationType: notificationType,
  //     );
  //     if (response.errors == false) {
  //       debugPrint(response.toString());
  //       const SnackBar snackBar = SnackBar(
  //         backgroundColor: TColor.greenSuccess,
  //         content: CustomText(
  //           text: "تم إرسال الرسالة بنجاح",
  //           color: TColor.white,
  //           maxLine: 3,
  //         ),
  //       );
  //       snackBarKey.currentState?.showSnackBar(snackBar);
  //       return response.errors!;
  //     } else {
  //       debugPrint("ErrorState: ${response.message}");
  //       SnackBar snackBar = SnackBar(
  //         backgroundColor: TColor.redAccent,
  //         content: CustomText(
  //           text: response.message,
  //           color: TColor.white,
  //           maxLine: 3,
  //         ),
  //       );
  //       snackBarKey.currentState?.showSnackBar(snackBar);
  //       return response.errors!;
  //     }
  //   } catch (e , stackTrace) {
  //     debugPrint("catch error $e");
  //     const SnackBar snackBar = SnackBar(
  //       backgroundColor: TColor.redAccent,
  //       content: CustomText(
  //         text: "حدث خطأ حاول مرة أخرى",
  //         color: TColor.white,
  //         maxLine: 3,
  //       ),
  //     );
  //     snackBarKey.currentState?.showSnackBar(snackBar);
  //     return true;
  //   }
  // }
}
