part of 'evening_trip_cubit.dart';

abstract class EveningTripState extends Equatable {}

class EveningTripInitial extends EveningTripState {
  @override
  List<Object> get props => [];
}

class EveningTripStatusLoadingState extends EveningTripState{
  @override
  List<Object> get props => [];
}

class EveningTripStatusErrorState extends EveningTripState{
  final String? error;
  EveningTripStatusErrorState({this.error});
  @override
  List<Object?> get props => [error];
}


class StartEveningTripLoadingState extends EveningTripState{
  @override
  List<Object> get props => [];
}

class StartEveningTripSuccessState extends EveningTripState {
  @override
  List<Object?> get props =>[];
}

class StartEveningTripErrorState extends EveningTripState {
  @override
  List<Object?> get props =>[];
}


class EndEveningTripLoadingState extends EveningTripState {
  @override
  List<Object?> get props =>[];
}

class EndEveningTripSuccessState extends EveningTripState {
  @override
  List<Object?> get props =>[];
}

class EndEveningTripErrorState extends EveningTripState {
  @override
  List<Object?> get props =>[];
}

class OnBusEveningTripSuccessState extends EveningTripState {
  final List<PresentOnBus>? presentOnBus;
  OnBusEveningTripSuccessState(this.presentOnBus);
  @override
  List<Object?> get props =>[presentOnBus?.length];
}

class ArrivedEveningTripSuccessState extends EveningTripState {
  final List<Arrived>? arrived;
  ArrivedEveningTripSuccessState(this.arrived);
  @override
  List<Object?> get props =>[arrived?.length];
}

class AbsentEveningTripSuccessState extends EveningTripState {
  final List<Absence>? absences;
  AbsentEveningTripSuccessState(this.absences);
  @override
  List<Object?> get props =>[absences?.length];
}