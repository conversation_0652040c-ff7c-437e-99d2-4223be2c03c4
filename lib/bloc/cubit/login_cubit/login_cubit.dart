import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/repo/login_repo.dart';
import '../../../helper/data_state.dart';
import '../../../helper/response_state.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  final _loginRepo = LoginRepo();
  LoginCubit() : super(const LoginState());

  Future<void> login({
    String? username,
    String? password,
  }) async {
    emit(state.copyWith(rStates: ResponseState.loading));
    DataState<LoginDataModelh> response = await _loginRepo.repo(
      username: username,
      password: password,
    );

    if (response is DataSuccess) {
      print(response.data?.emailVerifiedAt);
      if (response.data?.emailVerifiedAt == null) {
        emit(
          state.copyWith(
            rStates: ResponseState.needVerivecation,
          ),
        );
      } else {
        emit(
          state.copyWith(
            loginModel: response.data,
            rStates: ResponseState.success,
          ),
        );
      }
    } else {
      emit(
        state.copyWith(rStates: ResponseState.failure),
      );
    }
  }
}
