part of 'login_cubit.dart';

class LoginState extends Equatable {
  final LoginDataModelh? loginModel;
  final ResponseState rStates;
  final String? message;

  const LoginState({
    this.loginModel,
    this.rStates = ResponseState.init,
    this.message,
  });

  LoginState copyWith({
    LoginDataModelh? loginModel,
    ResponseState? rStates,
    String? message,
  }) {
    return LoginState(
      loginModel: loginModel ?? this.loginModel,
      rStates: rStates ?? this.rStates,
      message: message ?? this.message,
    );
  }

  @override
  List<Object?> get props => [
    loginModel,
    rStates,
    message,
  ];

}

class LoginInitial extends LoginState {}
