import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../data/models/change_password_models/change_password_models.dart';
import '../../../data/repo/logout_repo.dart';
import '../../../helper/data_state.dart';
import '../../../helper/response_state.dart';
import 'logout_states.dart';

class LogoutCubit extends Cubit<LogoutStates> {
  final _logoutRepo = LogoutRepo();
  LogoutCubit() : super(const LogoutStates());

  Future<void> logout() async {
    emit(state.copyWith(rStates: ResponseState.loading));
    DataState<ChangePasswordModels> response = await _logoutRepo.repo();

    if (response is DataSuccess) {
      emit(state.copyWith(
          rStates: ResponseState.success, changePasswordModels: response.data));
    } else {
      emit(state.copyWith(rStates: ResponseState.failure));
    }
  }
}
