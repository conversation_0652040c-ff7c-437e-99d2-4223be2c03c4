import 'package:equatable/equatable.dart';

import '../../../data/models/change_password_models/change_password_models.dart';
import '../../../helper/response_state.dart';

class LogoutStates extends Equatable {
  final ChangePasswordModels? changePasswordModels;
  final ResponseState rStates;

  const LogoutStates({
    this.changePasswordModels,
    this.rStates = ResponseState.init,
  });

  LogoutStates copyWith({
    ChangePasswordModels? changePasswordModels,
    ResponseState? rStates,
  }) {
    return LogoutStates(
      changePasswordModels: changePasswordModels ?? this.changePasswordModels,
      rStates: rStates ?? this.rStates,
    );
  }

  @override
  List<Object?> get props => [
        changePasswordModels,
        rStates,
      ];
}
