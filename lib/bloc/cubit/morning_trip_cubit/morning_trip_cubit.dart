import 'dart:async';
import 'dart:convert';

import 'package:bus_driver/data/models/profile_models/profile_models.dart';
import 'package:bus_driver/data/models/trips_model/morning_trip_model/group_and_single_message_model.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import 'package:bus_driver/data/models/trips_model/morning_trip_model/start_morning_trip_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:geolocator/geolocator.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;

import '../../../config/config_base.dart';
import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../data/models/trips_model/morning_trip_model/absent_morning_trip_model.dart';
import '../../../data/models/trips_model/morning_trip_model/end_morning_trip_model.dart';
import '../../../data/models/trips_model/morning_trip_model/morning_trip_status_model.dart';
import '../../../data/models/trips_model/morning_trip_model/on_bus_morning_trip_model.dart';
import '../../../data/models/trips_model/morning_trip_model/present_on_bus_morning_trip_model.dart';
import '../../../data/models/trips_model/morning_trip_model/waiting_morning_trip_model.dart';
import '../../../data/repo/morning_trip_repo.dart';
import '../../../utils/get_it_injection.dart';
import '../../../utils/navigation_helper.dart';
import '../../../views/custom_widgets/custom_text.dart';

part 'morning_trip_state.dart';

class MorningTripCubit extends Cubit<MorningTripState> {
  MorningTripCubit() : super(MorningTripInitial());

  static MorningTripCubit get() =>
      BlocProvider.of(getIt<NavHelper>().navigatorKey.currentState!.context);
  final _morningTripRepo = MorningTripRepo();

  MorningTripStatusModel? morningTripStatusModel;
  StartMorningTripModel? startMorningTripModel;
  EndMorningTripModel? endMorningTripModel;
  WaitingMorningTripModel? waitingMorningTripModel;
  OnBusMorningTripModel? onBusMorningTripModel;
  PresentOnBusMorningTripModel? presentOnBusMorningTripModel;
  AbsentMorningTripModel? absentMorningTripModel;
  ProfileModels? profileModels;
  StreamSubscription<Position>? _positionStreamSubscription;
  GeoPoint? currentLocation;
  late io.Socket socket;
  bool isSocketInitialized = false;
  bool isLoadingMorningTrips = false;

  // int? userableId;

  Future<void> getMorningTripStatus() async {
    print("[MorningTrip] Getting trip status");
    emit(MorningTripStatusLoadingState());
    try {
      final response = await _morningTripRepo.morningTripStatusRepo();
      if (response.status == true) {
        print("[MorningTrip] Trip status successful: ${response.toString()}");
        MorningTripCubit.get().initSocket();
        MorningTripCubit.get().startLocationTracking();
        morningTripStatusModel = response;
        tripId = response.trip?.id;
        print("[MorningTrip] Trip ID: $tripId");
        getWaitingMorningTrip();
        getOnBusMorningTrip();
        getAbsentMorningTrip();
        emit(StartMorningTripSuccessState());
      } else {
        print("[MorningTrip] Trip status error: ${response.message}");
        waitingMorningTripModel?.waiting?.clear();
        onBusMorningTripModel?.presentOnBus?.clear();
        absentMorningTripModel?.absences?.clear();
        emit(MorningTripStatusErrorState(response.message));
      }
    } catch (e) {
      print("[MorningTrip] Trip status exception: $e");
      emit(MorningTripStatusErrorState(e.toString()));
    }
  }

  void startLocationTracking({bool? dataTrip}) {
    print("[MorningTrip] Starting location tracking, dataTrip: $dataTrip");
    _positionStreamSubscription = Geolocator.getPositionStream().listen(
      (Position position) async {
        currentLocation = GeoPoint(
            latitude: position.latitude, longitude: position.longitude);
        print(
            "[MorningTrip] New position: lat=${position.latitude}, long=${position.longitude}");
        if (isSocketInitialized) {
          sendLocationToSocket(openTripTracking: dataTrip);
        } else {
          print(
              "[MorningTrip] Socket not initialized yet, can't send location");
        }
      },
    );
  }

  initSocket() {
    print("[MorningTrip] Initializing socket connection");
    socket = io.io(ConfigBase.socketUrl, <String, dynamic>{
      'query': {
        'token': socketToken,
      },
      'transports': ['websocket'],
    });
    isSocketInitialized = true;
    socket.dispose();
    socket.open();
    socket.onConnect((_) {
      print("[MorningTrip] Socket connected successfully");
      socket.onDisconnect((_) {
        print("[MorningTrip] Socket disconnected");
        socket.onConnectError((err) {
          print("[MorningTrip] Socket connection error: $err");
          socket.onError((err) {
            print("[MorningTrip] Socket error: $err");
          });
        });
      });
    });
  }

  void sendLocationToSocket({required bool? openTripTracking}) {
    print(
        "[MorningTrip] Sending location to socket, openTripTracking: $openTripTracking");
    if (currentLocation != null) {
      final locationJson = jsonEncode({
        'latitude': currentLocation!.latitude.toString(),
        'longitude': currentLocation!.longitude.toString(),
        'type': type,
        'bus_id': busId,
        "trip_id": tripId,
        // 'userable_id': userableId,
      });

      print("[MorningTrip] Location data: $locationJson");
      print("[MorningTrip] Bus ID: $busId, Type: $type");

      final String? sendEvent = type;

      if (sendEvent == null) {
        print("[MorningTrip] Error: sendEvent (type) is null");
        throw ArgumentError("sendEvent (type) is null");
      }

      try {
        if (!socket.connected) {
          print("[MorningTrip] Socket not connected, attempting to connect");
          socket.onConnect((_) {
            print("[MorningTrip] Socket connected, emitting location");
            socket.emit(sendEvent, ['$busId', locationJson]);
          });
        } else {
          print("[MorningTrip] Socket already connected, emitting location");
          socket.emit(sendEvent, ['$busId', locationJson]);
        }
      } catch (e, stackTrace) {
        print("[MorningTrip] Socket error: $e");
        print(stackTrace);
      }
    } else {
      print("[MorningTrip] Current location is null, can't send to socket");
    }
  }

  void stopLocationTracking() {
    print("[MorningTrip] Stopping location tracking");
    _positionStreamSubscription?.cancel();
  }

  Future<void> startMorningTrip({
    String? latitude,
    String? longitude,
    // required bool notify,
  }) async {
    print("[MorningTrip] Starting morning trip");
    emit(StartMorningTripLoadingState());
    try {
      final response = await _morningTripRepo.startMorningTripRepo(
        latitude: latitude,
        longitude: longitude,
        // notify: notify
      );
      if (response.status == true) {
        print(
            "[MorningTrip] Morning trip started successfully: ${response.toString()}");
        startMorningTripModel = response;
        tripId = response.data?.trip?.id;
        print("[MorningTrip] Trip ID: $tripId");
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم بدأ الرحلة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        MorningTripCubit.get().startLocationTracking();
        getWaitingMorningTrip();
        getAbsentMorningTrip();
        emit(StartMorningTripSuccessState());
      } else {
        print("[MorningTrip] Morning trip start error: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.message,
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        emit(StartMorningTripErrorState());
      }
    } catch (e) {
      print("[MorningTrip] Morning trip start exception: $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "تعذر الوصول حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
      emit(StartMorningTripErrorState());
    }
  }

  Future<void> endMorningTrip({
    int? tripId,
  }) async {
    print("[MorningTrip] Ending morning trip");
    emit(EndMorningTripLoadingState());
    try {
      final response = await _morningTripRepo.endMorningTripRepo(
        tripId: tripId,
      );
      if (response.status == true) {
        print(
            "[MorningTrip] Morning trip ended successfully: ${response.toString()}");
        endMorningTripModel = response;
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم إنهاء الرحلة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        waitingMorningTripModel?.waiting?.clear();
        onBusMorningTripModel?.presentOnBus?.clear();
        absentMorningTripModel?.absences?.clear();
        emit(EndMorningTripSuccessState());
      } else {
        print("[MorningTrip] Morning trip end error: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "${response.message}، ارجع لقائمة الرحلات وحاول مجددا",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        emit(EndMorningTripErrorState());
      }
    } on DioException catch (e) {
      print("[MorningTrip] Morning trip end Dio exception: $e");
      String errorMessage = e.response?.data['message'] ?? 'Unknown error';
      SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: errorMessage,
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
      emit(EndMorningTripErrorState());
    } catch (e) {
      print("[MorningTrip] Morning trip end exception: $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "تعذر الوصول حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
      emit(EndMorningTripErrorState());
    }
  }

  Future<void> getWaitingMorningTrip() async {
    print("[MorningTrip] Getting waiting morning trip");
    emit(WaitingMorningTripLoadingState());
    try {
      final response = await _morningTripRepo.waitingMorningTripRepo();
      if (response.status == true) {
        print(
            "[MorningTrip] Waiting morning trip successful: ${response.toString()}");
        waitingMorningTripModel = response;
        emit(WaitingMorningTripSuccessState(response.waiting));
      } else {
        print("[MorningTrip] Waiting morning trip error: ${response.message}");
        emit(WaitingMorningTripErrorState(error: response.message));
      }
    } catch (e) {
      print("[MorningTrip] Waiting morning trip exception: $e");
      emit(WaitingMorningTripErrorState(error: e.toString()));
    }
  }

  Future<void> getOnBusMorningTrip() async {
    print("[MorningTrip] Getting on bus morning trip");
    emit(OnBusMorningTripLoadingState());
    try {
      final response = await _morningTripRepo.onBusMorningTripRepo();
      if (response.errors == false) {
        print(
            "[MorningTrip] On bus morning trip successful: ${response.toString()}");
        onBusMorningTripModel = response;
        emit(OnBusMorningTripSuccessState(response.presentOnBus));
      } else {
        print("[MorningTrip] On bus morning trip error: ${response.message}");
        emit(OnBusMorningTripErrorState(error: response.message));
      }
    } catch (e) {
      print("[MorningTrip] On bus morning trip exception: $e");
      emit(OnBusMorningTripErrorState(error: e.toString()));
    }
  }

  Future<void> removePresentOnBusMorningTrip({
    String? studentId,
  }) async {
    print("[MorningTrip] Removing present on bus morning trip");
    emit(OnBusMorningTripLoadingState());
    try {
      isLoadingMorningTrips = true;
      final response = await _morningTripRepo.removePresentOnBusMorningTripRepo(
        studentId: studentId,
      );
      if (response.status == true) {
        print(
            "[MorningTrip] Present on bus morning trip removed successfully: ${response.toString()}");
        onBusMorningTripModel?.presentOnBus
            ?.removeWhere((element) => element.id == studentId);
        isLoadingMorningTrips = false;
        emit(OnBusMorningTripSuccessState(onBusMorningTripModel?.presentOnBus));
        getWaitingMorningTrip();
      } else {
        isLoadingMorningTrips = false;
      }
    } catch (e) {
      isLoadingMorningTrips = false;
    }
  }

  Future<void> presentOnBusMorningTrip({
    String? studentId,
  }) async {
    print("[MorningTrip] Present on bus morning trip");
    isLoadingMorningTrips = true;
    emit(PresentOnBusMorningTripLoadingState());
    try {
      final response = await _morningTripRepo.presentOnBusMorningTripRepo(
        studentId: studentId,
      );
      if (response.status == true) {
        print(
            "[MorningTrip] Present on bus morning trip successful: ${response.toString()}");
        waitingMorningTripModel?.waiting
            ?.removeWhere((element) => element.id == studentId);
        presentOnBusMorningTripModel = response;
        getOnBusMorningTrip();
        isLoadingMorningTrips = false;
        emit(WaitingMorningTripSuccessState(waitingMorningTripModel?.waiting));
      } else {
        isLoadingMorningTrips = false;
      }
    } catch (e) {
      isLoadingMorningTrips = false;
    }
  }

  Future<void> getAbsentMorningTrip() async {
    print("[MorningTrip] Getting absent morning trip");
    try {
      final response = await _morningTripRepo.absentMorningTripRepo();
      if (response.errors == false) {
        print(
            "[MorningTrip] Absent morning trip successful: ${response.toString()}");
        absentMorningTripModel = response;
        emit(AbsentMorningTripSuccessState(response.absences));
      } else {}
    } catch (e) {
      print("[MorningTrip] Absent morning trip exception: $e");
    }
  }

  Future<void> removeAbsenceMorningTrip({
    String? studentId,
  }) async {
    print("[MorningTrip] Removing absence morning trip");
    emit(AbsentMorningTripLoadingState());
    try {
      isLoadingMorningTrips = true;
      final response = await _morningTripRepo.removeAbsenceMorningTripRepo(
        studentId: studentId,
      );
      if (response.status == true) {
        print(
            "[MorningTrip] Absence morning trip removed successfully: ${response.toString()}");
        absentMorningTripModel?.absences
            ?.removeWhere((element) => element.id == studentId);
        getWaitingMorningTrip();
        isLoadingMorningTrips = false;
        emit(AbsentMorningTripSuccessState(absentMorningTripModel?.absences));
      } else {
        isLoadingMorningTrips = false;
      }
    } catch (e) {
      isLoadingMorningTrips = false;
    }
  }

  Future<void> studentAbsentMorningTrip({
    String? studentId,
  }) async {
    print("[MorningTrip] Student absent morning trip");
    isLoadingMorningTrips = true;
    emit(StudentAbsentMorningTripLoadingState());
    try {
      final response = await _morningTripRepo.studentAbsentMorningTripRepo(
        studentId: studentId,
      );
      if (response.errors == false) {
        print(
            "[MorningTrip] Student absent morning trip successful: ${response.toString()}");
        waitingMorningTripModel?.waiting
            ?.removeWhere((element) => element.id == studentId);
        isLoadingMorningTrips = false;
        emit(WaitingMorningTripSuccessState(waitingMorningTripModel?.waiting));
        getAbsentMorningTrip();
      } else {
        isLoadingMorningTrips = false;
      }
    } catch (e) {
      isLoadingMorningTrips = false;
    }
  }

  Future<void> sendMessageMorningTrip({
    String? studentId,
    int? messageId,
    // required String notificationsType,
  }) async {
    print("[MorningTrip] Sending message morning trip");
    try {
      final response = await _morningTripRepo.sendMessagemorningTripRepo(
          messageId: messageId, studentId: studentId);

      if (response.errors == false) {
        print(
            "[MorningTrip] Message sent successfully: ${response.toString()}");
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم إرسال الرسالة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        print("[MorningTrip] Message send error: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.message ?? "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e) {
      print("[MorningTrip] Message send exception: $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  Future<void> sendMessageForAllMorningTrip({
    required int staticMessageId,
    // required String? notificationType,
    // required String? message_en,
  }) async {
    print("[MorningTrip] Sending message for all morning trip");
    try {
      final response = await _morningTripRepo.sendMessageForAllMorningTripRepo(
        staticMessageId: staticMessageId,
        // message_en: message_en,
        // notificationType: notificationType,
      );
      if (response.errors == false) {
        print(
            "[MorningTrip] Message sent to all successfully: ${response.toString()}");
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "تم إرسال الرسالة بنجاح",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        print("[MorningTrip] Message send to all error: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.message ?? "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e) {
      print("[MorningTrip] Message send to all exception: $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  GroupAndSingleMessageModel? groupAndSingleMessageModel;

  Future<void> groupOrSingleMessage({required String type_day}) async {
    print("[MorningTrip] Group or single message");
    try {
      final response =
          await _morningTripRepo.groupOrSingleMessage(type_day: type_day);
      if (response.data != null) {
        print(
            "[MorningTrip] Group or single message successful: ${response.toString()}");
        groupAndSingleMessageModel = response;
      }
    } catch (e) {
      print("[MorningTrip] Group or single message exception: $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }
}
