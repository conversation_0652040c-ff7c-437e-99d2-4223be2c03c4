part of 'morning_trip_cubit.dart';

abstract class MorningTripState extends Equatable {}

class MorningTripInitial extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class MorningTripStatusLoadingState extends MorningTripState{
  @override
  List<Object> get props => [];
}

class MorningTripStatusErrorState extends MorningTripState{
  final String? error;
  MorningTripStatusErrorState(this.error);
  @override
  List<Object?> get props => [error];
}


class StartMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class StartMorningTripSuccessState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class StartMorningTripErrorState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}


class EndMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class EndMorningTripSuccessState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class EndMorningTripErrorState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

//GroupOrSingleMessageLoaging



// class GroupOrSingleMessageLoading extends MorningTripState {
//   @override
//   List<Object?> get props =>[];
// }

// class GroupOrSingleMessageSuccess extends MorningTripState {
//   @override
//   List<Object?> get props =>[];
// }

// class GroupOrSingleMessageError extends MorningTripState {
//   @override
//   List<Object?> get props =>[];
// }





class WaitingMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class WaitingMorningTripSuccessState extends MorningTripState {
  final List<Waiting>? waiting;
  WaitingMorningTripSuccessState(this.waiting);
  @override
  List<Object?> get props =>[waiting?.length];
}

class WaitingMorningTripErrorState extends MorningTripState {
  final String? error;
  WaitingMorningTripErrorState({this.error});
  @override
  List<Object?> get props =>[error];
}


class OnBusMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class OnBusMorningTripSuccessState extends MorningTripState {
  final List<PresentOnBus>? presentOnBus;
  OnBusMorningTripSuccessState(this.presentOnBus);
  @override
  List<Object?> get props =>[presentOnBus?.length];
}

class OnBusMorningTripErrorState extends MorningTripState {
  final String? error;
  OnBusMorningTripErrorState({this.error});
  @override
  List<Object?> get props =>[error];
}


class PresentOnBusMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class AbsentMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}

class AbsentMorningTripSuccessState extends MorningTripState {
  final List<Absence>? absence;
  AbsentMorningTripSuccessState(this.absence);
  @override
  List<Object?> get props =>[absence?.length];
}


class StudentAbsentMorningTripLoadingState extends MorningTripState {
  @override
  List<Object?> get props =>[];
}