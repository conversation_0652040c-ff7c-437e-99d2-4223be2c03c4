import 'dart:developer';

import 'package:bloc/bloc.dart';

import '../../../data/models/my_bus_model.dart';
import '../../../data/repo/my_bus_repo.dart';

part 'my_bus_state.dart';

class MyBusCubit extends Cubit<MyBusState> {
  final _myBusRepo = MyBusRepo();
  MyBusCubit() : super(MyBusInitial());

  Future<void> getMyBus() async {
    emit(MyBusLoadingState());
    try {
      final response = await _myBusRepo.repo();
      if (response.status == true) {
        log(response.data!.toString());

        emit(MyBusSuccessState(myBusModel: response));
      } else {
        emit(MyBusErrorState(error: response.message));
      }
    } catch (e , stackTrace) {
      print(stackTrace);
      print("catch error $e");
      emit(MyBusErrorState(error: e.toString()));
    }
  }
}
