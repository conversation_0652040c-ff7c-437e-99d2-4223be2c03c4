import 'package:bus_driver/data/models/notifications_models/fcm_token_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/notifications_models/notifications_model.dart';
import '../../../data/repo/notifications_repo.dart';

part 'notifications_state.dart';

// NotificationsCubit get notificationCubitProvider =>
//     NotificationsCubit.get(getIt<NavHelper>().navigatorKey.currentContext);

class NotificationsCubit extends Cubit<NotificationsState> {
  NotificationsCubit() : super(NotificationsInitial());
  static NotificationsCubit get(context) => BlocProvider.of(context);
  final _notificationsRepo = NotificationsRepo();

  FcmTokenModel? allParentsFcmTokens;
  FcmTokenModel? parentFcmToken;
  AllNotificationsModel1? notifications;
  List<NotificationAllData>? notificationData = [];
  int _page = 1;
  int? last_page;
  int? total;
  int? currentPage;
  bool? hasMoreData = false;

  void initScrollController({
    bool? isFirst = false,
    ScrollController? scrollController,
    Function()? setStates,
  }) {
    if (scrollController == null) return;

    scrollController.addListener(() {
      if (scrollController.position.maxScrollExtent ==
          scrollController.offset) {
        if (currentPage != null &&
            last_page != null &&
            currentPage! < last_page!) {
          hasMoreData = true;
          _page++;
          getNotifications(
            page: _page,
            isFirst: false,
          );
          setStates?.call();
        } else {
          hasMoreData = false;
          setStates?.call();
        }
      }
    });
  }

  Future<void> getNotifications({
    int? page,
    bool? isFirst = false,
  }) async {
    emit(NotificationsLoading());
    if (isFirst == true) {
      notificationData = [];
      _page = 1;
    }
    try {
      final response = await _notificationsRepo.notifications(page: page);
      if (response.data != null) {
        debugPrint(response.data.toString());
        notifications = response;
        // total = response.data!.total;
        // currentPage = response.data!.currentPage;
        // last_page = response.data!.lastPage;
        notificationData!.addAll(response.data!);
        emit(NotificationsSuccess());
      } else {
        debugPrint(
            "ErrorState Errorrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr");
        emit(NotificationsError());
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      emit(NotificationsError());
    }
  }

  // Future<void> getAllParentsFcmTokens() async {
  //   emit(NotificationsLoading());
  //   try {
  //     final response = await _notificationsRepo.allParentsFcmTokens();
  //     if (response.status == true) {
  //       debugPrint(response.data.toString());
  //       allParentsFcmTokens = response;
  //       Logger().w(allParentsFcmTokens);
  //       emit(NotificationsSuccess());
  //     } else {
  //       debugPrint("ErrorState: ${response.message}");
  //       emit(NotificationsError());
  //     }
  //   } catch (e , stackTrace) {
  //     debugPrint("getAllParentsFcmTokens catch error $e");
  //     emit(NotificationsError());
  //   }
  // }

  // Future<void> getParentFcmToken({int? studentId}) async {
  //   emit(NotificationsLoading());
  //   parentFcmToken = null;
  //   try {
  //     final response =
  //         await _notificationsRepo.parentFcmTokens(studentId: studentId);
  //     if (response.status == true) {
  //       debugPrint(response.data.toString());
  //       parentFcmToken = response;
  //       emit(NotificationsSuccess());
  //     } else {
  //       debugPrint("ErrorState: ${response.message}");
  //       emit(NotificationsError());
  //     }
  //   } catch (e , stackTrace) {
  //     debugPrint("getParentFcmToken catch error $e");
  //     emit(NotificationsError());
  //   }
  // }
}
