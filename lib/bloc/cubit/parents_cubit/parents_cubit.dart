import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:bus_driver/data/repo/parents_repo.dart';
import 'package:meta/meta.dart';

import '../../../data/models/parents_model.dart';

part 'parents_state.dart';

class ParentsCubit extends Cubit<ParentsState> {
  final _parentsRepo = ParentsRepo();

  ParentsCubit() : super(ParentsInitial());

  Future<void> getParents({String? id}) async {
    emit(ParentsLoadingState());
    try {
      final response = await _parentsRepo.repo(id: id);
      if (response.status == true) {
        log(response.data!.toString());

        emit(ParentsSuccessState(parentsModel: response));
      } else {
        emit(ParentsErrorState(error: response.message));
      }
    } catch (e , stackTrace) {
      print(stackTrace);
      print("catch error $e");
      emit(ParentsErrorState(error: e.toString()));
    }
  }
}
