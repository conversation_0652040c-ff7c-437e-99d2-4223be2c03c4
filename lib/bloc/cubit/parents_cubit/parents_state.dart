part of 'parents_cubit.dart';

@immutable
abstract class ParentsState {}

class ParentsInitial extends ParentsState {}

class ParentsLoadingState extends ParentsState {}

class ParentsSuccessState extends ParentsState {
  final ParentsModel? parentsModel;
  ParentsSuccessState({this.parentsModel});
}

class ParentsErrorState extends ParentsState {
  final String? error;
  ParentsErrorState({this.error});
}
