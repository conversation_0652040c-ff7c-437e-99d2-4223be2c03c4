import 'package:bus_driver/bloc/cubit/profile_cubit/profile_states.dart';
import 'package:bus_driver/data/repo/profile_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProfileCubit extends Cubit<ProfileStates> {
  ProfileCubit() : super(ProfileInitialStates());
  final _profileRepo = ProfileRepo();

  static ProfileCubit get(context) => BlocProvider.of(context);

  Future<void> getProfile() async {
    emit(ProfileLoadingStates());
    try {
      final response = await _profileRepo.repo();
      if (response.status != null) {
        emit(ProfileSuccessStates(profileModels: response));
      } else {
        emit(ProfileErrorStates(error: response.errors));
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      print("catch error at cubits$e");
      emit(ProfileErrorStates(error: e.toString()));
    }
  }
}
