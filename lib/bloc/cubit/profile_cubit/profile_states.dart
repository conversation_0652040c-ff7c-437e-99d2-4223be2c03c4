import 'package:bus_driver/data/models/profile_models/profile_models.dart';

abstract class ProfileStates {}

class ProfileInitialStates extends ProfileStates {}

class ProfileLoadingStates extends ProfileStates {}

class ProfileSuccessStates extends ProfileStates {
  final ProfileModels? profileModels;
  ProfileSuccessStates({
    this.profileModels,
  });
}

class ProfileErrorStates extends ProfileStates {
  final String? error;
  ProfileErrorStates({this.error});
}
