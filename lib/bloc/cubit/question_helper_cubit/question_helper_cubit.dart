import 'package:bus_driver/bloc/cubit/question_helper_cubit/question_helper_states.dart';
import 'package:bus_driver/data/repo/question_help_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class QuestionHelperCubit extends Cubit<QuestionHelperStates> {
  QuestionHelperCubit() : super(QuestionHelperInitialStates());
  final _questionHelperRepo = QuestionHelperRepo();

  static QuestionHelperCubit get(context) => BlocProvider.of(context);

  Future<void> getQuestionHelper() async {
    emit(QuestionHelperLoadingStates());
    try {
      final response = await _questionHelperRepo.repo();
      if (response.status == true) {
        emit(QuestionHelperSuccessStates(questionHelp: response));
      } else {
        emit(QuestionHelperErrorStates(error: response.message));
      }
    } catch (e) {
      emit(QuestionHelperErrorStates(error: e.toString()));
    }
  }
}
