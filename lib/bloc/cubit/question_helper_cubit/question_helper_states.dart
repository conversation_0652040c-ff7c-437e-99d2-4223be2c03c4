

import 'package:bus_driver/data/models/question_help_models/question_help_models.dart';

abstract class QuestionHelperStates {}

class QuestionH<PERSON>perInitialStates extends QuestionHelperStates {}

class QuestionHelperLoadingStates extends QuestionHelperStates {}

class QuestionHelperSuccessStates extends QuestionHelperStates {
  final QuestionHelp? questionHelp;
  QuestionHelperSuccessStates({
    this.questionHelp,
  });
}

class QuestionHelperErrorStates extends QuestionHelperStates {
  final String? error;
  QuestionHelperErrorStates({
    this.error,
  });
}
