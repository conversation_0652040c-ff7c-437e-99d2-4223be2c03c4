import 'package:bus_driver/bloc/cubit/students_cubit/students_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/repo/students_repo.dart';

class StudentsCubit extends Cubit<StudentsState> {
  final _studentsRepo = StudentsRepo();

  StudentsCubit() : super(StudentsInitialState());

  Future<void> getBusStudents() async {
    emit(StudentsLoadingState());
    try {
      final response = await _studentsRepo.repo();
      if (response.status == true) {
        emit(StudentsSuccessState(studentsModel: response));
      } else {
        emit(StudentsErrorState(error: response.message));
      }
    } catch (e, stackTrace) {
      print(stackTrace);
      print("catch error $e");
      emit(StudentsErrorState(error: e.toString()));
    }
  }
}
