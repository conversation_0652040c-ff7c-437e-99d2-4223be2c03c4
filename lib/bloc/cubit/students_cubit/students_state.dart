import '../../../data/models/students_model.dart';

abstract class StudentsState {}

class StudentsInitialState extends StudentsState {}

class StudentsLoadingState extends StudentsState {}

class StudentsSuccessState extends StudentsState {
  final StudentsModel? studentsModel;
  StudentsSuccessState({this.studentsModel});
}

class StudentsErrorState extends StudentsState {
  final String? error;
  StudentsErrorState({this.error});
}