import 'package:bus_driver/bloc/cubit/trip_evening_cubit/trip_evening_states.dart';
import 'package:bus_driver/data/repo/current_trip_evening_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TripEveningCubit extends Cubit<TripEveningStates> {
  TripEveningCubit() : super(TripEveningInitialStates());
  final _currentTripRepo = CurrentTripEveningRepo();

  static TripEveningCubit get(context) => BlocProvider.of(context);

  Future<void> getCurrentEvening() async {
    emit(TripEveningLoadingStates());
    try {
      final response = await _currentTripRepo.repo();
      if (response.status == true) {
        emit(TripEveningSuccessStates(tripOpenModels: response));
      } else {
        emit(TripEveningErrorStates(error: response.message));
      }
    } catch (e) {
      emit(TripEveningErrorStates(error: e.toString()));
    }
  }
}
