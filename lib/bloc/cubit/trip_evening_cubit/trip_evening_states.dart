import 'package:bus_driver/data/models/trip_open_models/trip_open_models.dart';

abstract class TripEveningStates {}

class TripEveningInitialStates extends TripEveningStates {}

class TripEveningLoadingStates extends TripEveningStates {}

class TripEveningSuccessStates extends TripEveningStates {
  final TripOpenModels? tripOpenModels;
  TripEveningSuccessStates({
    this.tripOpenModels,
  });
}

class TripEveningErrorStates extends TripEveningStates {
  final String? error;
  TripEveningErrorStates({
    this.error,
  });
}
