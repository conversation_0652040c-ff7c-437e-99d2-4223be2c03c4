import 'package:bus_driver/bloc/cubit/trip_morning_cubit/trip_morning_states.dart';
import 'package:bus_driver/data/repo/current_trip_morning_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TripMorningCubit extends Cubit<TripMorningStates> {
  TripMorningCubit() : super(TripMorningInitialStates());
  final _currentTripRepo = CurrentTripMorningRepo();

  static TripMorningCubit get(context) => BlocProvider.of(context);

  Future<void> getCurrentMorning() async {
    emit(TripMorningLoadingStates());
    try {
      final response = await _currentTripRepo.repo();
      if (response.status == true) {
        emit(TripMorningSuccessStates(tripOpenModels: response));
      } else {
        emit(TripMorningErrorStates(error: response.message));
      }
    } catch (e , stackTrace) {
      print(stackTrace);
      print("catch error at cubits$e");
      emit(TripMorningErrorStates(error: e.toString()));
    }
  }
}
