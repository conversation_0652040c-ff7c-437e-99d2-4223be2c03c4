import 'package:bus_driver/data/models/trip_open_models/trip_open_models.dart';

abstract class TripMorningStates {}

class TripMorningInitialStates extends TripMorningStates {}

class TripMorningLoadingStates extends TripMorningStates {}

class TripMorningSuccessStates extends TripMorningStates {
  final TripOpenModels? tripOpenModels;
  TripMorningSuccessStates({
    this.tripOpenModels,
  });
}

class TripMorningErrorStates extends TripMorningStates {
  final String? error;
  TripMorningErrorStates({
    this.error,
  });
}
