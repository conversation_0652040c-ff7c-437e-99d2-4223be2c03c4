/// تكوين نظام فحص حالة التحديث
class AppUpdateConfig {
  /// اسم التطبيق الافتراضي
  static const String defaultAppName = 'busaty-attendants';
  
  /// نوع التطبيق الافتراضي
  static const String defaultAppType = 'attendants';
  
  /// فترة انتظار الطلب (بالثواني)
  static const int requestTimeoutSeconds = 10;
  
  /// فترة إعادة المحاولة التلقائية (بالثواني)
  static const int retryIntervalSeconds = 30;
  
  /// عدد محاولات إعادة الطلب
  static const int maxRetryAttempts = 3;
  
  /// رسائل الخطأ المخصصة
  static const Map<String, String> errorMessages = {
    'network_error': 'خطأ في الاتصال بالشبكة',
    'timeout_error': 'انتهت مهلة الاتصال',
    'server_error': 'خطأ في الخادم',
    'unknown_error': 'حدث خطأ غير معروف',
    'invalid_app_name': 'اسم التطبيق غير صحيح',
    'app_not_found': 'التطبيق غير موجود',
  };
  
  /// رسائل الصيانة المخصصة
  static const Map<String, String> maintenanceMessages = {
    'default': 'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
    'scheduled': 'صيانة مجدولة للتطبيق. سيعود التطبيق للعمل قريباً.',
    'emergency': 'صيانة طارئة للتطبيق. نعتذر عن الإزعاج.',
    'update': 'يتم تحديث التطبيق الآن. يرجى الانتظار.',
  };
  
  /// إعدادات واجهة المستخدم
  static const Map<String, dynamic> uiSettings = {
    'show_retry_button': true,
    'show_progress_indicator': true,
    'auto_retry_enabled': false,
    'maintenance_screen_theme': 'light',
  };
  
  /// URLs للأنواع المختلفة من التطبيقات
  static const Map<String, String> appTypeUrls = {
    'attendants': 'attendants/app/updating',
    'parents': 'parents/app/updating',
    'schools': 'schools/app/updating',
  };
  
  /// أسماء التطبيقات المدعومة
  static const List<String> supportedAppNames = [
    'busaty-attendants',
    'busaty-parents',
    'busaty-schools',
  ];
  
  /// إعدادات التخزين المؤقت
  static const Map<String, dynamic> cacheSettings = {
    'cache_duration_minutes': 5,
    'cache_enabled': true,
    'cache_key_prefix': 'app_update_status_',
  };
  
  /// إعدادات السجلات
  static const Map<String, dynamic> loggingSettings = {
    'log_requests': true,
    'log_responses': true,
    'log_errors': true,
    'log_level': 'info', // debug, info, warning, error
  };
  
  /// الحصول على URL للنوع المحدد
  static String getUrlForAppType(String appType) {
    return appTypeUrls[appType] ?? appTypeUrls['attendants']!;
  }
  
  /// التحقق من صحة اسم التطبيق
  static bool isValidAppName(String appName) {
    return supportedAppNames.contains(appName);
  }
  
  /// الحصول على رسالة خطأ مخصصة
  static String getErrorMessage(String errorType) {
    return errorMessages[errorType] ?? errorMessages['unknown_error']!;
  }
  
  /// الحصول على رسالة صيانة مخصصة
  static String getMaintenanceMessage(String messageType) {
    return maintenanceMessages[messageType] ?? maintenanceMessages['default']!;
  }
  
  /// إعدادات البيئة
  static const Map<String, String> environmentSettings = {
    'development': 'https://test.busatyapp.com/api/',
    'staging': 'https://stage.busatyapp.com/api/',
    'production': 'https://api.busatyapp.com/api/',
  };
  
  /// الحصول على URL الأساسي حسب البيئة
  static String getBaseUrlForEnvironment(String environment) {
    return environmentSettings[environment] ?? environmentSettings['development']!;
  }
}
