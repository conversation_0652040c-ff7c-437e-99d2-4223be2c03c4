import 'package:bus_driver/helper/cache_helper.dart';
import 'package:flutter/material.dart';

String? token;
String? fCMToken;
String? name;
String? type;
int? busId;
int? id;
int? tripId;
  int? userableId;

String? username;
String? userImageUrl;
String? userAddress;
String? userPhone;
// const String socketToken = "****************************************";
const String socketToken = "71e456b873f87f214f139799878b911a";
const String firebaseServerKey = "AAAAD2sLMEM:APA91bHKGl6SMCq8kG9YMf0TZV5L_h13xBlc2iGDwcY8pqUPy6mh5HF4Q1ctRYBQP9lOrZfcW4gL-Ii0sMIl1BmoYizP2x5LMKJgN6ghjRq9sO5rf46tpNG_6zDOd_EXm6qUxR_NljXo";
bool isChecked = false;

final GlobalKey<ScaffoldMessengerState> snackBarKey =
GlobalKey<ScaffoldMessengerState>();
void initializeFCMToken() async {
  fCMToken = CacheHelper.getString( "fcmToken");
  debugPrint("---------------------------------------------------------------");

  debugPrint("fcmToken: $fCMToken");
  debugPrint("---------------------------------------------------------------");
}