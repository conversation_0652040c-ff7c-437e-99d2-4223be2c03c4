import 'package:bus_driver/views/screens/bus_data_screen/bus_data_screen.dart';
import 'package:bus_driver/views/screens/change_password_screen/change_password_screen.dart';
import 'package:bus_driver/views/screens/contact_us_screen/contact_us.dart';
import 'package:bus_driver/views/screens/done_screen/done_screen.dart';
import 'package:bus_driver/views/screens/forget_password_screen/forget_password_screen.dart';
import 'package:bus_driver/views/screens/help_screen/help_screen.dart';
import 'package:bus_driver/views/screens/home_screen/home.dart';
import 'package:bus_driver/views/screens/languages_screen/languages_screen.dart';
import 'package:bus_driver/views/screens/login_screen/login_screen.dart';
import 'package:bus_driver/views/screens/new_password_screen/new_password_screen.dart';
import 'package:bus_driver/views/screens/send_code_screen/send_code_screen.dart';
import 'package:bus_driver/views/screens/setting_screen/setting_screen.dart';
import 'package:bus_driver/views/screens/student_data_screen/student_data_screen.dart';
import 'package:bus_driver/views/screens/update_profile_screen/update_profile_screen.dart';
import 'package:bus_driver/widgets/open_street_map_widget.dart';
import 'package:flutter/material.dart';

import '../views/screens/languages_screen/on_boarding_language_screen.dart';
import '../views/screens/notifications_screen/notifications_screen.dart';
import '../views/screens/parents_screen/parents_screen.dart';
import '../views/screens/student_address_screen/student_address_screen.dart';
import '../views/screens/student_bus_screen/student_bus_screen.dart';
import '../views/screens/trips_screens/evening_trip/evening_trip_screen.dart';
import '../views/screens/trips_screens/morning_trip/morning_trip_screen.dart';
import '../views/screens/trips_screens/trips_screen.dart';

Map<String, Widget Function(BuildContext)> routes = {
  OnBoardingLanguageScreen.routeName: (context) => const OnBoardingLanguageScreen(),
  HomeScreen.routeName: (context) => const HomeScreen(),
  LoginScreen.routeName: (context) => const LoginScreen(),
  DoneScreen.routeName: (context) => const DoneScreen(),
  ChangePasswordScreen.routeName: (context) => const ChangePasswordScreen(),
  SendCodeScreen.routeName: (context) => SendCodeScreen(),
  NewPasswordScreen.routeName: (context) => const NewPasswordScreen(),
  ForgetPasswordScreen.routeName: (context) => const ForgetPasswordScreen(),
  TripsScreen.routeName: (context) => const TripsScreen(),
  BusDataScreen.routeName: (context) => const BusDataScreen(),
  ContactUsScreen.routeName: (context) => const ContactUsScreen(),
  StudentDataScreen.routeName: (context) => const StudentDataScreen(),
  UpdateProfileScreen.routeName: (context) => const UpdateProfileScreen(),
  // ProfileScreen.routeName: (context) => const ProfileScreen(),
  LanguagesScreen.routeName: (context) => const LanguagesScreen(),
  HelpScreen.routeName: (context) => const HelpScreen(),
  SettingScreen.routeName: (context) => const SettingScreen(),
  StudentBusScreen.routeName: (context) => const StudentBusScreen(),
  MorningTripScreen.routeName: (context) => const MorningTripScreen(),
  EveningTripScreen.routeName: (context) => const EveningTripScreen(),
  OpenStreetMapWidget.routeName: (context) => const OpenStreetMapWidget(),
  ParentsScreen.routeName: (context) => const ParentsScreen(),
  StudentAddressScreen.routeName: (context) => const StudentAddressScreen(),
  NotificationsScreen.routeName: (context) => const NotificationsScreen(),
};
