import 'dart:ui';

const int xff = 0xff;

class TColor {
  static const Color primary = Color(0xff4A5CD0);
  static const Color text = Color(0xff2C2828);
  static const Color grey5 = Color(0xffB7BBD2);
  static const Color textForm = Color(0xff808080);
  static const Color underInput = Color(0xffA0A0A0);
  static const Color white = Color(0xffffffff);
  static const Color black = Color(0xff000000);
  static const Color fillFormField = Color(0xffF1F1F3);
  static const Color tabColors = Color(0xff707070);
  static const Color greenSuccess = Color(0xff1FDE82);
  static const Color redAccent = Color(0xffFF5564);
  static const Color darkRed = Color(0xffd95b65);
  static const Color titleSub = Color(0xff2C2828);
  static const Color error = Color(0xffFF0000);
  static const Color mainColor = Color(0xff6f69cf);
  static const Color fillFormFieldB = Color(0xffF5F5F5);
  static const Color iconInputColor = Color(0xff9AA0A2);
  static const Color textLogin = Color(0xff717171);
  static const Color borderContainer = Color(0xff7771D2);
  static const Color borderAvatar = Color(0xff8B86D8);
  static const Color titleColor = Color(0xff707070);
  static const Color subTitle = Color(0xffC8C8C8);
  static const Color backgroundContainer = Color(0xff6059CB);
  static const Color dialogName = Color(0xff9FA1A8);
  static const Color namePersonal = Color(0xff747474);
  static const Color waitRequest = Color(0xffFFA200);
  static const Color refusalRequest = Color(0xffA6A2AD);
  static const Color  newRequest = Color(0xff4454C6);
  static const Color  acceptRequest = Color(0xff0DC400);
  static const Color inputRequestColor = Color(0xffDFDFDF);
  static const Color titleReasonColor = Color(0xffAEAFB5);
}
