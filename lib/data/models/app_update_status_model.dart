import 'dart:convert';

class AppUpdateStatusModel {
  final bool? success;
  final String? name;
  final bool? isUpdating;
  final String? message;

  AppUpdateStatusModel({
    this.success,
    this.name,
    this.isUpdating,
    this.message,
  });

  AppUpdateStatusModel copyWith({
    bool? success,
    String? name,
    bool? isUpdating,
    String? message,
  }) =>
      AppUpdateStatusModel(
        success: success ?? this.success,
        name: name ?? this.name,
        isUpdating: isUpdating ?? this.isUpdating,
        message: message ?? this.message,
      );

  factory AppUpdateStatusModel.fromRawJson(String str) => 
      AppUpdateStatusModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AppUpdateStatusModel.fromJson(Map<String, dynamic> json) => 
      AppUpdateStatusModel(
        success: json["success"],
        name: json["name"],
        isUpdating: json["is_updating"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "name": name,
        "is_updating": isUpdating,
        "message": message,
      };

  @override
  String toString() {
    return 'AppUpdateStatusModel(success: $success, name: $name, isUpdating: $isUpdating, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppUpdateStatusModel &&
        other.success == success &&
        other.name == name &&
        other.isUpdating == isUpdating &&
        other.message == message;
  }

  @override
  int get hashCode {
    return success.hashCode ^
        name.hashCode ^
        isUpdating.hashCode ^
        message.hashCode;
  }
}
