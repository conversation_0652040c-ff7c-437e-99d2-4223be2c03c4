import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'change_password_status_models.dart';

part 'change_password_models.g.dart';

@JsonSerializable()
class ChangePasswordModels extends Equatable {
  final String? data;
  final ChangePasswordStatusModels? changePasswordStatusModels;

  const ChangePasswordModels({
    this.data,
    this.changePasswordStatusModels,
  });

  factory ChangePasswordModels.fromJson(Map<String, dynamic> json) {
    return _$ChangePasswordModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ChangePasswordModelsToJson(this);

  @override
  List<Object?> get props => [
        data,
        changePasswordStatusModels,
      ];
}
