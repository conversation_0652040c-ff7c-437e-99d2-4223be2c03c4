import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'change_password_status_models.g.dart';

@JsonSerializable()
class ChangePasswordStatusModels extends Equatable {
  final int? status;
  final String? messages;
  final int? server_titm;

  const ChangePasswordStatusModels({
    this.status,
    this.server_titm,
    this.messages,
  });

  factory ChangePasswordStatusModels.fromJson(Map<String, dynamic> json) {
    return _$ChangePasswordStatusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ChangePasswordStatusModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        server_titm,
        messages,
      ];
}
