import 'dart:convert';
MyBusModel myBusModelFromJson(String str) => MyBusModel.fromJson(json.decode(str));
String myBusModelToJson(MyBusModel data) => json.encode(data.toJson());
class MyBusModel {
  MyBusModel({
      this.data, 
      this.message, 
      this.status,});

  MyBusModel.fromJson(dynamic json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }
  Data? data;
  String? message;
  bool? status;
MyBusModel copyWith({  Data? data,
  String? message,
  bool? status,
}) => MyBusModel(  data: data ?? this.data,
  message: message ?? this.message,
  status: status ?? this.status,
);
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (data != null) {
      map['data'] = data?.toJson();
    }
    map['message'] = message;
    map['status'] = status;
    return map;
  }

}

Data dataFromJson(String str) => Data.fromJson(json.decode(str));
String dataToJson(Data data) => json.encode(data.toJson());
class Data {
  Data({
      this.bus, 
      this.studentsCount,});

  Data.fromJson(dynamic json) {
    bus = json['bus'] != null ? Bus.fromJson(json['bus']) : null;
    studentsCount = json['students_count'];
  }
  Bus? bus;
  int? studentsCount;
Data copyWith({  Bus? bus,
  int? studentsCount,
}) => Data(  bus: bus ?? this.bus,
  studentsCount: studentsCount ?? this.studentsCount,
);
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (bus != null) {
      map['bus'] = bus?.toJson();
    }
    map['students_count'] = studentsCount;
    return map;
  }

}

Bus busFromJson(String str) => Bus.fromJson(json.decode(str));
String busToJson(Bus data) => json.encode(data.toJson());
class Bus {
  Bus({
      this.id, 
      this.name, 
      this.notes, 
      this.status, 
      this.schoolId, 
      this.carNumber, 
      this.createdAt, 
      this.updatedAt, 
      this.driver, 
      this.admin, 
      this.schools, 
      this.students,});

  Bus.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    notes = json['notes'];
    status = json['status'];
    schoolId = json['school_id'];
    carNumber = json['car_number'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    driver = json['driver'];
    admin = json['admin'] != null ? Admin.fromJson(json['admin']) : null;
    schools = json['schools'] != null ? Schools.fromJson(json['schools']) : null;
    if (json['students'] != null) {
      students = [];
      json['students'].forEach((v) {
        students?.add(Students.fromJson(v));
      });
    }
  }
  int? id;
  String? name;
  String? notes;
  int? status;
  int? schoolId;
  String? carNumber;
  String? createdAt;
  String? updatedAt;
  dynamic driver;
  Admin? admin;
  Schools? schools;
  List<Students>? students;
Bus copyWith({  int? id,
  String? name,
  String? notes,
  int? status,
  int? schoolId,
  String? carNumber,
  String? createdAt,
  String? updatedAt,
  dynamic driver,
  Admin? admin,
  Schools? schools,
  List<Students>? students,
}) => Bus(  id: id ?? this.id,
  name: name ?? this.name,
  notes: notes ?? this.notes,
  status: status ?? this.status,
  schoolId: schoolId ?? this.schoolId,
  carNumber: carNumber ?? this.carNumber,
  createdAt: createdAt ?? this.createdAt,
  updatedAt: updatedAt ?? this.updatedAt,
  driver: driver ?? this.driver,
  admin: admin ?? this.admin,
  schools: schools ?? this.schools,
  students: students ?? this.students,
);
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['notes'] = notes;
    map['status'] = status;
    map['school_id'] = schoolId;
    map['car_number'] = carNumber;
    map['created_at'] = createdAt;
    map['updated_at'] = updatedAt;
    map['driver'] = driver;
    if (admin != null) {
      map['admin'] = admin?.toJson();
    }
    if (schools != null) {
      map['schools'] = schools?.toJson();
    }
    if (students != null) {
      map['students'] = students?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

Students studentsFromJson(String str) => Students.fromJson(json.decode(str));
String studentsToJson(Students data) => json.encode(data.toJson());
class Students {
  Students({
      this.id, 
      this.name, 
      this.phone, 
      this.gradeId, 
      this.genderId, 
      this.schoolId, 
      this.religionId, 
      this.typeBloodId, 
      this.classroomId, 
      this.busId, 
      this.address, 
      this.cityName, 
      this.status, 
      this.tripType, 
      this.parentKey, 
      this.parentSecret, 
      this.dateBirth, 
      this.logo, 
      this.latitude, 
      this.longitude, 
      this.createdAt, 
      this.updatedAt, 
      this.logoPath,});

  Students.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    phone = json['phone'];
    gradeId = json['grade_id'];
    genderId = json['gender_id'];
    schoolId = json['school_id'];
    religionId = json['religion_id'];
    typeBloodId = json['type__blood_id'];
    classroomId = json['classroom_id'];
    busId = json['bus_id'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    tripType = json['trip_type'];
    parentKey = json['parent_key'];
    parentSecret = json['parent_secret'];
    dateBirth = json['Date_Birth'];
    logo = json['logo'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    logoPath = json['logo_path'];
  }
  String? id;
  String? name;
  String? phone;
  int? gradeId;
  int? genderId;
  int? schoolId;
  int? religionId;
  int? typeBloodId;
  int? classroomId;
  int? busId;
  String? address;
  String? cityName;
  int? status;
  String? tripType;
  String? parentKey;
  String? parentSecret;
  String? dateBirth;
  String? logo;
  String? latitude;
  String? longitude;
  String? createdAt;
  String? updatedAt;
  String? logoPath;
Students copyWith({  String? id,
  String? name,
  String? phone,
  int? gradeId,
  int? genderId,
  int? schoolId,
  int? religionId,
  int? typeBloodId,
  int? classroomId,
  int? busId,
  String? address,
  String? cityName,
  int? status,
  String? tripType,
  String? parentKey,
  String? parentSecret,
  String? dateBirth,
  String? logo,
  String? latitude,
  String? longitude,
  String? createdAt,
  String? updatedAt,
  String? logoPath,
}) => Students(  id: id ?? this.id,
  name: name ?? this.name,
  phone: phone ?? this.phone,
  gradeId: gradeId ?? this.gradeId,
  genderId: genderId ?? this.genderId,
  schoolId: schoolId ?? this.schoolId,
  religionId: religionId ?? this.religionId,
  typeBloodId: typeBloodId ?? this.typeBloodId,
  classroomId: classroomId ?? this.classroomId,
  busId: busId ?? this.busId,
  address: address ?? this.address,
  cityName: cityName ?? this.cityName,
  status: status ?? this.status,
  tripType: tripType ?? this.tripType,
  parentKey: parentKey ?? this.parentKey,
  parentSecret: parentSecret ?? this.parentSecret,
  dateBirth: dateBirth ?? this.dateBirth,
  logo: logo ?? this.logo,
  latitude: latitude ?? this.latitude,
  longitude: longitude ?? this.longitude,
  createdAt: createdAt ?? this.createdAt,
  updatedAt: updatedAt ?? this.updatedAt,
  logoPath: logoPath ?? this.logoPath,
);
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['phone'] = phone;
    map['grade_id'] = gradeId;
    map['gender_id'] = genderId;
    map['school_id'] = schoolId;
    map['religion_id'] = religionId;
    map['type__blood_id'] = typeBloodId;
    map['classroom_id'] = classroomId;
    map['bus_id'] = busId;
    map['address'] = address;
    map['city_name'] = cityName;
    map['status'] = status;
    map['trip_type'] = tripType;
    map['parent_key'] = parentKey;
    map['parent_secret'] = parentSecret;
    map['Date_Birth'] = dateBirth;
    map['logo'] = logo;
    map['latitude'] = latitude;
    map['longitude'] = longitude;
    map['created_at'] = createdAt;
    map['updated_at'] = updatedAt;
    map['logo_path'] = logoPath;
    return map;
  }

}

Schools schoolsFromJson(String str) => Schools.fromJson(json.decode(str));
String schoolsToJson(Schools data) => json.encode(data.toJson());
class Schools {
  Schools({
      this.id, 
      this.name, 
      this.email, 
      this.phone, 
      this.emailVerifiedAt, 
      this.address, 
      this.cityName, 
      this.status, 
      this.logo, 
      this.createdAt, 
      this.updatedAt, 
      this.typeAuth, 
      this.latitude, 
      this.longitude, 
      this.logoPath,});

  Schools.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    emailVerifiedAt = json['email_verified_at'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    logo = json['logo'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    typeAuth = json['typeAuth'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    logoPath = json['logo_path'];
  }
  int? id;
  String? name;
  String? email;
  String? phone;
  String? emailVerifiedAt;
  String? address;
  String? cityName;
  int? status;
  String? logo;
  String? createdAt;
  String? updatedAt;
  String? typeAuth;
  String? latitude;
  String? longitude;
  String? logoPath;
Schools copyWith({  int? id,
  String? name,
  String? email,
  String? phone,
  String? emailVerifiedAt,
  String? address,
  String? cityName,
  int? status,
  String? logo,
  String? createdAt,
  String? updatedAt,
  String? typeAuth,
  String? latitude,
  String? longitude,
  String? logoPath,
}) => Schools(  id: id ?? this.id,
  name: name ?? this.name,
  email: email ?? this.email,
  phone: phone ?? this.phone,
  emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
  address: address ?? this.address,
  cityName: cityName ?? this.cityName,
  status: status ?? this.status,
  logo: logo ?? this.logo,
  createdAt: createdAt ?? this.createdAt,
  updatedAt: updatedAt ?? this.updatedAt,
  typeAuth: typeAuth ?? this.typeAuth,
  latitude: latitude ?? this.latitude,
  longitude: longitude ?? this.longitude,
  logoPath: logoPath ?? this.logoPath,
);
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['email'] = email;
    map['phone'] = phone;
    map['email_verified_at'] = emailVerifiedAt;
    map['address'] = address;
    map['city_name'] = cityName;
    map['status'] = status;
    map['logo'] = logo;
    map['created_at'] = createdAt;
    map['updated_at'] = updatedAt;
    map['typeAuth'] = typeAuth;
    map['latitude'] = latitude;
    map['longitude'] = longitude;
    map['logo_path'] = logoPath;
    return map;
  }

}

Admin adminFromJson(String str) => Admin.fromJson(json.decode(str));
String adminToJson(Admin data) => json.encode(data.toJson());
class Admin {
  Admin({
      this.id, 
      this.username, 
      this.name, 
      this.genderId, 
      this.schoolId, 
      this.religionId, 
      this.typeBloodId, 
      this.busId, 
      this.joiningDate, 
      this.address, 
      this.cityName, 
      this.status, 
      this.logo, 
      this.type, 
      this.phone, 
      this.birthDate, 
      this.emailVerifiedAt, 
      this.createdAt, 
      this.updatedAt, 
      this.typeAuth, 
      this.logoPath,});

  Admin.fromJson(dynamic json) {
    id = json['id'];
    username = json['username'];
    name = json['name'];
    genderId = json['gender_id'];
    schoolId = json['school_id'];
    religionId = json['religion_id'];
    typeBloodId = json['type__blood_id'];
    busId = json['bus_id'];
    joiningDate = json['Joining_Date'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    logo = json['logo'];
    type = json['type'];
    phone = json['phone'];
    birthDate = json['birth_date'];
    emailVerifiedAt = json['email_verified_at'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    typeAuth = json['typeAuth'];
    logoPath = json['logo_path'];
  }
  int? id;
  String? username;
  String? name;
  int? genderId;
  int? schoolId;
  int? religionId;
  int? typeBloodId;
  int? busId;
  dynamic joiningDate;
  String? address;
  String? cityName;
  int? status;
  String? logo;
  String? type;
  String? phone;
  dynamic birthDate;
  String? emailVerifiedAt;
  String? createdAt;
  String? updatedAt;
  String? typeAuth;
  String? logoPath;
Admin copyWith({  int? id,
  String? username,
  String? name,
  int? genderId,
  int? schoolId,
  int? religionId,
  int? typeBloodId,
  int? busId,
  dynamic joiningDate,
  String? address,
  String? cityName,
  int? status,
  String? logo,
  String? type,
  String? phone,
  dynamic birthDate,
  String? emailVerifiedAt,
  String? createdAt,
  String? updatedAt,
  String? typeAuth,
  String? logoPath,
}) => Admin(  id: id ?? this.id,
  username: username ?? this.username,
  name: name ?? this.name,
  genderId: genderId ?? this.genderId,
  schoolId: schoolId ?? this.schoolId,
  religionId: religionId ?? this.religionId,
  typeBloodId: typeBloodId ?? this.typeBloodId,
  busId: busId ?? this.busId,
  joiningDate: joiningDate ?? this.joiningDate,
  address: address ?? this.address,
  cityName: cityName ?? this.cityName,
  status: status ?? this.status,
  logo: logo ?? this.logo,
  type: type ?? this.type,
  phone: phone ?? this.phone,
  birthDate: birthDate ?? this.birthDate,
  emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
  createdAt: createdAt ?? this.createdAt,
  updatedAt: updatedAt ?? this.updatedAt,
  typeAuth: typeAuth ?? this.typeAuth,
  logoPath: logoPath ?? this.logoPath,
);
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['username'] = username;
    map['name'] = name;
    map['gender_id'] = genderId;
    map['school_id'] = schoolId;
    map['religion_id'] = religionId;
    map['type__blood_id'] = typeBloodId;
    map['bus_id'] = busId;
    map['Joining_Date'] = joiningDate;
    map['address'] = address;
    map['city_name'] = cityName;
    map['status'] = status;
    map['logo'] = logo;
    map['type'] = type;
    map['phone'] = phone;
    map['birth_date'] = birthDate;
    map['email_verified_at'] = emailVerifiedAt;
    map['created_at'] = createdAt;
    map['updated_at'] = updatedAt;
    map['typeAuth'] = typeAuth;
    map['logo_path'] = logoPath;
    return map;
  }

}