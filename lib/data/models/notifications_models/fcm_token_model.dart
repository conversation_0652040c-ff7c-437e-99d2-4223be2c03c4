import 'dart:convert';

class FcmTokenModel {
  final List<String>? data;
  final String? message;
  final bool? status;

  FcmTokenModel({
    this.data,
    this.message,
    this.status,
  });

  FcmTokenModel copyWith({
    List<String>? data,
    String? message,
    bool? status,
  }) =>
      FcmTokenModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory FcmTokenModel.fromJson(String str) => FcmTokenModel.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory FcmTokenModel.fromMap(Map<String, dynamic> json) => FcmTokenModel(
    data: json["data"] == null ? [] : List<String>.from(json["data"]!.map((x) => x)),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toMap() => {
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x)),
    "message": message,
    "status": status,
  };
}
