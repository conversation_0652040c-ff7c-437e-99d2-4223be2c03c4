import 'dart:convert';

ParentsModel parentsModelFromJson(String str) =>
    ParentsModel.fromJson(json.decode(str));
String parentsModelToJson(ParentsModel data) => json.encode(data.toJson());

class ParentsModel {
  ParentsModel({
    this.data,
    this.message,
    this.status,
  });

  ParentsModel.fromJson(dynamic json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }
  Data? data;
  String? message;
  bool? status;
  ParentsModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      ParentsModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (data != null) {
      map['data'] = data?.toJson();
    }
    map['message'] = message;
    map['status'] = status;
    return map;
  }
}

Data dataFromJson(String str) => Data.fromJson(json.decode(str));
String dataToJson(Data data) => json.encode(data.toJson());

class Data {
  Data({
    this.students,
    this.parentsCount,
  });

  Data.fromJson(dynamic json) {
    if (json['students'] != null) {
      students = [];
      json['students'].forEach((v) {
        students?.add(Students.fromJson(v));
      });
    }
    parentsCount = json['parents_count'];
  }
  List<Students>? students;
  int? parentsCount;
  Data copyWith({
    List<Students>? students,
    int? parentsCount,
  }) =>
      Data(
        students: students ?? this.students,
        parentsCount: parentsCount ?? this.parentsCount,
      );
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (students != null) {
      map['students'] = students?.map((v) => v.toJson()).toList();
    }
    map['parents_count'] = parentsCount;
    return map;
  }
}

Students studentsFromJson(String str) => Students.fromJson(json.decode(str));
String studentsToJson(Students data) => json.encode(data.toJson());

class Students {
  Students({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.status,
    this.logo,
    this.typeAuth,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  Students.fromJson(dynamic json) {
    id = json['id'].toString();
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    emailVerifiedAt = json['email_verified_at'];
    address = json['address'];
    status = json['status'];
    logo = json['logo'];
    typeAuth = json['typeAuth'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    logoPath = json['logo_path'];
  }
  String? id;
  String? name;
  String? email;
  String? phone;
  String? emailVerifiedAt;
  dynamic address;
  int? status;
  String? logo;
  String? typeAuth;
  String? createdAt;
  String? updatedAt;
  String? logoPath;
  Students copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? emailVerifiedAt,
    dynamic address,
    int? status,
    String? logo,
    String? typeAuth,
    String? createdAt,
    String? updatedAt,
    String? logoPath,
  }) =>
      Students(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
        address: address ?? this.address,
        status: status ?? this.status,
        logo: logo ?? this.logo,
        typeAuth: typeAuth ?? this.typeAuth,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        logoPath: logoPath ?? this.logoPath,
      );
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['email'] = email;
    map['phone'] = phone;
    map['email_verified_at'] = emailVerifiedAt;
    map['address'] = address;
    map['status'] = status;
    map['logo'] = logo;
    map['typeAuth'] = typeAuth;
    map['created_at'] = createdAt;
    map['updated_at'] = updatedAt;
    map['logo_path'] = logoPath;
    return map;
  }
}
