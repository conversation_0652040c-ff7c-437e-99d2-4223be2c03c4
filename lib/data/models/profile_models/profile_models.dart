class ProfileModels {
  int? id;
  String? username;
  String? name;
  int? genderId;
  int? schoolId;
  int? religionId;
  int? typeBloodId;
  int? busId;
  String? joiningDate;
  String? address;
  String? cityName;
  int? status;
  String? logo;
  String? type;
  String? phone;
  String? birthDate;
  String? emailVerifiedAt;
  String? firebaseToken;
  String? typeAuth;
  String? createdAt;
  String? updatedAt;
  String? logoPath;
  String? errors;

  ProfileModels(
      {this.id,
        this.username,
        this.name,
        this.genderId,
        this.schoolId,
        this.religionId,
        this.typeBloodId,
        this.busId,
        this.joiningDate,
        this.address,
        this.cityName,
        this.status,
        this.logo,
        this.type,
        this.phone,
        this.birthDate,
        this.emailVerifiedAt,
        this.firebaseToken,
        this.typeAuth,
        this.createdAt,
        this.updatedAt,
        this.logoPath, this.errors,});

  ProfileModels.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    username = json['username'];
    name = json['name'];
    genderId = json['gender_id'];
    schoolId = json['school_id'];
    religionId = json['religion_id'];
    typeBloodId = json['type__blood_id'];
    busId = json['bus_id'];
    joiningDate = json['Joining_Date'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    logo = json['logo'];
    type = json['type'];
    phone = json['phone'];
    birthDate = json['birth_date'];
    emailVerifiedAt = json['email_verified_at'];
    firebaseToken = json['firebase_token'];
    typeAuth = json['typeAuth'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    logoPath = json['logo_path'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['username'] = this.username;
    data['name'] = this.name;
    data['gender_id'] = this.genderId;
    data['school_id'] = this.schoolId;
    data['religion_id'] = this.religionId;
    data['type__blood_id'] = this.typeBloodId;
    data['bus_id'] = this.busId;
    data['Joining_Date'] = this.joiningDate;
    data['address'] = this.address;
    data['city_name'] = this.cityName;
    data['status'] = this.status;
    data['logo'] = this.logo;
    data['type'] = this.type;
    data['phone'] = this.phone;
    data['birth_date'] = this.birthDate;
    data['email_verified_at'] = this.emailVerifiedAt;
    data['firebase_token'] = this.firebaseToken;
    data['typeAuth'] = this.typeAuth;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['logo_path'] = this.logoPath;
    return data;
  }
}