class QuestionHelp {
  QuestionData? data;
  String? message;
  bool? status;

  QuestionHelp({this.data, this.message, this.status});

  QuestionHelp.fromJson(Map<String, dynamic> json) {
    data =
        json['data'] != null ? new QuestionData.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = this.message;
    data['status'] = this.status;
    return data;
  }
}

class QuestionData {
  List<QuestionDataList>? data;

  QuestionData({
    this.data,
  });

  QuestionData.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <QuestionDataList>[];
      json['data'].forEach((v) {
        data!.add(new QuestionDataList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJ<PERSON>() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class QuestionDataList {
  int? id;
  String? question;
  String? answer;
  String? type;
  int? status;
  String? createdAt;
  String? updatedAt;

  QuestionDataList(
      {this.id,
      this.question,
      this.answer,
      this.type,
      this.status,
      this.createdAt,
      this.updatedAt});

  QuestionDataList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    answer = json['answer'];
    type = json['type'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['answer'] = this.answer;
    data['type'] = this.type;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
