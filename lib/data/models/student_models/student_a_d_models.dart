import 'package:equatable/equatable.dart';

part 'student_a_d_models.g.dart';

class StudentADModels extends Equatable {
  final int? id;
  final String? email;
  final String? name;
  final String? gender_id;
  final String? school_id;
  final String? religion_id;
  final String? type__blood_id;
  final String? Joining_Date;
  final String? address;
  final String? city_name;
  final String? status;
  final String? logo;
  final String? type;
  final String? phone;
  final String? birth_date;
  final String? email_verified_at;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? typeAuth;
  final String? logo_path;

  const StudentADModels({
    this.created_at,
    this.school_id,
    this.status,
    this.updated_at,
    this.id,
    this.name,
    this.type__blood_id,
    this.religion_id,
    this.gender_id,
    this.logo,
    this.deleted_at,
    this.address,
    this.city_name,
    this.phone,
    this.email,
    this.logo_path,
    this.typeAuth,
    this.email_verified_at,
    this.type,
    this.birth_date,
    this.Joining_Date,
  });

  factory StudentADModels.fromJson(Map<String, dynamic> json) {
    return _$StudentADModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentADModelsToJson(this);

  @override
  List<Object?> get props => [
        created_at,
        school_id,
        status,
        updated_at,
        id,
        name,
        type__blood_id,
        religion_id,
        gender_id,
        logo,
        deleted_at,
        address,
        city_name,
        phone,
        email,
        logo_path,
        typeAuth,
        email_verified_at,
        type,
        birth_date,
        Joining_Date,
      ];
}
