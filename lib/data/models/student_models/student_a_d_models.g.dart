part of 'student_a_d_models.dart';

StudentADModels _$StudentADModelsFromJson(Map<String, dynamic> json) =>
    StudentADModels(
      created_at: json['created_at'] as String?,
      school_id: json['school_id'] as String?,
      status: json['status'] as String?,
      updated_at: json['updated_at'] as String?,
      id: json['id'] as int?,
      name: json['name'] as String?,
      type__blood_id: json['type__blood_id'] as String?,
      religion_id: json['religion_id'] as String?,
      gender_id: json['gender_id'] as String?,
      logo: json['logo'] as String?,
      deleted_at: json['deleted_at'] as String?,
      address: json['address'] as String?,
      city_name: json['city_name'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      logo_path: json['logo_path'] as String?,
      typeAuth: json['typeAuth'] as String?,
      email_verified_at: json['email_verified_at'] as String?,
      type: json['type'] as String?,
      birth_date: json['birth_date'] as String?,
      Joining_Date: json['Joining_Date'] as String?,
    );

Map<String, dynamic> _$StudentADModelsToJson(StudentADModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'gender_id': instance.gender_id,
      'school_id': instance.school_id,
      'religion_id': instance.religion_id,
      'type__blood_id': instance.type__blood_id,
      'Joining_Date': instance.Joining_Date,
      'address': instance.address,
      'city_name': instance.city_name,
      'status': instance.status,
      'logo': instance.logo,
      'type': instance.type,
      'phone': instance.phone,
      'birth_date': instance.birth_date,
      'email_verified_at': instance.email_verified_at,
      'deleted_at': instance.deleted_at,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'typeAuth': instance.typeAuth,
      'logo_path': instance.logo_path,
    };
