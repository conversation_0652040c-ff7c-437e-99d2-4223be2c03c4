import 'package:equatable/equatable.dart';

part 'student_attendance_models.g.dart';

class StudentAttendanceModels extends Equatable {
  final int? id;
  final String? grade_id;
  final String? school_id;
  final String? classroom_id;
  final String? bus_id;
  final String? attendant_driver_id;
  final String? attendant_admins_id;
  final String? my__parent_id;
  final String? student_id;
  final String? attendence_date;
  final String? trip_id;
  final String? type;
  final String? attendence_type;
  final String? attendence_status;
  final String? created_at;
  final String? updated_at;

  const StudentAttendanceModels({
    this.type,
    this.id,
    this.updated_at,
    this.school_id,
    this.created_at,
    this.grade_id,
    this.attendant_admins_id,
    this.attendant_driver_id,
    this.classroom_id,
    this.bus_id,
    this.attendence_date,
    this.attendence_status,
    this.attendence_type,
    this.my__parent_id,
    this.student_id,
    this.trip_id,
  });

  factory StudentAttendanceModels.fromJson(Map<String, dynamic> json) {
    return _$StudentAttendanceModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentAttendanceModelsToJson(this);

  @override
  List<Object?> get props => [
        type,
        id,
        updated_at,
        school_id,
        created_at,
        grade_id,
        attendant_admins_id,
        attendant_driver_id,
        classroom_id,
        bus_id,
        attendence_date,
        attendence_status,
        attendence_type,
        my__parent_id,
        student_id,
        trip_id,
      ];
}
