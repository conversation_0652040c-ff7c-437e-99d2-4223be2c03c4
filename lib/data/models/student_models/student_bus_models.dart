import 'package:equatable/equatable.dart';

part 'student_bus_models.g.dart';

class StudentBusModels extends Equatable {
  final int? id;
  final String? name;
  final String? notes;
  final String? status;
  final String? school_id;
  final String? attendant_driver_id;
  final String? attendant_admins_id;
  final String? car_number;
  final String? created_at;
  final String? updated_at;

  const StudentBusModels({
    this.created_at,
    this.updated_at,
    this.school_id,
    this.attendant_driver_id,
    this.attendant_admins_id,
    this.name,
    this.id,
    this.status,
    this.notes,
    this.car_number,
  });

  factory StudentBusModels.fromJson(Map<String, dynamic> json) {
    return _$StudentBusModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentBusModelsToJson(this);

  @override
  List<Object?> get props => [
        created_at,
        updated_at,
        school_id,
        attendant_driver_id,
        attendant_admins_id,
        name,
        id,
        status,
        notes,
        car_number,
      ];
}
