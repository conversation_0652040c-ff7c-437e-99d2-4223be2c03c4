// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'student_data_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StudentDataModels _$StudentDataModelsFromJson(Map<String, dynamic> json) =>
    StudentDataModels(
      path: json['path'] as String?,
      from: json['from'] as int?,
      current_page: json['current_page'] as int?,
      first_page_url: json['first_page_url'] as String?,
      last_page: json['last_page'] as int?,
      last_page_url: json['last_page_url'] as String?,
      next_page_url: json['next_page_url'] as String?,
      per_page: json['per_page'] as int?,
      prev_page_url: json['prev_page_url'] as String?,
      to: json['to'] as int?,
      total: json['total'] as int?,
      // links: (json['links'] as List<dynamic>?)
      //     ?.map((e) => StudentLinksModels.fromJson(e as Map<String, dynamic>))
      //     .toList(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => StudentInfoModels.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$StudentDataModelsToJson(StudentDataModels instance) =>
    <String, dynamic>{
      'current_page': instance.current_page,
      'first_page_url': instance.first_page_url,
      'from': instance.from,
      'last_page': instance.last_page,
      'last_page_url': instance.last_page_url,
      'next_page_url': instance.next_page_url,
      'path': instance.path,
      'per_page': instance.per_page,
      'prev_page_url': instance.prev_page_url,
      'to': instance.to,
      'total': instance.total,
      'links': instance.links,
      'data': instance.data,
    };
