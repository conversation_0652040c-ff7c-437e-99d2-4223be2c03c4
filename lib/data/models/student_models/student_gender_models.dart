import 'package:equatable/equatable.dart';

part 'student_gender_models.g.dart';

class StudentGenderModels extends Equatable {
  final int? id;
  final String? name;
  final String? created_at;
  final String? updated_at;

  const StudentGenderModels({
    this.id,
    this.created_at,
    this.updated_at,
    this.name,
  });
  factory StudentGenderModels.fromJson(Map<String, dynamic> json) {
    return _$StudentGenderModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentGenderModelsToJson(this);
  @override
  List<Object?> get props => [
        id,
        created_at,
        updated_at,
        name,
      ];
}
