import 'package:equatable/equatable.dart';

part 'student_grade_models.g.dart';

class StudentGradeModels extends Equatable {
  final int? id;
  final String? name;
  final String? notes;
  final String? status;
  final String? created_at;
  final String? updated_at;

  const StudentGradeModels({
    this.notes,
    this.status,
    this.id,
    this.name,
    this.updated_at,
    this.created_at,
  });

  factory StudentGradeModels.fromJson(Map<String, dynamic> json) {
    return _$StudentGradeModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentGradeModelsToJson(this);

  @override
  List<Object?> get props => [
        notes,
        status,
        id,
        name,
        updated_at,
        created_at,
      ];
}
