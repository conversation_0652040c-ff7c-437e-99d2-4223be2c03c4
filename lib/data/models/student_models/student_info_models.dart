
import 'package:bus_driver/data/models/student_models/student_a_d_models.dart';
import 'package:bus_driver/data/models/student_models/student_attendance_models.dart';
import 'package:bus_driver/data/models/student_models/student_bus_models.dart';
import 'package:bus_driver/data/models/student_models/student_classRoom_models.dart';
import 'package:bus_driver/data/models/student_models/student_gender_models.dart';
import 'package:bus_driver/data/models/student_models/student_grade_models.dart';
import 'package:equatable/equatable.dart';

part 'student_info_models.g.dart';

class StudentInfoModels extends Equatable {
  final int? id;
  final String? name;
  final String? phone;
  final String? grade_id;
  final String? gender_id;
  final String? school_id;
  final String? religion_id;
  final String? type__blood_id;
  final String? classroom_id;
  final String? bus_id;
  final String? address;
  final String? city_name;
  final String? status;
  final String? trip_type;
  final String? attendant_driver_id;
  final String? attendant_admins_id;
  final String? parent_key;
  final String? parent_secret;
  final String? Date_Birth;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final String? deleted_at;
  final String? created_at;
  final String? updated_at;
  final String? logo_path;

  final StudentGenderModels? gender;
  final StudentGenderModels? religion;
  final StudentGenderModels? type_blood;
  final StudentBusModels? bus;
  final StudentGradeModels? grade;
  final StudentClassRoomModels? classroom;
  // final List<String>? my__parents;
  // final List<String>? absences;
  final StudentADModels? attendant_admins;
  final StudentADModels? attendant_driver;
  final List<StudentAttendanceModels>? attendance;

  const StudentInfoModels({
    this.status,
    this.id,
    this.phone,
    this.city_name,
    this.address,
    this.name,
    this.logo_path,
    this.updated_at,
    this.created_at,
    this.deleted_at,
    this.logo,
    this.longitude,
    this.latitude,
    this.attendant_admins_id,
    this.attendant_driver_id,
    this.bus_id,
    this.classroom_id,
    this.Date_Birth,
    this.gender_id,
    this.grade_id,
    this.parent_key,
    this.parent_secret,
    this.religion_id,
    this.school_id,
    this.trip_type,
    this.type__blood_id,
    this.gender,
    this.religion,
    this.type_blood,
    this.bus,
    this.grade,
    this.classroom,
    // this.my__parents,
    // this.absences,
    this.attendant_admins,
    this.attendant_driver,
    this.attendance,
  });

  factory StudentInfoModels.fromJson(Map<String, dynamic> json) {
    return _$StudentInfoModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentInfoModelsToJson(this);

  @override
  List<Object?> get props => [
        status,
        id,
        phone,
        city_name,
        address,
        name,
        logo_path,
        updated_at,
        created_at,
        deleted_at,
        logo,
        longitude,
        latitude,
        attendant_admins_id,
        attendant_driver_id,
        bus_id,
        classroom_id,
        Date_Birth,
        gender_id,
        grade_id,
        parent_key,
        parent_secret,
        religion_id,
        school_id,
        trip_type,
        type__blood_id,

        gender,
        religion,
        type_blood,
        bus,
        grade,
        classroom,
        // my__parents,
        // absences,
        attendant_admins,
        attendant_driver,
        attendance,
      ];
}
