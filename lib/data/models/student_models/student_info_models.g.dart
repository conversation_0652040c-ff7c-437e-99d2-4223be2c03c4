// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'student_info_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StudentInfoModels _$StudentInfoModelsFromJson(Map<String, dynamic> json) =>
    StudentInfoModels(
      status: json['status'] as String?,
      id: json['id'] as int?,
      phone: json['phone'] as String?,
      city_name: json['city_name'] as String?,
      address: json['address'] as String?,
      name: json['name'] as String?,
      logo_path: json['logo_path'] as String?,
      updated_at: json['updated_at'] as String?,
      created_at: json['created_at'] as String?,
      deleted_at: json['deleted_at'] as String?,
      logo: json['logo'] as String?,
      longitude: json['longitude'] as String?,
      latitude: json['latitude'] as String?,
      attendant_admins_id: json['attendant_admins_id'] as String?,
      attendant_driver_id: json['attendant_driver_id'] as String?,
      bus_id: json['bus_id'] as String?,
      classroom_id: json['classroom_id'] as String?,
      Date_Birth: json['Date_Birth'] as String?,
      gender_id: json['gender_id'] as String?,
      grade_id: json['grade_id'] as String?,
      parent_key: json['parent_key'] as String?,
      parent_secret: json['parent_secret'] as String?,
      religion_id: json['religion_id'] as String?,
      school_id: json['school_id'] as String?,
      trip_type: json['trip_type'] as String?,
      type__blood_id: json['type__blood_id'] as String?,
      gender: json['gender'] == null
          ? null
          : StudentGenderModels.fromJson(
              json['gender'] as Map<String, dynamic>),
      religion: json['religion'] == null
          ? null
          : StudentGenderModels.fromJson(
              json['religion'] as Map<String, dynamic>),
      type_blood: json['type_blood'] == null
          ? null
          : StudentGenderModels.fromJson(
              json['type_blood'] as Map<String, dynamic>),
      bus: json['bus'] == null
          ? null
          : StudentBusModels.fromJson(json['bus'] as Map<String, dynamic>),
      grade: json['grade'] == null
          ? null
          : StudentGradeModels.fromJson(json['grade'] as Map<String, dynamic>),
      classroom: json['classroom'] == null
          ? null
          : StudentClassRoomModels.fromJson(
              json['classroom'] as Map<String, dynamic>),
      attendant_admins: json['attendant_admins'] == null
          ? null
          : StudentADModels.fromJson(
              json['attendant_admins'] as Map<String, dynamic>),
      attendant_driver: json['attendant_driver'] == null
          ? null
          : StudentADModels.fromJson(
              json['attendant_driver'] as Map<String, dynamic>),
      attendance: (json['attendance'] as List<dynamic>?)
          ?.map((e) =>
              StudentAttendanceModels.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$StudentInfoModelsToJson(StudentInfoModels instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'grade_id': instance.grade_id,
      'gender_id': instance.gender_id,
      'school_id': instance.school_id,
      'religion_id': instance.religion_id,
      'type__blood_id': instance.type__blood_id,
      'classroom_id': instance.classroom_id,
      'bus_id': instance.bus_id,
      'address': instance.address,
      'city_name': instance.city_name,
      'status': instance.status,
      'trip_type': instance.trip_type,
      'attendant_driver_id': instance.attendant_driver_id,
      'attendant_admins_id': instance.attendant_admins_id,
      'parent_key': instance.parent_key,
      'parent_secret': instance.parent_secret,
      'Date_Birth': instance.Date_Birth,
      'logo': instance.logo,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'deleted_at': instance.deleted_at,
      'created_at': instance.created_at,
      'updated_at': instance.updated_at,
      'logo_path': instance.logo_path,
      'gender': instance.gender,
      'religion': instance.religion,
      'type_blood': instance.type_blood,
      'bus': instance.bus,
      'grade': instance.grade,
      'classroom': instance.classroom,
      'attendant_admins': instance.attendant_admins,
      'attendant_driver': instance.attendant_driver,
      'attendance': instance.attendance,
    };
