import 'package:bus_driver/data/models/student_models/student_data_models.dart';
import 'package:equatable/equatable.dart';

part 'student_models.g.dart';

class StudentModels extends Equatable {
  String? message;
  bool? status;
  StudentDataModels? data;

  StudentModels({
    this.message,
    this.status,
    this.data,
  });

  factory StudentModels.fromJson(Map<String, dynamic> json) {
    return _$StudentModelsFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StudentModelsToJson(this);

  @override
  List<Object?> get props => [
        message,
        status,
        data,
      ];
}
