part of 'student_models.dart';

StudentModels _$StudentModelsFromJson(Map<String, dynamic> json) {
  return StudentModels(
    message: json['message'] as String?,
    status: json['status'] as bool?,
    data: json['data'] == null
        ? null
        : StudentDataModels.fromJson(json['data'][0] as Map<String, dynamic>),
  );
}

Map<String, dynamic> _$StudentModelsToJson(StudentModels instance) =>
    <String, dynamic>{
      'message': instance.message,
      'status': instance.status,
      'data': instance.data,
    };
