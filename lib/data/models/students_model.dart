class StudentsModel {
  Data? data;
  String? message;
  bool? status;

  StudentsModel({this.data, this.message, this.status});

  StudentsModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    data['status'] = status;
    return data;
  }
}

class Data {
  List<Students>? students;
  int? studentsCount;

  Data({this.students, this.studentsCount});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['students'] != null) {
      students = <Students>[];
      json['students'].forEach((v) {
        students!.add(Students.fromJson(v));
      });
    }
    studentsCount = json['students_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (students != null) {
      data['students'] = students!.map((v) => v.toJson()).toList();
    }
    data['students_count'] = studentsCount;
    return data;
  }
}

class Students {
  String? id;
  String? name;
  String? phone;
  int? gradeId;
  int? genderId;
  int? schoolId;
  int? religionId;
  int? typeBloodId;
  int? classroomId;
  int? busId;
  String? address;
  String? cityName;
  int? status;
  String? tripType;
  String? parentKey;
  String? parentSecret;
  String? dateBirth;
  String? logo;
  String? latitude;
  String? longitude;
  String? createdAt;
  String? updatedAt;
  String? logoPath;
  Schools? schools;
  Gender? gender;
  Gender? religion;
  Gender? typeBlood;
  Bus? bus;
  Grade? grade;
  Classroom? classroom;
  List<MyParents>? myParents;

  Students(
      {this.id,
      this.name,
      this.phone,
      this.gradeId,
      this.genderId,
      this.schoolId,
      this.religionId,
      this.typeBloodId,
      this.classroomId,
      this.busId,
      this.address,
      this.cityName,
      this.status,
      this.tripType,
      this.parentKey,
      this.parentSecret,
      this.dateBirth,
      this.logo,
      this.latitude,
      this.longitude,
      this.createdAt,
      this.updatedAt,
      this.logoPath,
      this.schools,
      this.gender,
      this.religion,
      this.typeBlood,
      this.bus,
      this.grade,
      this.classroom,
      this.myParents});

  Students.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    phone = json['phone'];
    gradeId = json['grade_id'];
    genderId = json['gender_id'];
    schoolId = json['school_id'];
    religionId = json['religion_id'];
    typeBloodId = json['type__blood_id'];
    classroomId = json['classroom_id'];
    busId = json['bus_id'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    tripType = json['trip_type'];
    parentKey = json['parent_key'];
    parentSecret = json['parent_secret'];
    dateBirth = json['Date_Birth'];
    logo = json['logo'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    logoPath = json['logo_path'];
    schools =
        json['schools'] != null ? Schools.fromJson(json['schools']) : null;
    gender = json['gender'] != null ? Gender.fromJson(json['gender']) : null;
    religion =
        json['religion'] != null ? Gender.fromJson(json['religion']) : null;
    typeBlood =
        json['type_blood'] != null ? Gender.fromJson(json['type_blood']) : null;
    bus = json['bus'] != null ? Bus.fromJson(json['bus']) : null;
    grade = json['grade'] != null ? Grade.fromJson(json['grade']) : null;
    classroom = json['classroom'] != null
        ? Classroom.fromJson(json['classroom'])
        : null;
    if (json['my__parents'] != null) {
      myParents = <MyParents>[];
      json['my__parents'].forEach((v) {
        myParents!.add(MyParents.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['phone'] = phone;
    data['grade_id'] = gradeId;
    data['gender_id'] = genderId;
    data['school_id'] = schoolId;
    data['religion_id'] = religionId;
    data['type__blood_id'] = typeBloodId;
    data['classroom_id'] = classroomId;
    data['bus_id'] = busId;
    data['address'] = address;
    data['city_name'] = cityName;
    data['status'] = status;
    data['trip_type'] = tripType;
    data['parent_key'] = parentKey;
    data['parent_secret'] = parentSecret;
    data['Date_Birth'] = dateBirth;
    data['logo'] = logo;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['logo_path'] = logoPath;
    if (schools != null) {
      data['schools'] = schools!.toJson();
    }
    if (gender != null) {
      data['gender'] = gender!.toJson();
    }
    if (religion != null) {
      data['religion'] = religion!.toJson();
    }
    if (typeBlood != null) {
      data['type_blood'] = typeBlood!.toJson();
    }
    if (bus != null) {
      data['bus'] = bus!.toJson();
    }
    if (grade != null) {
      data['grade'] = grade!.toJson();
    }
    if (classroom != null) {
      data['classroom'] = classroom!.toJson();
    }
    if (myParents != null) {
      data['my__parents'] = myParents!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Schools {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? emailVerifiedAt;
  String? address;
  String? cityName;
  int? status;
  String? logo;
  String? createdAt;
  String? updatedAt;
  String? typeAuth;
  String? latitude;
  String? longitude;
  String? logoPath;

  Schools(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.emailVerifiedAt,
      this.address,
      this.cityName,
      this.status,
      this.logo,
      this.createdAt,
      this.updatedAt,
      this.typeAuth,
      this.latitude,
      this.longitude,
      this.logoPath});

  Schools.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    emailVerifiedAt = json['email_verified_at'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    logo = json['logo'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    typeAuth = json['typeAuth'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    logoPath = json['logo_path'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['email_verified_at'] = emailVerifiedAt;
    data['address'] = address;
    data['city_name'] = cityName;
    data['status'] = status;
    data['logo'] = logo;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['typeAuth'] = typeAuth;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['logo_path'] = logoPath;
    return data;
  }
}

class Gender {
  int? id;
  String? name;
  String? createdAt;
  String? updatedAt;

  Gender({
    this.id,
    this.name,
    this.createdAt,
    this.updatedAt,
  });

  Gender.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class Bus {
  int? id;
  String? name;
  String? notes;
  int? status;
  int? schoolId;
  String? carNumber;
  String? createdAt;
  String? updatedAt;
  Driver? driver;
  Driver? admin;

  Bus(
      {this.id,
      this.name,
      this.notes,
      this.status,
      this.schoolId,
      this.carNumber,
      this.createdAt,
      this.updatedAt,
      this.driver,
      this.admin});

  Bus.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    notes = json['notes'];
    status = json['status'];
    schoolId = json['school_id'];
    carNumber = json['car_number'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    driver = json['driver'] != null ? Driver.fromJson(json['driver']) : null;
    admin = json['admin'] != null ? Driver.fromJson(json['admin']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['notes'] = notes;
    data['status'] = status;
    data['school_id'] = schoolId;
    data['car_number'] = carNumber;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (driver != null) {
      data['driver'] = driver!.toJson();
    }
    if (admin != null) {
      data['admin'] = admin!.toJson();
    }
    return data;
  }
}

class Driver {
  int? id;
  String? username;
  String? name;
  int? genderId;
  int? schoolId;
  int? religionId;
  int? typeBloodId;
  int? busId;
  String? joiningDate;
  String? address;
  String? cityName;
  int? status;
  String? logo;
  String? type;
  String? phone;
  String? birthDate;
  String? emailVerifiedAt;
  String? createdAt;
  String? updatedAt;
  String? typeAuth;
  String? logoPath;

  Driver(
      {this.id,
      this.username,
      this.name,
      this.genderId,
      this.schoolId,
      this.religionId,
      this.typeBloodId,
      this.busId,
      this.joiningDate,
      this.address,
      this.cityName,
      this.status,
      this.logo,
      this.type,
      this.phone,
      this.birthDate,
      this.emailVerifiedAt,
      this.createdAt,
      this.updatedAt,
      this.typeAuth,
      this.logoPath});

  Driver.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    username = json['username'];
    name = json['name'];
    genderId = json['gender_id'];
    schoolId = json['school_id'];
    religionId = json['religion_id'];
    typeBloodId = json['type__blood_id'];
    busId = json['bus_id'];
    joiningDate = json['Joining_Date'];
    address = json['address'];
    cityName = json['city_name'];
    status = json['status'];
    logo = json['logo'];
    type = json['type'];
    phone = json['phone'];
    birthDate = json['birth_date'];
    emailVerifiedAt = json['email_verified_at'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    typeAuth = json['typeAuth'];
    logoPath = json['logo_path'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = username;
    data['name'] = name;
    data['gender_id'] = genderId;
    data['school_id'] = schoolId;
    data['religion_id'] = religionId;
    data['type__blood_id'] = typeBloodId;
    data['bus_id'] = busId;
    data['Joining_Date'] = joiningDate;
    data['address'] = address;
    data['city_name'] = cityName;
    data['status'] = status;
    data['logo'] = logo;
    data['type'] = type;
    data['phone'] = phone;
    data['birth_date'] = birthDate;
    data['email_verified_at'] = emailVerifiedAt;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['typeAuth'] = typeAuth;
    data['logo_path'] = logoPath;
    return data;
  }
}

class Grade {
  int? id;
  String? name;
  String? notes;
  int? status;
  String? createdAt;
  String? updatedAt;
  String? defaultClassrooms;

  Grade(
      {this.id,
      this.name,
      this.notes,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.defaultClassrooms});

  Grade.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    notes = json['notes'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    defaultClassrooms = json['default_classrooms'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['notes'] = notes;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['default_classrooms'] = defaultClassrooms;
    return data;
  }
}

class Classroom {
  int? id;
  String? name;
  int? status;
  int? schoolId;
  int? gradeId;
  String? createdAt;
  String? updatedAt;

  Classroom(
      {this.id,
      this.name,
      this.status,
      this.schoolId,
      this.gradeId,
      this.createdAt,
      this.updatedAt});

  Classroom.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    status = json['status'];
    schoolId = json['school_id'];
    gradeId = json['grade_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    data['school_id'] = schoolId;
    data['grade_id'] = gradeId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class MyParents {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? emailVerifiedAt;
  String? address;
  int? status;
  String? logo;
  String? typeAuth;
  String? createdAt;
  String? updatedAt;
  String? logoPath;
  Pivot? pivot;

  MyParents(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.emailVerifiedAt,
      this.address,
      this.status,
      this.logo,
      this.typeAuth,
      this.createdAt,
      this.updatedAt,
      this.logoPath,
      this.pivot});

  MyParents.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    emailVerifiedAt = json['email_verified_at'];
    address = json['address'];
    status = json['status'];
    logo = json['logo'];
    typeAuth = json['typeAuth'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    logoPath = json['logo_path'];
    pivot = json['pivot'] != null ? Pivot.fromJson(json['pivot']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['email_verified_at'] = emailVerifiedAt;
    data['address'] = address;
    data['status'] = status;
    data['logo'] = logo;
    data['typeAuth'] = typeAuth;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['logo_path'] = logoPath;
    if (pivot != null) {
      data['pivot'] = pivot!.toJson();
    }
    return data;
  }
}

class Pivot {
  String? studentId;
  int? myParentId;

  Pivot({this.studentId, this.myParentId});

  Pivot.fromJson(Map<String, dynamic> json) {
    studentId = json['student_id'];
    myParentId = json['my__parent_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['student_id'] = studentId;
    data['my__parent_id'] = myParentId;
    return data;
  }
}
