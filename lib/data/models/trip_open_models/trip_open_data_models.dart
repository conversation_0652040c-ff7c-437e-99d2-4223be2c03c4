import 'dart:convert';

class TripOpenDataModels {
  final int? id;
  final int? school_id;
  final int? bus_id;
  final String? trips_date;
  final String? trip_type;
  final int? status;
  final String? latitude;
  final String? longitude;
  final String? created_at;
  final String? updated_at;
  final String? attendance_type;

  const TripOpenDataModels({
    this.id,
    this.school_id,
    this.bus_id,
    this.status,
    this.created_at,
    this.attendance_type,
    this.latitude,
    this.longitude,
    this.trip_type,
    this.trips_date,
    this.updated_at,
  });

  TripOpenDataModels copyWith({
    int? id,
    int? school_id,
    int? bus_id,
    String? trips_date,
    String? trip_type,
    int? status,
    String? latitude,
    String? longitude,
    String? created_at,
    String? updated_at,
    String? attendance_type,
  }) =>
      TripOpenDataModels(
        id: id ?? this.id,
        school_id: school_id ?? this.school_id,
        bus_id: bus_id ?? this.bus_id,
        trips_date: trips_date ?? this.trips_date,
        trip_type: trip_type ?? this.trip_type,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        created_at: created_at ?? this.created_at,
        updated_at: updated_at ?? this.updated_at,
        attendance_type: attendance_type ?? this.attendance_type,
      );

  factory TripOpenDataModels.fromJson(String str) =>
      TripOpenDataModels.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory TripOpenDataModels.fromMap(Map<String, dynamic> json) =>
      TripOpenDataModels(
        status: json["status"],
        school_id: json["school_id"],
        bus_id: json["bus_id"],
        trips_date: json["trips_date"],
        trip_type: json["trip_type"],
        id: json["id"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        created_at: json["created_at"],
        updated_at: json["updated_at"],
        attendance_type: json["attendance_type"],
      );

  Map<String, dynamic> toMap() => {
        "status": status,
        "school_id": school_id,
        "bus_id": bus_id,
        "trips_date": trips_date,
        "trip_type": trip_type,
        "id": id,
        "latitude": latitude,
        "longitude": longitude,
        "created_at": created_at,
        "updated_at": updated_at,
        "attendance_type": attendance_type,
      };
}
