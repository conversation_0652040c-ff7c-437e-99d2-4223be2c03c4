import 'dart:convert';
import 'package:bus_driver/data/models/trip_open_models/trip_open_data_models.dart';

class TripOpenModels {
  final bool? status;
  final String? message;
  final bool? errors;
  final dynamic trip;

  const TripOpenModels({
    this.message,
    this.status,
    this.errors,
    this.trip,
  });

  TripOpenModels copyWith(
          {bool? status,
          String? message,
          bool? errors,
          TripOpenDataModels? trip}) =>
      TripOpenModels(
        status: status ?? this.status,
        message: message ?? this.message,
        errors: errors ?? this.errors,
        trip: trip ?? this.trip,
      );

  factory TripOpenModels.fromRawJson(String str) => TripOpenModels.fromMap(json.decode(str));

  String toRawJson() => json.encode(toMap());

  factory TripOpenModels.fromMap(Map<String, dynamic> json) => TripOpenModels(
      status: json["status"],
      message: json["message"],
      errors: json["errors"],
      trip: json["status"] == false
          ? []
          : TripOpenDataModels.fromMap(json["trip"]));

  Map<String, dynamic> toMap() => {
        "status": status,
        "message": message,
        "errors": errors,
        "trip": trip?.toJson(),
      };
}
