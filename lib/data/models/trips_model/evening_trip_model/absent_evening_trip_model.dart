import 'dart:convert';

class AbsentEveningTripModel {
  final List<Absence>? absences;
  final String? message;
  final bool? errors;

  AbsentEveningTripModel({
    this.absences,
    this.message,
    this.errors,
  });

  AbsentEveningTripModel copyWith({
    List<Absence>? absences,
    String? message,
    bool? errors,
  }) =>
      AbsentEveningTripModel(
        absences: absences ?? this.absences,
        message: message ?? this.message,
        errors: errors ?? this.errors,
      );

  factory AbsentEveningTripModel.fromRawJson(String str) =>
      AbsentEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AbsentEveningTripModel.fromJson(Map<String, dynamic> json) =>
      AbsentEveningTripModel(
        absences: json["absences"] == null
            ? []
            : List<Absence>.from(
                json["absences"]!.map((x) => Absence.fromJson(x))),
        message: json["message"],
        errors: json["errors"],
      );

  Map<String, dynamic> toJson() => {
        "absences": absences == null
            ? []
            : List<dynamic>.from(absences!.map((x) => x.toJson())),
        "message": message,
        "errors": errors,
      };
}

class Absence {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final int? typeBloodId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final String? cityName;
  final String? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final DateTime? dateBirth;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? logoPath;

  Absence({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.classroomId,
    this.busId,
    this.address,
    this.cityName,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.dateBirth,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  Absence copyWith({
    String? id,
    String? name,
    String? phone,
    int? gradeId,
    int? genderId,
    int? schoolId,
    int? religionId,
    int? typeBloodId,
    int? classroomId,
    int? busId,
    String? address,
    String? cityName,
    String? status,
    String? tripType,
    String? parentKey,
    String? parentSecret,
    DateTime? dateBirth,
    String? logo,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? logoPath,
  }) =>
      Absence(
        id: id ?? this.id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        gradeId: gradeId ?? this.gradeId,
        genderId: genderId ?? this.genderId,
        schoolId: schoolId ?? this.schoolId,
        religionId: religionId ?? this.religionId,
        typeBloodId: typeBloodId ?? this.typeBloodId,
        classroomId: classroomId ?? this.classroomId,
        busId: busId ?? this.busId,
        address: address ?? this.address,
        cityName: cityName ?? this.cityName,
        status: status ?? this.status,
        tripType: tripType ?? this.tripType,
        parentKey: parentKey ?? this.parentKey,
        parentSecret: parentSecret ?? this.parentSecret,
        dateBirth: dateBirth ?? this.dateBirth,
        logo: logo ?? this.logo,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        logoPath: logoPath ?? this.logoPath,
      );

  factory Absence.fromRawJson(String str) => Absence.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Absence.fromJson(Map<String, dynamic> json) => Absence(
        id: json["id"],
        name: json["name"],
        phone: json["phone"],
        gradeId: json["grade_id"],
        genderId: json["gender_id"],
        schoolId: json["school_id"],
        religionId: json["religion_id"],
        typeBloodId: json["type__blood_id"],
        classroomId: json["classroom_id"],
        busId: json["bus_id"],
        address: json["address"],
        cityName: json["city_name"],
        status: json["status"].toString(),
        tripType: json["trip_type"],
        parentKey: json["parent_key"],
        parentSecret: json["parent_secret"],
        dateBirth: json["Date_Birth"] == null
            ? null
            : DateTime.parse(json["Date_Birth"]),
        logo: json["logo"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        logoPath: json["logo_path"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "phone": phone,
        "grade_id": gradeId,
        "gender_id": genderId,
        "school_id": schoolId,
        "religion_id": religionId,
        "type__blood_id": typeBloodId,
        "classroom_id": classroomId,
        "bus_id": busId,
        "address": address,
        "city_name": cityName,
        "status": status,
        "trip_type": tripType,
        "parent_key": parentKey,
        "parent_secret": parentSecret,
        "Date_Birth":
            "${dateBirth!.year.toString().padLeft(4, '0')}-${dateBirth!.month.toString().padLeft(2, '0')}-${dateBirth!.day.toString().padLeft(2, '0')}",
        "logo": logo,
        "latitude": latitude,
        "longitude": longitude,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "logo_path": logoPath,
      };
}
