import 'dart:convert';

class EndEveningTripModel {
  final Data? data;
  final String? message;
  final bool? errors;
  final bool? status;

  EndEveningTripModel({
    this.data,
    this.message,
    this.errors,
    this.status,
  });

  EndEveningTripModel copyWith({
    Data? data,
    String? message,
    bool? errors,
    bool? status,
  }) =>
      EndEveningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        errors: errors ?? this.errors,
        status: status ?? this.status,
      );

  factory EndEveningTripModel.fromRawJson(String str) => EndEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EndEveningTripModel.fromJson(Map<String, dynamic> json) => EndEveningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    errors: json["errors"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "errors": errors,
    "status": status,
  };
}

class Data {
  final Trip? trip;

  Data({
    this.trip,
  });

  Data copyWith({
    Trip? trip,
  }) =>
      Data(
        trip: trip ?? this.trip,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    trip: json["trip"] == null ? null : Trip.fromJson(json["trip"]),
  );

  Map<String, dynamic> toJson() => {
    "trip": trip?.toJson(),
  };
}

class Trip {
  final int? id;
  final int? schoolId;
  final int? busId;
  final DateTime? tripsDate;
  final String? tripType;
  final int? status;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? attendanceType;

  Trip({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.attendanceType,
  });

  Trip copyWith({
    int? id,
    int? schoolId,
    int? busId,
    DateTime? tripsDate,
    String? tripType,
    int? status,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? attendanceType,
  }) =>
  //currentLocation: GeoPoint{latitude: 31.0539698 , longitude: 31.4013669}
  //currentLocation: GeoPoint{latitude: 31.0539698 , longitude: 31.4013669}
      Trip(
        id: id ?? this.id,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        tripsDate: tripsDate ?? this.tripsDate,
        tripType: tripType ?? this.tripType,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        attendanceType: attendanceType ?? this.attendanceType,
      );

  factory Trip.fromRawJson(String str) => Trip.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
    id: json["id"],
    schoolId: json["school_id"],
    busId: json["bus_id"],
    tripsDate: json["trips_date"] == null ? null : DateTime.parse(json["trips_date"]),
    tripType: json["trip_type"],
    status: json["status"],
    latitude: json["latitude"],
    longitude: json["longitude"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    attendanceType: json["attendance_type"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "school_id": schoolId,
    "bus_id": busId,
    "trips_date": "${tripsDate!.year.toString().padLeft(4, '0')}-${tripsDate!.month.toString().padLeft(2, '0')}-${tripsDate!.day.toString().padLeft(2, '0')}",
    "trip_type": tripType,
    "status": status,
    "latitude": latitude,
    "longitude": longitude,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "attendance_type": attendanceType,
  };
}
