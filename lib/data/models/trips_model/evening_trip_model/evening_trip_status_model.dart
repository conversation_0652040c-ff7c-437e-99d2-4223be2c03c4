import 'dart:convert';

class EveningTripStatusModel {
  final dynamic trip;
  final bool? status;
  final String? message;
  final bool? errors;

  EveningTripStatusModel({
    this.trip,
    this.status,
    this.message,
    this.errors,
  });

// body => {latitude: 31.0539711, longitude: 31.4013756}

// body => {latitude: 31.0539782, longitude: 31.4013695}

  EveningTripStatusModel copyWith({
    dynamic trip,
    bool? status,
    String? message,
    bool? errors,
  }) =>
      EveningTripStatusModel(
        trip: trip ?? this.trip,
        status: status ?? this.status,
        message: message ?? this.message,
        errors: errors ?? this.errors,
      );

  factory EveningTripStatusModel.fromRawJson(String str) =>
      EveningTripStatusModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EveningTripStatusModel.fromJson(Map<String, dynamic> json) =>
      EveningTripStatusModel(
        trip: json["trip"] is List ? [] : Trip.fromJson(json["trip"]),
        status: json["status"],
        message: json["message"],
        errors: json["errors"],
      );

  Map<String, dynamic> toJson() => {
        "trip": trip?.toJson(),
        "status": status,
        "message": message,
        "errors": errors,
      };
}

class Trip {
  final int? id;
  final int? schoolId;
  final int? busId;
  final DateTime? tripsDate;
  final String? tripType;
  var status;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? attendanceType;

  Trip({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.attendanceType,
  });

  Trip copyWith({
    int? id,
    int? schoolId,
    int? busId,
    DateTime? tripsDate,
    String? tripType,
    var status,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? attendanceType,
  }) =>
      Trip(
        id: id ?? this.id,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        tripsDate: tripsDate ?? this.tripsDate,
        tripType: tripType ?? this.tripType,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        attendanceType: attendanceType ?? this.attendanceType,
      );

  factory Trip.fromRawJson(String str) => Trip.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
        id: json["id"],
        schoolId: json["school_id"],
        busId: json["bus_id"],
        tripsDate: json["trips_date"] == null
            ? null
            : DateTime.parse(json["trips_date"]),
        tripType: json["trip_type"],
        status: json["status"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        attendanceType: json["attendance_type"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "school_id": schoolId,
        "bus_id": busId,
        "trips_date":
            "${tripsDate!.year.toString().padLeft(4, '0')}-${tripsDate!.month.toString().padLeft(2, '0')}-${tripsDate!.day.toString().padLeft(2, '0')}",
        "trip_type": tripType,
        "status": status,
        "latitude": latitude,
        "longitude": longitude,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "attendance_type": attendanceType,
      };
}
