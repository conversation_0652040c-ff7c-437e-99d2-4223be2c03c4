import 'dart:convert';

class OnBusEveningTripModel {
  final List<PresentOnBus>? presentOnBus;
  final String? message;
  final bool? trip;
  final bool? errors;

  OnBusEveningTripModel({
    this.presentOnBus,
    this.message,
    this.trip,
    this.errors,
  });

  OnBusEveningTripModel copyWith({
    List<PresentOnBus>? presentOnBus,
    String? message,
    bool? trip,
    bool? errors,
  }) =>
      OnBusEveningTripModel(
        presentOnBus: presentOnBus ?? this.presentOnBus,
        message: message ?? this.message,
        trip: trip ?? this.trip,
        errors: errors ?? this.errors,
      );

  factory OnBusEveningTripModel.fromRawJson(String str) =>
      OnBusEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OnBusEveningTripModel.fromJson(Map<String, dynamic> json) =>
      OnBusEveningTripModel(
        presentOnBus: json["present_on_bus"] == null
            ? []
            : List<PresentOnBus>.from(
                json["present_on_bus"]!.map((x) => PresentOnBus.fromJson(x))),
        message: json["message"],
        trip: json["trip"],
        errors: json["errors"],
      );

  Map<String, dynamic> toJson() => {
        "present_on_bus": presentOnBus == null
            ? []
            : List<dynamic>.from(presentOnBus!.map((x) => x.toJson())),
        "message": message,
        "trip": trip,
        "errors": errors,
      };
}

class PresentOnBus {
  final String? attendanceType;
  final DateTime? attendenceDate;
  final int? tripId;
  final String? studentId;
  final Students? students;

  PresentOnBus({
    this.attendanceType,
    this.attendenceDate,
    this.tripId,
    this.studentId,
    this.students,
  });

  PresentOnBus copyWith({
    String? attendanceType,
    DateTime? attendenceDate,
    int? tripId,
    String? studentId,
    Students? students,
  }) =>
      PresentOnBus(
        attendanceType: attendanceType ?? this.attendanceType,
        attendenceDate: attendenceDate ?? this.attendenceDate,
        tripId: tripId ?? this.tripId,
        studentId: studentId ?? this.studentId,
        students: students ?? this.students,
      );

  factory PresentOnBus.fromRawJson(String str) =>
      PresentOnBus.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PresentOnBus.fromJson(Map<String, dynamic> json) => PresentOnBus(
        attendanceType: json["attendance_type"],
        attendenceDate: json["attendence_date"] == null
            ? null
            : DateTime.parse(json["attendence_date"]),
        tripId: json["trip_id"],
        studentId: json["student_id"],
        students: json["students"] == null
            ? null
            : Students.fromJson(json["students"]),
      );

  Map<String, dynamic> toJson() => {
        "attendance_type": attendanceType,
        "attendence_date":
            "${attendenceDate!.year.toString().padLeft(4, '0')}-${attendenceDate!.month.toString().padLeft(2, '0')}-${attendenceDate!.day.toString().padLeft(2, '0')}",
        "trip_id": tripId,
        "student_id": studentId,
        "students": students?.toJson(),
      };
}

class Students {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final int? typeBloodId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final String? cityName;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final DateTime? dateBirth;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? logoPath;
  final List<MyParent>? myParents;

  Students({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.classroomId,
    this.busId,
    this.address,
    this.cityName,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.dateBirth,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
    this.myParents,
  });

  Students copyWith({
    String? id,
    String? name,
    String? phone,
    int? gradeId,
    int? genderId,
    int? schoolId,
    int? religionId,
    int? typeBloodId,
    int? classroomId,
    int? busId,
    String? address,
    String? cityName,
    int? status,
    String? tripType,
    String? parentKey,
    String? parentSecret,
    DateTime? dateBirth,
    String? logo,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? logoPath,
    List<MyParent>? myParents,
  }) =>
      Students(
        id: id ?? this.id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        gradeId: gradeId ?? this.gradeId,
        genderId: genderId ?? this.genderId,
        schoolId: schoolId ?? this.schoolId,
        religionId: religionId ?? this.religionId,
        typeBloodId: typeBloodId ?? this.typeBloodId,
        classroomId: classroomId ?? this.classroomId,
        busId: busId ?? this.busId,
        address: address ?? this.address,
        cityName: cityName ?? this.cityName,
        status: status ?? this.status,
        tripType: tripType ?? this.tripType,
        parentKey: parentKey ?? this.parentKey,
        parentSecret: parentSecret ?? this.parentSecret,
        dateBirth: dateBirth ?? this.dateBirth,
        logo: logo ?? this.logo,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        logoPath: logoPath ?? this.logoPath,
        myParents: myParents ?? this.myParents,
      );

  factory Students.fromRawJson(String str) =>
      Students.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Students.fromJson(Map<String, dynamic> json) => Students(
        id: json["id"],
        name: json["name"],
        phone: json["phone"],
        gradeId: json["grade_id"],
        genderId: json["gender_id"],
        schoolId: json["school_id"],
        religionId: json["religion_id"],
        typeBloodId: json["type__blood_id"],
        classroomId: json["classroom_id"],
        busId: json["bus_id"],
        address: json["address"],
        cityName: json["city_name"],
        status: json["status"],
        tripType: json["trip_type"],
        parentKey: json["parent_key"],
        parentSecret: json["parent_secret"],
        dateBirth: json["Date_Birth"] == null
            ? null
            : DateTime.parse(json["Date_Birth"]),
        logo: json["logo"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        logoPath: json["logo_path"],
        myParents: json["my__parents"] == null
            ? []
            : List<MyParent>.from(
                json["my__parents"]!.map((x) => MyParent.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "phone": phone,
        "grade_id": gradeId,
        "gender_id": genderId,
        "school_id": schoolId,
        "religion_id": religionId,
        "type__blood_id": typeBloodId,
        "classroom_id": classroomId,
        "bus_id": busId,
        "address": address,
        "city_name": cityName,
        "status": status,
        "trip_type": tripType,
        "parent_key": parentKey,
        "parent_secret": parentSecret,
        "Date_Birth":
            "${dateBirth!.year.toString().padLeft(4, '0')}-${dateBirth!.month.toString().padLeft(2, '0')}-${dateBirth!.day.toString().padLeft(2, '0')}",
        "logo": logo,
        "latitude": latitude,
        "longitude": longitude,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "logo_path": logoPath,
        "my__parents": myParents == null
            ? []
            : List<dynamic>.from(myParents!.map((x) => x.toJson())),
      };
}

class MyParent {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final int? status;
  final String? logo;
  final String? typeAuth;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? firebaseToken;
  final String? logoPath;
  final Pivot? pivot;

  MyParent({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.status,
    this.logo,
    this.typeAuth,
    this.createdAt,
    this.updatedAt,
    this.firebaseToken,
    this.logoPath,
    this.pivot,
  });

  MyParent copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? emailVerifiedAt,
    String? address,
    int? status,
    String? logo,
    String? typeAuth,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? firebaseToken,
    String? logoPath,
    Pivot? pivot,
  }) =>
      MyParent(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
        address: address ?? this.address,
        status: status ?? this.status,
        logo: logo ?? this.logo,
        typeAuth: typeAuth ?? this.typeAuth,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        firebaseToken: firebaseToken ?? this.firebaseToken,
        logoPath: logoPath ?? this.logoPath,
        pivot: pivot ?? this.pivot,
      );

  factory MyParent.fromRawJson(String str) =>
      MyParent.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MyParent.fromJson(Map<String, dynamic> json) => MyParent(
        id: json["id"],
        name: json["name"],
        email: json["email"],
        phone: json["phone"],
        emailVerifiedAt: json["email_verified_at"],
        address: json["address"],
        status: json["status"],
        logo: json["logo"],
        typeAuth: json["typeAuth"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        firebaseToken: json["firebase_token"],
        logoPath: json["logo_path"],
        pivot: json["pivot"] == null ? null : Pivot.fromJson(json["pivot"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "phone": phone,
        "email_verified_at": emailVerifiedAt,
        "address": address,
        "status": status,
        "logo": logo,
        "typeAuth": typeAuth,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "firebase_token": firebaseToken,
        "logo_path": logoPath,
        "pivot": pivot?.toJson(),
      };
}

class Pivot {
  final String? studentId;
  final int? myParentId;

  Pivot({
    this.studentId,
    this.myParentId,
  });

  Pivot copyWith({
    String? studentId,
    int? myParentId,
  }) =>
      Pivot(
        studentId: studentId ?? this.studentId,
        myParentId: myParentId ?? this.myParentId,
      );

  factory Pivot.fromRawJson(String str) => Pivot.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Pivot.fromJson(Map<String, dynamic> json) => Pivot(
        studentId: json["student_id"],
        myParentId: json["my__parent_id"],
      );

  Map<String, dynamic> toJson() => {
        "student_id": studentId,
        "my__parent_id": myParentId,
      };
}
