import 'dart:convert';

class RemoveAbsenceEveningTripModel {
  final Data? data;
  final String? message;
  final bool? status;

  RemoveAbsenceEveningTripModel({
    this.data,
    this.message,
    this.status,
  });

  RemoveAbsenceEveningTripModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      RemoveAbsenceEveningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory RemoveAbsenceEveningTripModel.fromRawJson(String str) => RemoveAbsenceEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RemoveAbsenceEveningTripModel.fromJson(Map<String, dynamic> json) => RemoveAbsenceEveningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "status": status,
  };
}

class Data {
  final String? trip;

  Data({
    this.trip,
  });

  Data copyWith({
    String? trip,
  }) =>
      Data(
        trip: trip ?? this.trip,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    trip: json["trip"],
  );

  Map<String, dynamic> toJson() => {
    "trip": trip,
  };
}
