import 'dart:convert';

class RemoveArrivedEveningTripModel {
  final Data? data;
  final String? message;
  final bool? status;

  RemoveArrivedEveningTripModel({
    this.data,
    this.message,
    this.status,
  });

  RemoveArrivedEveningTripModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      RemoveArrivedEveningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory RemoveArrivedEveningTripModel.fromRawJson(String str) => RemoveArrivedEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RemoveArrivedEveningTripModel.fromJson(Map<String, dynamic> json) => RemoveArrivedEveningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "status": status,
  };
}

class Data {
  final String? message;
  final String? removeAbsence;

  Data({
    this.message,
    this.removeAbsence,
  });

  Data copyWith({
    String? message,
    String? removeAbsence,
  }) =>
      Data(
        message: message ?? this.message,
        removeAbsence: removeAbsence ?? this.removeAbsence,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    message: json["message"],
    removeAbsence: json["removeAbsence"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "removeAbsence": removeAbsence,
  };
}
