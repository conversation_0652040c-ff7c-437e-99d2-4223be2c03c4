import 'dart:convert';

class SendMessageEveningTripModel {
  final bool? errors;
  final String? message;

  SendMessageEveningTripModel({
    this.errors,
    this.message,
  });

  SendMessageEveningTripModel copyWith({
    bool? errors,
    String? message,
  }) =>
      SendMessageEveningTripModel(
        errors: errors ?? this.errors,
        message: message ?? this.message,
      );

  factory SendMessageEveningTripModel.fromRawJson(String str) => SendMessageEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SendMessageEveningTripModel.fromJson(Map<String, dynamic> json) => SendMessageEveningTripModel(
    errors: json["errors"],
    message: json["message"],
  );

  Map<String, dynamic> toJson() => {
    "errors": errors,
    "message": message,
  };
}
