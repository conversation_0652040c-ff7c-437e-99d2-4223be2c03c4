import 'dart:convert';

class SendMessageForAllEveningTripModel {
  final bool? errors;
  final String? message;

  SendMessageForAllEveningTripModel({
    this.errors,
    this.message,
  });

  SendMessageForAllEveningTripModel copyWith({
    bool? errors,
    String? message,
  }) =>
      SendMessageForAllEveningTripModel(
        errors: errors ?? this.errors,
        message: message ?? this.message,
      );

  factory SendMessageForAllEveningTripModel.fromRawJson(String str) => SendMessageForAllEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SendMessageForAllEveningTripModel.fromJson(Map<String, dynamic> json) => SendMessageForAllEveningTripModel(
    errors: json["errors"],
    message: json["message"],
  );

  Map<String, dynamic> toJson() => {
    "errors": errors,
    "message": message,
  };
}
