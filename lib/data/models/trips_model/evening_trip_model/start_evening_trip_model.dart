import 'dart:convert';

class StartEveningTripModel {
  final Data? data;
  final String? message;
  final bool? status;

  StartEveningTripModel({
    this.data,
    this.message,
    this.status,
  });

  StartEveningTripModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      StartEveningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory StartEveningTripModel.fromRawJson(String str) => StartEveningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StartEveningTripModel.fromJson(Map<String, dynamic> json) => StartEveningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "status": status,
  };
}

class Data {
  final Trip? trip;
  final List<Attendance>? attendance;

  Data({
    this.trip,
    this.attendance,
  });

  Data copyWith({
    Trip? trip,
    List<Attendance>? attendance,
  }) =>
      Data(
        trip: trip ?? this.trip,
        attendance: attendance ?? this.attendance,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    trip: json["trip"] == null ? null : Trip.fromJson(json["trip"]),
    attendance: json["attendance"] == null ? [] : List<Attendance>.from(json["attendance"]!.map((x) => Attendance.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "trip": trip?.toJson(),
    "attendance": attendance == null ? [] : List<dynamic>.from(attendance!.map((x) => x.toJson())),
  };
}

class Attendance {
  final String? id;
  final String? name;
  final String? longitude;
  final String? latitude;
  final TripType? tripType;
  final int? schoolId;
  final int? busId;
  final dynamic logoPath;

  Attendance({
    this.id,
    this.name,
    this.longitude,
    this.latitude,
    this.tripType,
    this.schoolId,
    this.busId,
    this.logoPath,
  });

  Attendance copyWith({
    String? id,
    String? name,
    String? longitude,
    String? latitude,
    TripType? tripType,
    int? schoolId,
    int? busId,
    dynamic logoPath,
  }) =>
      Attendance(
        id: id ?? this.id,
        name: name ?? this.name,
        longitude: longitude ?? this.longitude,
        latitude: latitude ?? this.latitude,
        tripType: tripType ?? this.tripType,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        logoPath: logoPath ?? this.logoPath,
      );

  factory Attendance.fromRawJson(String str) => Attendance.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Attendance.fromJson(Map<String, dynamic> json) => Attendance(
    id: json["id"],
    name: json["name"],
    longitude: json["longitude"],
    latitude: json["latitude"],
    tripType: tripTypeValues.map[json["trip_type"]],
    schoolId: json["school_id"],
    busId: json["bus_id"],
    logoPath: json["logo_path"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "longitude": longitude,
    "latitude": latitude,
    "trip_type": tripTypeValues.reverse[tripType],
    "school_id": schoolId,
    "bus_id": busId,
    "logo_path": logoPath,
  };
}

enum TripType {
  FULL_DAY
}

final tripTypeValues = EnumValues({
  "full_day": TripType.FULL_DAY
});

class Trip {
  final String? latitude;
  final String? longitude;
  final int? busId;
  final int? schoolId;
  final DateTime? tripsDate;
  final String? tripType;
  final DateTime? updatedAt;
  final DateTime? createdAt;
  final int? id;

  Trip({
    this.latitude,
    this.longitude,
    this.busId,
    this.schoolId,
    this.tripsDate,
    this.tripType,
    this.updatedAt,
    this.createdAt,
    this.id,
  });

  Trip copyWith({
    String? latitude,
    String? longitude,
    int? busId,
    int? schoolId,
    DateTime? tripsDate,
    String? tripType,
    DateTime? updatedAt,
    DateTime? createdAt,
    int? id,
  }) =>
      Trip(
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        busId: busId ?? this.busId,
        schoolId: schoolId ?? this.schoolId,
        tripsDate: tripsDate ?? this.tripsDate,
        tripType: tripType ?? this.tripType,
        updatedAt: updatedAt ?? this.updatedAt,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
      );

  factory Trip.fromRawJson(String str) => Trip.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
    latitude: json["latitude"],
    longitude: json["longitude"],
    busId: json["bus_id"],
    schoolId: json["school_id"],
    tripsDate: json["trips_date"] == null ? null : DateTime.parse(json["trips_date"]),
    tripType: json["trip_type"],
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "latitude": latitude,
    "longitude": longitude,
    "bus_id": busId,
    "school_id": schoolId,
    "trips_date": "${tripsDate!.year.toString().padLeft(4, '0')}-${tripsDate!.month.toString().padLeft(2, '0')}-${tripsDate!.day.toString().padLeft(2, '0')}",
    "trip_type": tripType,
    "updated_at": updatedAt?.toIso8601String(),
    "created_at": createdAt?.toIso8601String(),
    "id": id,
  };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
