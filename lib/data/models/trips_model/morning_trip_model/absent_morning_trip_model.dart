import 'dart:convert';

class AbsentMorningTripModel {
  final List<Absence>? absences;
  final String? message;
  final bool? errors;

  AbsentMorningTripModel({
    this.absences,
    this.message,
    this.errors,
  });

  AbsentMorningTripModel copyWith({
    List<Absence>? absences,
    String? message,
    bool? errors,
  }) =>
      AbsentMorningTripModel(
        absences: absences ?? this.absences,
        message: message ?? this.message,
        errors: errors ?? this.errors,
      );

  factory AbsentMorningTripModel.fromRawJson(String str) => AbsentMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AbsentMorningTripModel.fromJson(Map<String, dynamic> json) => AbsentMorningTripModel(
    absences: json["absences"] == null ? [] : List<Absence>.from(json["absences"]!.map((x) => Absence.from<PERSON><PERSON>(x))),
    message: json["message"],
    errors: json["errors"],
  );

  Map<String, dynamic> toJson() => {
    "absences": absences == null ? [] : List<dynamic>.from(absences!.map((x) => x.toJson())),
    "message": message,
    "errors": errors,
  };
}

class Absence {
  final String? id;
  final String? name;
  final int? busId;
  final dynamic logoPath;

  Absence({
    this.id,
    this.name,
    this.busId,
    this.logoPath,
  });

  Absence copyWith({
    String? id,
    String? name,
    int? busId,
    dynamic logoPath,
  }) =>
      Absence(
        id: id ?? this.id,
        name: name ?? this.name,
        busId: busId ?? this.busId,
        logoPath: logoPath ?? this.logoPath,
      );

  factory Absence.fromRawJson(String str) => Absence.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Absence.fromJson(Map<String, dynamic> json) => Absence(
    id: json["id"],
    name: json["name"],
    busId: json["bus_id"],
    logoPath: json["logo_path"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "bus_id": busId,
    "logo_path": logoPath,
  };
}
