class GroupAndSingleMessageModel {
  Data? data;
  String? message;
  bool? status;

  GroupAndSingleMessageModel({this.data, this.message, this.status});

  GroupAndSingleMessageModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    message = json['message'];
    status = json['status'];
  }

}

class Data {
  List<Group>? group;
  List<Single>? single;

  Data({this.group, this.single});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['group'] != null) {
      group = <Group>[];
      json['group'].forEach((v) {
        group!.add(new Group.fromJson(v));
      });
    }
    if (json['single'] != null) {
      single = <Single>[];
      json['single'].forEach((v) {
        single!.add(new Single.fromJson(v));
      });
    }
  }

}

class Single {
  int? id;
  String? title;
  String? body;
  int? group;

  Single({this.id, this.title, this.body, this.group});

  Single.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    body = json['body'];
    group = json['group'];
  }

}


class Group {
  int? id;
  String? title;
  String? body;
  int? group;

  Group({this.id, this.title, this.body, this.group});

  Group.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    body = json['body'];
    group = json['group'];
  }

}