import 'dart:convert';

class MorningTripStatusModel {
  final dynamic trip;
  final bool? status;
  final String? message;
  final bool? errors;

  MorningTripStatusModel({
    this.trip,
    this.status,
    this.message,
    this.errors,
  });

  MorningTripStatusModel copyWith({
    dynamic trip,
    bool? status,
    String? message,
    bool? errors,
  }) =>
      MorningTripStatusModel(
        trip: trip ?? this.trip,
        status: status ?? this.status,
        message: message ?? this.message,
        errors: errors ?? this.errors,
      );

  factory MorningTripStatusModel.fromRawJson(String str) => MorningTripStatusModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MorningTripStatusModel.fromJson(Map<String, dynamic> json) => MorningTripStatusModel(
    trip: json["trip"] is List ? [] : Trip.fromJson(json["trip"]),
    status: json["status"],
    message: json["message"],
    errors: json["errors"],
  );

  Map<String, dynamic> toJson() => {
    "trip": trip?.toJson(),
    "status": status,
    "message": message,
    "errors": errors,
  };
}

class Trip {
  final int? id;
  final int? schoolId;
  final int? busId;
  final DateTime? tripsDate;
  final String? tripType;
  final int? status;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? attendanceType;
  List<Creator>? creator;
  // final  List<Creator>? creator;
  

  Trip({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.attendanceType,
    this.creator
  });

  Trip copyWith({
    int? id,
    int? schoolId,
    int? busId,
    DateTime? tripsDate,
    String? tripType,
    int? status,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? attendanceType,
       List<Creator>? creator,

  }) =>
      Trip(
        id: id ?? this.id,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        tripsDate: tripsDate ?? this.tripsDate,
        tripType: tripType ?? this.tripType,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        attendanceType: attendanceType ?? this.attendanceType,
        creator: creator ,

      );

  factory Trip.fromRawJson(String str) => Trip.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
    id: json["id"],
    schoolId: json["school_id"],
    busId: json["bus_id"],
    tripsDate: json["trips_date"] == null ? null : DateTime.parse(json["trips_date"]),
    tripType: json["trip_type"],
    status: json["status"],
    latitude: json["latitude"],
    longitude: json["longitude"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    attendanceType: json["attendance_type"],
    // creator: json["creator"],

  

  //    if (json['creator'] != null) {
  //     creator = <Creator>[];
  //     json['creator'].forEach((v) {
  //       creator!.add(new Creator.fromJson(v));
  // })
  // }
    // creator: json["creator"],
    creator: json["creator"] == null ? [] : List<Creator>.from(json["creator"]!.map((x) => Creator.fromJson(x))),


  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "school_id": schoolId,
    "bus_id": busId,
    "trips_date": "${tripsDate!.year.toString().padLeft(4, '0')}-${tripsDate!.month.toString().padLeft(2, '0')}-${tripsDate!.day.toString().padLeft(2, '0')}",
    "trip_type": tripType,
    "status": status,
    "latitude": latitude,
    "longitude": longitude,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "attendance_type": attendanceType,
    // "creator": creator,

    
    "creator": creator == null ? [] : List<dynamic>.from(creator!.map((x) => x.toJson())),
  };




}

class Creator {
  int? id;
  String? userableType;
  int? userableId;
  String? creatableType;
  int? creatableId;
  String? createdAt;
  String? updatedAt;

  Creator(
      {this.id,
      this.userableType,
      this.userableId,
      this.creatableType,
      this.creatableId,
      this.createdAt,
      this.updatedAt});

  Creator.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userableType = json['userable_type'];
    userableId = json['userable_id'];
    creatableType = json['creatable_type'];
    creatableId = json['creatable_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userable_type'] = this.userableType;
    data['userable_id'] = this.userableId;
    data['creatable_type'] = this.creatableType;
    data['creatable_id'] = this.creatableId;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

// class Creator {
//   final int? id;
//  final String? userableType;
//  final int? userableId;
// final  String? creatableType;

//   Creator({
//     this.id,
//     this.userableType,
//     this.userableId
// ,
//     this.creatableType,
//   });

//   Creator copyWith({
//     int? id,
//   String? userableType,
//   int? userableId,
//   String? creatableType,
//   }) =>
//       Creator(
//         id: id ?? this.id,
//         userableType: userableType ?? this.userableType,
//         userableId: userableId ?? this.userableId,

//         creatableType: creatableType ?? this.creatableType,

//       );

//   factory Creator.fromRawJson(String str) => Creator.fromJson(json.decode(str));

//   String toRawJson() => json.encode(toJson());

//   factory Creator.fromJson(Map<String, dynamic> json) => Creator(
//     id: json["id"],
//     userableType: json["userable_type"],
//     userableId: json["userable_id"],

//     creatableType: json["creatable_type"],
//   );

//   Map<String, dynamic> toJson() => {
//     "id": id,
//     "userable_type": userableType,
//     "userable_id":userableId
// ,
//     "creatable_type":creatableType,

//   };
// }

