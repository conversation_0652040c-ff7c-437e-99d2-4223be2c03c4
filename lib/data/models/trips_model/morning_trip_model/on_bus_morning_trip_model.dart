import 'dart:convert';

class OnBusMorningTripModel {
  final List<PresentOnBus>? presentOnBus;
  final bool? trip;
  final Data? data;
  final String? message;
  final bool? errors;

  OnBusMorningTripModel({
    this.presentOnBus,
    this.trip,
    this.data,
    this.message,
    this.errors,
  });

  OnBusMorningTripModel copyWith({
    List<PresentOnBus>? presentOnBus,
    bool? trip,
    Data? data,
    String? message,
    bool? errors,
  }) =>
      OnBusMorningTripModel(
        presentOnBus: presentOnBus ?? this.presentOnBus,
        trip: trip ?? this.trip,
        data: data ?? this.data,
        message: message ?? this.message,
        errors: errors ?? this.errors,
      );

  factory OnBusMorningTripModel.fromRawJson(String str) => OnBusMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OnBusMorningTripModel.fromJson(Map<String, dynamic> json) => OnBusMorningTripModel(
    presentOnBus: json["present_on_bus"] == null ? [] : List<PresentOnBus>.from(json["present_on_bus"]!.map((x) => PresentOnBus.fromJson(x))),
    trip: json["trip"],
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    errors: json["errors"],
  );

  Map<String, dynamic> toJson() => {
    "present_on_bus": presentOnBus == null ? [] : List<dynamic>.from(presentOnBus!.map((x) => x.toJson())),
    "trip": trip,
    "data": data?.toJson(),
    "message": message,
    "errors": errors,
  };
}

class Data {
  final int? id;
  final int? schoolId;
  final int? busId;
  final DateTime? tripsDate;
  final String? tripType;
  final int? status;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? attendanceType;

  Data({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.attendanceType,
  });

  Data copyWith({
    int? id,
    int? schoolId,
    int? busId,
    DateTime? tripsDate,
    String? tripType,
    int? status,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? attendanceType,
  }) =>
      Data(
        id: id ?? this.id,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        tripsDate: tripsDate ?? this.tripsDate,
        tripType: tripType ?? this.tripType,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        attendanceType: attendanceType ?? this.attendanceType,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    id: json["id"],
    schoolId: json["school_id"],
    busId: json["bus_id"],
    tripsDate: json["trips_date"] == null ? null : DateTime.parse(json["trips_date"]),
    tripType: json["trip_type"],
    status: json["status"],
    latitude: json["latitude"],
    longitude: json["longitude"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    attendanceType: json["attendance_type"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "school_id": schoolId,
    "bus_id": busId,
    "trips_date": "${tripsDate!.year.toString().padLeft(4, '0')}-${tripsDate!.month.toString().padLeft(2, '0')}-${tripsDate!.day.toString().padLeft(2, '0')}",
    "trip_type": tripType,
    "status": status,
    "latitude": latitude,
    "longitude": longitude,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "attendance_type": attendanceType,
  };
}

class PresentOnBus {
  final String? id;
  final String? name;
  final String? phone;
  final int? gradeId;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final int? typeBloodId;
  final int? classroomId;
  final int? busId;
  final String? address;
  final String? cityName;
  final int? status;
  final String? tripType;
  final String? parentKey;
  final String? parentSecret;
  final DateTime? dateBirth;
  final String? logo;
  final String? latitude;
  final String? longitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? logoPath;

  PresentOnBus({
    this.id,
    this.name,
    this.phone,
    this.gradeId,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.classroomId,
    this.busId,
    this.address,
    this.cityName,
    this.status,
    this.tripType,
    this.parentKey,
    this.parentSecret,
    this.dateBirth,
    this.logo,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.logoPath,
  });

  PresentOnBus copyWith({
    String? id,
    String? name,
    String? phone,
    int? gradeId,
    int? genderId,
    int? schoolId,
    int? religionId,
    int? typeBloodId,
    int? classroomId,
    int? busId,
    String? address,
    String? cityName,
    int? status,
    String? tripType,
    String? parentKey,
    String? parentSecret,
    DateTime? dateBirth,
    String? logo,
    String? latitude,
    String? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? logoPath,
  }) =>
      PresentOnBus(
        id: id ?? this.id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        gradeId: gradeId ?? this.gradeId,
        genderId: genderId ?? this.genderId,
        schoolId: schoolId ?? this.schoolId,
        religionId: religionId ?? this.religionId,
        typeBloodId: typeBloodId ?? this.typeBloodId,
        classroomId: classroomId ?? this.classroomId,
        busId: busId ?? this.busId,
        address: address ?? this.address,
        cityName: cityName ?? this.cityName,
        status: status ?? this.status,
        tripType: tripType ?? this.tripType,
        parentKey: parentKey ?? this.parentKey,
        parentSecret: parentSecret ?? this.parentSecret,
        dateBirth: dateBirth ?? this.dateBirth,
        logo: logo ?? this.logo,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        logoPath: logoPath ?? this.logoPath,
      );

  factory PresentOnBus.fromRawJson(String str) => PresentOnBus.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PresentOnBus.fromJson(Map<String, dynamic> json) => PresentOnBus(
    id: json["id"],
    name: json["name"],
    phone: json["phone"],
    gradeId: json["grade_id"],
    genderId: json["gender_id"],
    schoolId: json["school_id"],
    religionId: json["religion_id"],
    typeBloodId: json["type__blood_id"],
    classroomId: json["classroom_id"],
    busId: json["bus_id"],
    address: json["address"],
    cityName: json["city_name"],
    status: json["status"],
    tripType: json["trip_type"],
    parentKey: json["parent_key"],
    parentSecret: json["parent_secret"],
    dateBirth: json["Date_Birth"] == null ? null : DateTime.parse(json["Date_Birth"]),
    logo: json["logo"],
    latitude: json["latitude"],
    longitude: json["longitude"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    logoPath: json["logo_path"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "phone": phone,
    "grade_id": gradeId,
    "gender_id": genderId,
    "school_id": schoolId,
    "religion_id": religionId,
    "type__blood_id": typeBloodId,
    "classroom_id": classroomId,
    "bus_id": busId,
    "address": address,
    "city_name": cityName,
    "status": status,
    "trip_type": tripType,
    "parent_key": parentKey,
    "parent_secret": parentSecret,
    "Date_Birth": "${dateBirth!.year.toString().padLeft(4, '0')}-${dateBirth!.month.toString().padLeft(2, '0')}-${dateBirth!.day.toString().padLeft(2, '0')}",
    "logo": logo,
    "latitude": latitude,
    "longitude": longitude,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "logo_path": logoPath,
  };
}
