import 'dart:convert';

class PresentOnBusMorningTripModel {
  final Data? data;
  final String? message;
  final bool? status;

  PresentOnBusMorningTripModel({
    this.data,
    this.message,
    this.status,
  });

  PresentOnBusMorningTripModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      PresentOnBusMorningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory PresentOnBusMorningTripModel.fromRawJson(String str) => PresentOnBusMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PresentOnBusMorningTripModel.fromJson(Map<String, dynamic> json) => PresentOnBusMorningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "status": status,
  };
}

class Data {
  final Attendance? attendance;
  final String? removeAbsence;

  Data({
    this.attendance,
    this.removeAbsence,
  });

  Data copyWith({
    Attendance? attendance,
    String? removeAbsence,
  }) =>
      Data(
        attendance: attendance ?? this.attendance,
        removeAbsence: removeAbsence ?? this.removeAbsence,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    attendance: json["attendance"] == null ? null : Attendance.fromJson(json["attendance"]),
    removeAbsence: json["removeAbsence"],
  );

  Map<String, dynamic> toJson() => {
    "attendance": attendance?.toJson(),
    "removeAbsence": removeAbsence,
  };
}

class Attendance {
  final int? schoolId;
  final int? studentId;
  final DateTime? attendenceDate;
  final int? tripId;
  final int? attendenceStatus;
  final String? attendanceType;
  final DateTime? updatedAt;
  final DateTime? createdAt;
  final int? id;

  Attendance({
    this.schoolId,
    this.studentId,
    this.attendenceDate,
    this.tripId,
    this.attendenceStatus,
    this.attendanceType,
    this.updatedAt,
    this.createdAt,
    this.id,
  });

  Attendance copyWith({
    int? schoolId,
    int? studentId,
    DateTime? attendenceDate,
    int? tripId,
    int? attendenceStatus,
    String? attendanceType,
    DateTime? updatedAt,
    DateTime? createdAt,
    int? id,
  }) =>
      Attendance(
        schoolId: schoolId ?? this.schoolId,
        studentId: studentId ?? this.studentId,
        attendenceDate: attendenceDate ?? this.attendenceDate,
        tripId: tripId ?? this.tripId,
        attendenceStatus: attendenceStatus ?? this.attendenceStatus,
        attendanceType: attendanceType ?? this.attendanceType,
        updatedAt: updatedAt ?? this.updatedAt,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
      );

  factory Attendance.fromRawJson(String str) => Attendance.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Attendance.fromJson(Map<String, dynamic> json) => Attendance(
    schoolId: json["school_id"],
    studentId: json["student_id"],
    attendenceDate: json["attendence_date"] == null ? null : DateTime.parse(json["attendence_date"]),
    tripId: json["trip_id"],
    attendenceStatus: json["attendence_status"],
    attendanceType: json["attendance_type"],
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "school_id": schoolId,
    "student_id": studentId,
    "attendence_date": "${attendenceDate!.year.toString().padLeft(4, '0')}-${attendenceDate!.month.toString().padLeft(2, '0')}-${attendenceDate!.day.toString().padLeft(2, '0')}",
    "trip_id": tripId,
    "attendence_status": attendenceStatus,
    "attendance_type": attendanceType,
    "updated_at": updatedAt?.toIso8601String(),
    "created_at": createdAt?.toIso8601String(),
    "id": id,
  };
}
