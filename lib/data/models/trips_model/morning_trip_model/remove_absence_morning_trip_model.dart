import 'dart:convert';

class RemoveAbsenceMorningTripModel {
  final Data? data;
  final String? message;
  final bool? status;

  RemoveAbsenceMorningTripModel({
    this.data,
    this.message,
    this.status,
  });

  RemoveAbsenceMorningTripModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      RemoveAbsenceMorningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory RemoveAbsenceMorningTripModel.fromRawJson(String str) => RemoveAbsenceMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RemoveAbsenceMorningTripModel.fromJson(Map<String, dynamic> json) => RemoveAbsenceMorningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "status": status,
  };
}

class Data {
  final Message? message;
  final String? trip;

  Data({
    this.message,
    this.trip,
  });

  Data copyWith({
    Message? message,
    String? trip,
  }) =>
      Data(
        message: message ?? this.message,
        trip: trip ?? this.trip,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    message: json["message"] == null ? null : Message.fromJson(json["message"]),
    trip: json["trip"],
  );

  Map<String, dynamic> toJson() => {
    "message": message?.toJson(),
    "trip": trip,
  };
}

class Message {
  final int? id;
  final int? schoolId;
  final int? busId;
  final int? myParentId;
  final int? studentId;
  final DateTime? attendenceDate;
  final String? attendenceType;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final dynamic updatedBy;

  Message({
    this.id,
    this.schoolId,
    this.busId,
    this.myParentId,
    this.studentId,
    this.attendenceDate,
    this.attendenceType,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  Message copyWith({
    int? id,
    int? schoolId,
    int? busId,
    int? myParentId,
    int? studentId,
    DateTime? attendenceDate,
    String? attendenceType,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    dynamic updatedBy,
  }) =>
      Message(
        id: id ?? this.id,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        myParentId: myParentId ?? this.myParentId,
        studentId: studentId ?? this.studentId,
        attendenceDate: attendenceDate ?? this.attendenceDate,
        attendenceType: attendenceType ?? this.attendenceType,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory Message.fromRawJson(String str) => Message.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Message.fromJson(Map<String, dynamic> json) => Message(
    id: json["id"],
    schoolId: json["school_id"],
    busId: json["bus_id"],
    myParentId: json["my__parent_id"],
    studentId: json["student_id"],
    attendenceDate: json["attendence_date"] == null ? null : DateTime.parse(json["attendence_date"]),
    attendenceType: json["attendence_type"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    createdBy: json["created_by"],
    updatedBy: json["updated_by"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "school_id": schoolId,
    "bus_id": busId,
    "my__parent_id": myParentId,
    "student_id": studentId,
    "attendence_date": "${attendenceDate!.year.toString().padLeft(4, '0')}-${attendenceDate!.month.toString().padLeft(2, '0')}-${attendenceDate!.day.toString().padLeft(2, '0')}",
    "attendence_type": attendenceType,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "created_by": createdBy,
    "updated_by": updatedBy,
  };
}
