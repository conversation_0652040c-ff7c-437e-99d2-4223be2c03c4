import 'dart:convert';

class RemovePresentOnBusMorningTripModel {
  final Data? data;
  final String? message;
  final bool? status;

  RemovePresentOnBusMorningTripModel({
    this.data,
    this.message,
    this.status,
  });

  RemovePresentOnBusMorningTripModel copyWith({
    Data? data,
    String? message,
    bool? status,
  }) =>
      RemovePresentOnBusMorningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory RemovePresentOnBusMorningTripModel.fromRawJson(String str) => RemovePresentOnBusMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RemovePresentOnBusMorningTripModel.fromJson(Map<String, dynamic> json) => RemovePresentOnBusMorningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "status": status,
  };
}

class Data {
  final String? trip;

  Data({
    this.trip,
  });

  Data copyWith({
    String? trip,
  }) =>
      Data(
        trip: trip ?? this.trip,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    trip: json["trip"],
  );

  Map<String, dynamic> toJson() => {
    "trip": trip,
  };
}
