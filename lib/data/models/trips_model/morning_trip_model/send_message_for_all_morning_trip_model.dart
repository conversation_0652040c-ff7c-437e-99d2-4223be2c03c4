import 'dart:convert';

class SendMessageForAllMorningTripModel {
  final bool? errors;
  final String? message;

  SendMessageForAllMorningTripModel({
    this.errors,
    this.message,
  });

  SendMessageForAllMorningTripModel copyWith({
    bool? errors,
    String? message,
  }) =>
      SendMessageForAllMorningTripModel(
        errors: errors ?? this.errors,
        message: message ?? this.message,
      );

  factory SendMessageForAllMorningTripModel.fromRawJson(String str) => SendMessageForAllMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SendMessageForAllMorningTripModel.fromJson(Map<String, dynamic> json) => SendMessageForAllMorningTripModel(
    errors: json["errors"],
    message: json["message"],
  );

  Map<String, dynamic> toJson() => {
    "errors": errors,
    "message": message,
  };
}
