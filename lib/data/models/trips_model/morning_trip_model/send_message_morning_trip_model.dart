import 'dart:convert';

class SendMessageMorningTripModel {
  final bool? errors;
  final String? message;

  SendMessageMorningTripModel({
    this.errors,
    this.message,
  });

  SendMessageMorningTripModel copyWith({
    bool? errors,
    String? message,
  }) =>
      SendMessageMorningTripModel(
        errors: errors ?? this.errors,
        message: message ?? this.message,
      );

  factory SendMessageMorningTripModel.fromRawJson(String str) => SendMessageMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SendMessageMorningTripModel.fromJson(Map<String, dynamic> json) => SendMessageMorningTripModel(
    errors: json["errors"],
    message: json["message"],
  );

  Map<String, dynamic> toJson() => {
    "errors": errors,
    "message": message,
  };
}
