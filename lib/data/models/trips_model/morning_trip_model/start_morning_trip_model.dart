import 'dart:convert';

class StartMorningTripModel {
  final Data? data;
  final String? message;
  final String? messages;
  final bool? status;
  final bool? errors;

  StartMorningTripModel({
    this.data,
    this.message,
    this.messages,
    this.status,
    this.errors,
  });

  StartMorningTripModel copyWith({
    Data? data,
    String? message,
    String? messages,
    bool? status,
    bool? errors,
  }) =>
      StartMorningTripModel(
        data: data ?? this.data,
        message: message ?? this.message,
        messages: messages ?? this.messages,
        status: status ?? this.status,
        errors: errors ?? this.errors,
      );

  factory StartMorningTripModel.fromRawJson(String str) => StartMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StartMorningTripModel.fromJson(Map<String, dynamic> json) => StartMorningTripModel(
    data: json["data"] == null ? null : Data.fromJson(json["data"]),
    message: json["message"],
    messages: json["messages"],
    status: json["status"],
    errors: json["errors"],
  );

  Map<String, dynamic> toJson() => {
    "data": data?.toJson(),
    "message": message,
    "messages": messages,
    "status": status,
    "errors": errors,
  };
}

class Data {
  final Trip? trip;

  Data({
    this.trip,
  });

  Data copyWith({
    Trip? trip,
  }) =>
      Data(
        trip: trip ?? this.trip,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    trip: json["trip"] == null ? null : Trip.fromJson(json["trip"]),
  );

  Map<String, dynamic> toJson() => {
    "trip": trip?.toJson(),
  };
}

class Trip {
  final String? latitude;
  final String? longitude;
  final int? busId;
  final int? schoolId;
  final DateTime? tripsDate;
  final String? tripType;
  final DateTime? updatedAt;
  final DateTime? createdAt;
  final int? id;

  Trip({
    this.latitude,
    this.longitude,
    this.busId,
    this.schoolId,
    this.tripsDate,
    this.tripType,
    this.updatedAt,
    this.createdAt,
    this.id,
  });

  Trip copyWith({
    String? latitude,
    String? longitude,
    int? busId,
    int? schoolId,
    DateTime? tripsDate,
    String? tripType,
    DateTime? updatedAt,
    DateTime? createdAt,
    int? id,
  }) =>
      Trip(
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        busId: busId ?? this.busId,
        schoolId: schoolId ?? this.schoolId,
        tripsDate: tripsDate ?? this.tripsDate,
        tripType: tripType ?? this.tripType,
        updatedAt: updatedAt ?? this.updatedAt,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
      );

  factory Trip.fromRawJson(String str) => Trip.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
    latitude: json["latitude"],
    longitude: json["longitude"],
    busId: json["bus_id"],
    schoolId: json["school_id"],
    tripsDate: json["trips_date"] == null ? null : DateTime.parse(json["trips_date"]),
    tripType: json["trip_type"],
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "latitude": latitude,
    "longitude": longitude,
    "bus_id": busId,
    "school_id": schoolId,
    "trips_date": "${tripsDate!.year.toString().padLeft(4, '0')}-${tripsDate!.month.toString().padLeft(2, '0')}-${tripsDate!.day.toString().padLeft(2, '0')}",
    "trip_type": tripType,
    "updated_at": updatedAt?.toIso8601String(),
    "created_at": createdAt?.toIso8601String(),
    "id": id,
  };
}
