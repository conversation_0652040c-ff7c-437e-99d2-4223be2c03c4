import 'dart:convert';

class StudentAbsentMorningTripModel {
  final bool? errors;
  final Message? message;

  StudentAbsentMorningTripModel({
    this.errors,
    this.message,
  });

  StudentAbsentMorningTripModel copyWith({
    bool? errors,
    Message? message,
  }) =>
      StudentAbsentMorningTripModel(
        errors: errors ?? this.errors,
        message: message ?? this.message,
      );

  factory StudentAbsentMorningTripModel.fromRawJson(String str) => StudentAbsentMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StudentAbsentMorningTripModel.fromJson(Map<String, dynamic> json) => StudentAbsentMorningTripModel(
    errors: json["errors"],
    message: json["message"] == null ? null : Message.fromJson(json["message"]),
  );

  Map<String, dynamic> toJson() => {
    "errors": errors,
    "message": message?.toJson(),
  };
}

class Message {
  final int? schoolId;
  final int? busId;
  final int? myParentId;
  final int? studentId;
  final DateTime? attendenceDate;
  final String? attendenceType;
  final String? createdBy;
  final DateTime? updatedAt;
  final DateTime? createdAt;
  final int? id;

  Message({
    this.schoolId,
    this.busId,
    this.myParentId,
    this.studentId,
    this.attendenceDate,
    this.attendenceType,
    this.createdBy,
    this.updatedAt,
    this.createdAt,
    this.id,
  });

  Message copyWith({
    int? schoolId,
    int? busId,
    int? myParentId,
    int? studentId,
    DateTime? attendenceDate,
    String? attendenceType,
    String? createdBy,
    DateTime? updatedAt,
    DateTime? createdAt,
    int? id,
  }) =>
      Message(
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        myParentId: myParentId ?? this.myParentId,
        studentId: studentId ?? this.studentId,
        attendenceDate: attendenceDate ?? this.attendenceDate,
        attendenceType: attendenceType ?? this.attendenceType,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
      );

  factory Message.fromRawJson(String str) => Message.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Message.fromJson(Map<String, dynamic> json) => Message(
    schoolId: json["school_id"],
    busId: json["bus_id"],
    myParentId: json["my__parent_id"],
    studentId: json["student_id"],
    attendenceDate: json["attendence_date"] == null ? null : DateTime.parse(json["attendence_date"]),
    attendenceType: json["attendence_type"],
    createdBy: json["created_by"],
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "school_id": schoolId,
    "bus_id": busId,
    "my__parent_id": myParentId,
    "student_id": studentId,
    "attendence_date": "${attendenceDate!.year.toString().padLeft(4, '0')}-${attendenceDate!.month.toString().padLeft(2, '0')}-${attendenceDate!.day.toString().padLeft(2, '0')}",
    "attendence_type": attendenceType,
    "created_by": createdBy,
    "updated_at": updatedAt?.toIso8601String(),
    "created_at": createdAt?.toIso8601String(),
    "id": id,
  };
}
