import 'dart:convert';

class WaitingMorningTripModel {
  final List<Waiting>? waiting;
  final String? message;
  final bool? status;

  WaitingMorningTripModel({
    this.waiting,
    this.message,
    this.status,
  });

  WaitingMorningTripModel copyWith({
    List<Waiting>? waiting,
    String? message,
    bool? status,
  }) =>
      WaitingMorningTripModel(
        waiting: waiting ?? this.waiting,
        message: message ?? this.message,
        status: status ?? this.status,
      );

  factory WaitingMorningTripModel.fromRawJson(String str) =>
      WaitingMorningTripModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory WaitingMorningTripModel.fromJson(Map<String, dynamic> json) =>
      WaitingMorningTripModel(
        waiting: json["waiting"] == null
            ? []
            : List<Waiting>.from(
                json["waiting"]!.map((x) => Waiting.fromJson(x))),
        message: json["message"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "waiting": waiting == null
            ? []
            : List<dynamic>.from(waiting!.map((x) => x.toJson())),
        "message": message,
        "status": status,
      };
}

class Waiting {
  final String? id;
  final String? name;
  final String? longitude;
  final String? latitude;
  final String? tripType;
  final int? schoolId;
  final int? busId;
  final dynamic logoPath;
  final List<MyParent>? myParents;

  Waiting({
    this.id,
    this.name,
    this.longitude,
    this.latitude,
    this.tripType,
    this.schoolId,
    this.busId,
    this.logoPath,
    this.myParents,
  });

  Waiting copyWith({
    String? id,
    String? name,
    String? longitude,
    String? latitude,
    String? tripType,
    int? schoolId,
    int? busId,
    dynamic logoPath,
    List<MyParent>? myParents,
  }) =>
      Waiting(
        id: id ?? this.id,
        name: name ?? this.name,
        longitude: longitude ?? this.longitude,
        latitude: latitude ?? this.latitude,
        tripType: tripType ?? this.tripType,
        schoolId: schoolId ?? this.schoolId,
        busId: busId ?? this.busId,
        logoPath: logoPath ?? this.logoPath,
        myParents: myParents ?? this.myParents,
      );

  factory Waiting.fromRawJson(String str) => Waiting.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Waiting.fromJson(Map<String, dynamic> json) => Waiting(
        id: json["id"],
        name: json["name"],
        longitude: json["longitude"],
        latitude: json["latitude"],
        tripType: json["trip_type"],
        schoolId: json["school_id"],
        busId: json["bus_id"],
        logoPath: json["logo_path"],
        myParents: json["my__parents"] == null
            ? []
            : List<MyParent>.from(
                json["my__parents"]!.map((x) => MyParent.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "longitude": longitude,
        "latitude": latitude,
        "trip_type": tripType,
        "school_id": schoolId,
        "bus_id": busId,
        "logo_path": logoPath,
        "my__parents": myParents == null
            ? []
            : List<dynamic>.from(myParents!.map((x) => x.toJson())),
      };
}

class MyParent {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final int? status;
  final String? logo;
  final String? typeAuth;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? firebaseToken;
  final String? logoPath;
  final Pivot? pivot;

  MyParent({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.status,
    this.logo,
    this.typeAuth,
    this.createdAt,
    this.updatedAt,
    this.firebaseToken,
    this.logoPath,
    this.pivot,
  });

  MyParent copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? emailVerifiedAt,
    String? address,
    int? status,
    String? logo,
    String? typeAuth,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? firebaseToken,
    String? logoPath,
    Pivot? pivot,
  }) =>
      MyParent(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
        address: address ?? this.address,
        status: status ?? this.status,
        logo: logo ?? this.logo,
        typeAuth: typeAuth ?? this.typeAuth,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        firebaseToken: firebaseToken ?? this.firebaseToken,
        logoPath: logoPath ?? this.logoPath,
        pivot: pivot ?? this.pivot,
      );

  factory MyParent.fromRawJson(String str) =>
      MyParent.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MyParent.fromJson(Map<String, dynamic> json) => MyParent(
        id: json["id"],
        name: json["name"],
        email: json["email"],
        phone: json["phone"],
        emailVerifiedAt: json["email_verified_at"],
        address: json["address"],
        status: json["status"],
        logo: json["logo"],
        typeAuth: json["typeAuth"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        firebaseToken: json["firebase_token"],
        logoPath: json["logo_path"],
        pivot: json["pivot"] == null ? null : Pivot.fromJson(json["pivot"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "email": email,
        "phone": phone,
        "email_verified_at": emailVerifiedAt,
        "address": address,
        "status": status,
        "logo": logo,
        "typeAuth": typeAuth,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "firebase_token": firebaseToken,
        "logo_path": logoPath,
        "pivot": pivot?.toJson(),
      };
}

class Pivot {
  final String? studentId;
  final int? myParentId;

  Pivot({
    this.studentId,
    this.myParentId,
  });

  Pivot copyWith({
    String? studentId,
    int? myParentId,
  }) =>
      Pivot(
        studentId: studentId ?? this.studentId,
        myParentId: myParentId ?? this.myParentId,
      );

  factory Pivot.fromRawJson(String str) => Pivot.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Pivot.fromJson(Map<String, dynamic> json) => Pivot(
        studentId: json["student_id"],
        myParentId: json["my__parent_id"],
      );

  Map<String, dynamic> toJson() => {
        "student_id": studentId,
        "my__parent_id": myParentId,
      };
}
