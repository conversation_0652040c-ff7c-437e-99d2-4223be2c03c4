import 'package:flutter/material.dart';

import '../../config/app_update_config.dart';
import '../../helper/network_serviecs.dart';
import '../models/app_update_status_model.dart';

class AppUpdateStatusRepo {
  final _dio = NetworkService();

  /// Check if app is currently under maintenance or updating
  /// [appName] - The unique identifier for the app
  /// [appType] - The type of app (parents, schools, attendants)
  Future<AppUpdateStatusModel> checkAppUpdateStatus({
    required String appName,
    required String appType,
  }) async {
    try {
      // Validate app name
      if (!AppUpdateConfig.isValidAppName(appName)) {
        debugPrint('Warning: App name "$appName" is not in the supported list');
      }

      // Construct the URL based on app type
      final String url = AppUpdateConfig.getUrlForAppType(appType);

      final request = await _dio.get(
        url: url,
        queryParameters: {
          'name': appName,
        },
        isAuth: false, // No authentication required as per documentation
      );

      AppUpdateStatusModel? appUpdateStatusModel;

      if (request.statusCode == 200) {
        appUpdateStatusModel = AppUpdateStatusModel.fromJson(request.data);
      } else {
        // Handle error responses
        appUpdateStatusModel = AppUpdateStatusModel.fromJson(request.data);
      }

      return appUpdateStatusModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return AppUpdateStatusModel(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Check if attendants app is updating
  Future<AppUpdateStatusModel> checkAttendantsAppStatus({
    required String appName,
  }) async {
    return await checkAppUpdateStatus(
      appName: appName,
      appType: 'attendants',
    );
  }

  /// Check if parents app is updating
  Future<AppUpdateStatusModel> checkParentsAppStatus({
    required String appName,
  }) async {
    return await checkAppUpdateStatus(
      appName: appName,
      appType: 'parents',
    );
  }

  /// Check if schools app is updating
  Future<AppUpdateStatusModel> checkSchoolsAppStatus({
    required String appName,
  }) async {
    return await checkAppUpdateStatus(
      appName: appName,
      appType: 'schools',
    );
  }
}
