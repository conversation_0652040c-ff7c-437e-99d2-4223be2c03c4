import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/data/models/trip_open_models/trip_open_models.dart';
import 'package:bus_driver/helper/network_serviecs.dart';

class CurrentTripEveningRepo {
  final _dio = NetworkService();

  Future<TripOpenModels> repo() async {
    final request = await _dio.get(
      url: ConfigBase.currentTripEvening,
      isAuth: true,
    );
    TripOpenModels? currentTripMorning;
    if (request.statusCode == 200) {
      currentTripMorning = TripOpenModels.fromMap(request.data);
    } else {
      currentTripMorning = TripOpenModels.fromMap(request.data);
    }
    return currentTripMorning;
  }
}
