import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/data/models/trip_open_models/trip_open_models.dart';
import 'package:bus_driver/helper/network_serviecs.dart';

class CurrentTripMorningRepo {
  final _dio = NetworkService();

  Future<TripOpenModels> repo() async {
    final request = await _dio.get(
      url: ConfigBase.currentTripMorning,
      isAuth: true,
    );
    // Logger().w(request.data);
    TripOpenModels? currentTripMorning;
    if (request.statusCode == 200) {
      currentTripMorning = TripOpenModels.fromMap(request.data);
    } else {
      currentTripMorning = TripOpenModels.fromMap(request.data);
    }
    return currentTripMorning;
  }
}
