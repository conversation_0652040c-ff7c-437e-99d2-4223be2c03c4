import 'package:bus_driver/config/global_variable.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';
import '../models/trips_model/evening_trip_model/absent_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/arrived_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/arrived_student_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/end_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/evening_trip_status_model.dart';
import '../models/trips_model/evening_trip_model/on_bus_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/remove_absence_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/remove_arrived_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/send_message_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/send_message_for_all_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/start_evening_trip_model.dart';
import '../models/trips_model/evening_trip_model/student_absent_evening_trip_model.dart';

class EveningTripRepo {
  final _dio = NetworkService();

  Future<EveningTripStatusModel> eveningTripStatusRepo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.eveningTripStatus,
        isAuth: true,
      );
      EveningTripStatusModel? eveningTripStatusModel;
      if (request.statusCode == 200) {
        eveningTripStatusModel = EveningTripStatusModel.fromJson(request.data);
      } else {
        eveningTripStatusModel = EveningTripStatusModel.fromJson(request.data);
      }
      return eveningTripStatusModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return EveningTripStatusModel(message: e.toString());
    }
  }

  Future<StartEveningTripModel> startEveningTripRepo({
    String? latitude,
    String? longitude,
    // required bool notify,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.startEveningTrip,
        body: {
          "latitude": latitude,
          "longitude": longitude,
          'notify': isChecked
        },
        isAuth: true,
      );
      StartEveningTripModel? startEveningTripModel;
      if (request.statusCode == 200) {
        startEveningTripModel = StartEveningTripModel.fromJson(request.data);
      } else {
        startEveningTripModel = StartEveningTripModel.fromJson(request.data);
      }
      return startEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return StartEveningTripModel(message: e.toString());
    }
  }

  Future<EndEveningTripModel> endEveningTripRepo({
    int? tripId,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.endEveningTrip,
        body: {
          "trip_id": tripId,
        },
        isAuth: true,
      );
      // Logger().w(request.data);
      EndEveningTripModel? endEveningTripModel;
      if (request.statusCode == 200) {
        endEveningTripModel = EndEveningTripModel.fromJson(request.data);
      } else {
        endEveningTripModel = EndEveningTripModel.fromJson(request.data);
      }
      return endEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return EndEveningTripModel(message: e.toString());
    }
  }

  Future<OnBusEveningTripModel> onBusEveningTripRepo({
    int? tripId,
  }) async {
    try {
      final request = await _dio.get(
        //"https://test.busatyapp.com/api/attendants/trips/evening/onBus/tripId"
        url: "${ConfigBase.onBusEveningTrip}$tripId",
        isAuth: true,
      );
      OnBusEveningTripModel? onBusEveningTripModel;
      if (request.statusCode == 200) {
        onBusEveningTripModel = OnBusEveningTripModel.fromJson(request.data);
      } else {
        onBusEveningTripModel = OnBusEveningTripModel.fromJson(request.data);
      }
      return onBusEveningTripModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return OnBusEveningTripModel(message: e.toString());
    }
  }

  Future<ArrivedStudentEveningTripModel> arrivedStudentEveningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: "${ConfigBase.arrivedStudentEveningTrip}$studentId",
        isAuth: true,
      );
      ArrivedStudentEveningTripModel? studentOnBusMorningTripModel;
      if (request.statusCode == 200) {
        studentOnBusMorningTripModel =
            ArrivedStudentEveningTripModel.fromJson(request.data);
      } else {
        studentOnBusMorningTripModel =
            ArrivedStudentEveningTripModel.fromJson(request.data);
      }
      return studentOnBusMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return ArrivedStudentEveningTripModel(message: e.toString());
    }
  }

  Future<ArrivedEveningTripModel> arrivedEveningTripRepo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.arrivedEveningTrip,
        isAuth: true,
      );
      ArrivedEveningTripModel? arrivedEveningTripModel;
      if (request.statusCode == 200) {
        arrivedEveningTripModel =
            ArrivedEveningTripModel.fromJson(request.data);
      } else {
        arrivedEveningTripModel =
            ArrivedEveningTripModel.fromJson(request.data);
      }
      return arrivedEveningTripModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return ArrivedEveningTripModel(message: e.toString());
    }
  }

  Future<StudentAbsentEveningTripModel> studentAbsentEveningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: "${ConfigBase.studentAbsentEveningTrip}$studentId",
        isAuth: true,
      );
      StudentAbsentEveningTripModel? studentAbsentEveningTripModel;
      if (request.statusCode == 200) {
        studentAbsentEveningTripModel =
            StudentAbsentEveningTripModel.fromJson(request.data);
      } else {
        studentAbsentEveningTripModel =
            StudentAbsentEveningTripModel.fromJson(request.data);
      }
      return studentAbsentEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return StudentAbsentEveningTripModel();
    }
  }

  Future<AbsentEveningTripModel> absentEveningTripRepo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.absentEveningTrip,
        isAuth: true,
      );
      AbsentEveningTripModel? absentEveningTripModel;
      if (request.statusCode == 200) {
        absentEveningTripModel = AbsentEveningTripModel.fromJson(request.data);
      } else {
        absentEveningTripModel = AbsentEveningTripModel.fromJson(request.data);
      }
      return absentEveningTripModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return AbsentEveningTripModel(message: e.toString());
    }
  }

  Future<RemoveArrivedEveningTripModel> removeArrivedEveningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: "${ConfigBase.removeArrivedHomeEveningTrip}$studentId",
        isAuth: true,
      );
      RemoveArrivedEveningTripModel? removeArrivedEveningTripModel;
      if (request.statusCode == 200) {
        removeArrivedEveningTripModel =
            RemoveArrivedEveningTripModel.fromJson(request.data);
      } else {
        removeArrivedEveningTripModel =
            RemoveArrivedEveningTripModel.fromJson(request.data);
      }
      return removeArrivedEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return RemoveArrivedEveningTripModel();
    }
  }

  Future<RemoveAbsenceEveningTripModel> removeAbsentEveningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: "${ConfigBase.removeAbsenceEveningTrip}$studentId",
        isAuth: true,
      );
      RemoveAbsenceEveningTripModel? removeAbsenceEveningTripModel;
      if (request.statusCode == 200) {
        removeAbsenceEveningTripModel =
            RemoveAbsenceEveningTripModel.fromJson(request.data);
      } else {
        removeAbsenceEveningTripModel =
            RemoveAbsenceEveningTripModel.fromJson(request.data);
      }
      return removeAbsenceEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return RemoveAbsenceEveningTripModel();
    }
  }

  Future<SendMessageEveningTripModel> sendMessageEveningTripRepo({
    String? studentId,
    int? messageId,
    // required String notificationsType,
  }) async {
    try {
      final request = await _dio.post(
        url: '${ConfigBase.sendMessageEveningTrip}$studentId',
        body: {
          "static_message_id": messageId,
          // "notifications_type":notificationsType
        },
        isAuth: true,
      );
      SendMessageEveningTripModel sendMessageEveningTripModel =
          SendMessageEveningTripModel();
      if (request.statusCode == 200) {
        sendMessageEveningTripModel =
            SendMessageEveningTripModel.fromJson(request.data);
      } else {
        sendMessageEveningTripModel =
            SendMessageEveningTripModel.fromJson(request.data);
      }
      return sendMessageEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return SendMessageEveningTripModel();
    }
  }

  Future<SendMessageForAllEveningTripModel> sendMessageForAllEveningTripRepo({
    required int staticMessageId,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.sendMessageForAllEveningTrip,
        body: {
          "static_message_id": staticMessageId,
        },
        isAuth: true,
      );
      SendMessageForAllEveningTripModel? sendMessageForAllEveningTripModel;
      if (request.statusCode == 200) {
        sendMessageForAllEveningTripModel =
            SendMessageForAllEveningTripModel.fromJson(request.data);
      } else {
        sendMessageForAllEveningTripModel =
            SendMessageForAllEveningTripModel.fromJson(request.data);
      }
      return sendMessageForAllEveningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return SendMessageForAllEveningTripModel();
    }
  }
}
