import 'dart:convert';

import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/helper/network_serviecs.dart';

import '../../../helper/data_state.dart';
import '../../config/global_variable.dart';

class LoginRepo {
  final _dio = NetworkService();

  Future<DataState<LoginDataModelh>> repo({
    String? username,
    String? password,
  }) async {
    final request = await _dio.post(
      url: ConfigBase.login,
      body: {
        "username": username,
        "password": password,
        "firebase_token": fCMToken
      },
    );
    print(request.data.toString());
    if (request.statusMessage == "OK") {
      print(request.data["token"]);
      var logindata = LoginDataModelh.fromMap(request.data);
      print(logindata.toString());
      return DataSuccess(
        logindata,
      );
    } else {
      return DataFailed(
        message: request.data['massage'],
      );
    }
  }
}

class LoginDataModelh {
  String? token;
  final int? id;
  final String? name;
  final String? username;
  final String? phone;
  final String? emailVerifiedAt;
  final String? address;
  final String? cityName;
  final int? status;
  final String? logo;
  final String? deletedAt;
  final String? createdAt;
  final String? updatedAt;
  final String? typeAuth;
  final String? logoPath;
  final int? genderId;
  final int? schoolId;
  final int? religionId;
  final int? typeBloodId;
  final int? busId;
  final String? joiningDate;
  final String? type;
  final String? birthDate;

  LoginDataModelh({
    this.token,
    this.id,
    this.name,
    this.username,
    this.phone,
    this.emailVerifiedAt,
    this.address,
    this.cityName,
    this.status,
    this.logo,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.typeAuth,
    this.logoPath,
    this.genderId,
    this.schoolId,
    this.religionId,
    this.typeBloodId,
    this.busId,
    this.joiningDate,
    this.type,
    this.birthDate,
  });

  LoginDataModelh copyWith({
    int? id,
    String? name,
    String? username,
    String? phone,
    String? emailVerifiedAt,
    String? address,
    String? cityName,
    int? status,
    String? logo,
    String? deletedAt,
    String? createdAt,
    String? updatedAt,
    String? typeAuth,
    String? logoPath,
    int? genderId,
    int? schoolId,
    int? religionId,
    int? typeBloodId,
    int? busId,
    String? joiningDate,
    String? type,
    String? birthDate,
  }) {
    return LoginDataModelh(
      id: id ?? this.id,
      name: name ?? this.name,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      address: address ?? this.address,
      cityName: cityName ?? this.cityName,
      status: status ?? this.status,
      logo: logo ?? this.logo,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      typeAuth: typeAuth ?? this.typeAuth,
      logoPath: logoPath ?? this.logoPath,
      genderId: genderId ?? this.genderId,
      schoolId: schoolId ?? this.schoolId,
      religionId: religionId ?? this.religionId,
      typeBloodId: typeBloodId ?? this.typeBloodId,
      busId: busId ?? this.busId,
      joiningDate: joiningDate ?? this.joiningDate,
      type: type ?? this.type,
      birthDate: birthDate ?? this.birthDate,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'username': username,
      'phone': phone,
      'email_verified_at': emailVerifiedAt,
      'address': address,
      'city_name': cityName,
      'status': status,
      'logo': logo,
      'deleted_at': deletedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'typeAuth': typeAuth,
      'logo_path': logoPath,
      'gender_id': genderId,
      'school_id': schoolId,
      'religion_id': religionId,
      'type__blood_id': typeBloodId,
      'bus_id': busId,
      'Joining_Date': joiningDate,
      'type': type,
      'birth_date': birthDate,
    };
  }

  factory LoginDataModelh.fromMap(Map<String, dynamic> map) {
    return LoginDataModelh(
      token: map["token"],
      id: map["data"]['id'],
      name: map["data"]['name'],
      username: map["data"]['email'],
      phone: map["data"]['phone'],
      emailVerifiedAt: map["data"]['email_verified_at'],
      address: map["data"]['address'],
      cityName: map["data"]['city_name'],
      status: map["data"]['status'],
      logo: map["data"]['logo'],
      deletedAt: map["data"]['deleted_at'],
      createdAt: map["data"]['created_at'],
      updatedAt: map["data"]['updated_at'],
      typeAuth: map["data"]['typeAuth'],
      logoPath: map["data"]['logo_path'],
      genderId: map["data"]['gender_id'],
      schoolId: map["data"]['school_id'],
      religionId: map["data"]['religion_id'],
      typeBloodId: map["data"]['type__blood_id'],
      busId: map["data"]['bus_id'],
      joiningDate: map["data"]['Joining_Date'],
      type: map["data"]['type'],
      birthDate: map["data"]['birth_date'],
    );
  }

  String toJson() => json.encode(toMap());

  factory LoginDataModelh.fromJson(String source) =>
      LoginDataModelh.fromMap(json.decode(source));

  @override
  String toString() {
    return 'LoginDataModelh(token :$token,'
        'id: $id, name: $name, '
        'email: $username, '
        'phone: $phone, '
        'email_verified_at: $emailVerifiedAt, '
        'address: $address, '
        'city_name: $cityName, '
        'status: $status, '
        'logo: $logo, '
        'deleted_at: $deletedAt, '
        'created_at: $createdAt, '
        'updated_at: $updatedAt, '
        'typeAuth: $typeAuth, '
        'logo_path: $logoPath, '
        'gender_id: $genderId, '
        'school_id: $schoolId, '
        'religion_id: $religionId, '
        'type__blood_id: $typeBloodId, '
        'bus_id: $busId, '
        'Joining_Date: $joiningDate, '
        'type: $type, '
        'birth_date: $birthDate,)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LoginDataModelh &&
        other.id == id &&
        other.name == name &&
        other.username == username &&
        other.phone == phone &&
        other.emailVerifiedAt == emailVerifiedAt &&
        other.address == address &&
        other.cityName == cityName &&
        other.status == status &&
        other.logo == logo &&
        other.deletedAt == deletedAt &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.typeAuth == typeAuth &&
        other.logoPath == logoPath &&
        other.genderId == genderId &&
        other.schoolId == schoolId &&
        other.religionId == religionId &&
        other.typeBloodId == typeBloodId &&
        other.busId == busId &&
        other.joiningDate == joiningDate &&
        other.type == type &&
        other.birthDate == birthDate;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        username.hashCode ^
        phone.hashCode ^
        emailVerifiedAt.hashCode ^
        address.hashCode ^
        cityName.hashCode ^
        status.hashCode ^
        logo.hashCode ^
        deletedAt.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        typeAuth.hashCode ^
        logoPath.hashCode ^
        genderId.hashCode ^
        schoolId.hashCode ^
        religionId.hashCode ^
        typeBloodId.hashCode ^
        busId.hashCode ^
        joiningDate.hashCode ^
        type.hashCode ^
        birthDate.hashCode;
  }
}
