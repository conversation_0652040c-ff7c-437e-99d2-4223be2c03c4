import 'package:bus_driver/helper/cache_helper.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../../../config/config_base.dart';
import '../../../helper/data_state.dart';
import '../../../helper/network_serviecs.dart';
import '../models/change_password_models/change_password_models.dart';

class LogoutRepo {
  final _dio = NetworkService();

  Future<DataState<ChangePasswordModels>> repo() async {
    final request = await _dio.post(
      url: ConfigBase.logout,
      body: {
        "firebase_token": CacheHelper.getString("token"),
      },
      isAuth: true,
    );

    if (request.statusMessage == "OK") {
      CacheHelper.remove("token");
      await FirebaseMessaging.instance.deleteToken();
      return DataSuccess(
        ChangePasswordModels.fromJson(
          request.data,
        ),
      );
    } else {
      return const DataFailed();
    }
  }
}
