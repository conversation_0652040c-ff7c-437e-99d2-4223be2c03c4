import 'package:bus_driver/config/global_variable.dart';
import 'package:bus_driver/data/models/trips_model/morning_trip_model/group_and_single_message_model.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';
import '../models/trips_model/morning_trip_model/absent_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/end_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/morning_trip_status_model.dart';
import '../models/trips_model/morning_trip_model/on_bus_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/present_on_bus_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/remove_absence_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/remove_present_on_bus_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/send_message_for_all_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/send_message_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/student_absent_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/waiting_morning_trip_model.dart';
import '../models/trips_model/morning_trip_model/start_morning_trip_model.dart';

class MorningTripRepo {
  final _dio = NetworkService();

  Future<MorningTripStatusModel> morningTripStatusRepo() async {
    try {
      final request = await _dio.get(
        //https://stage.busatyapp.com/api/attendants/trips/morning/status
        url: ConfigBase.morningTripStatus,
        isAuth: true,
      );
      MorningTripStatusModel? morningTripStatusModel;
      if (request.statusCode == 200) {
        morningTripStatusModel = MorningTripStatusModel.fromJson(request.data);
      } else {
        morningTripStatusModel = MorningTripStatusModel.fromJson(request.data);
      }
      return morningTripStatusModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return MorningTripStatusModel(message: e.toString());
    }
  }

  Future<StartMorningTripModel> startMorningTripRepo({
    String? latitude,
    String? longitude,
    //  required bool notify,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.startMorningTrip,
        body: {
          "latitude": latitude,
          "longitude": longitude,
          "notify": isChecked,
        },
        isAuth: true,
      );

      StartMorningTripModel? startMorningTripModel;
      if (request.statusCode == 200) {
        startMorningTripModel = StartMorningTripModel.fromJson(request.data);
      } else {
        startMorningTripModel = StartMorningTripModel.fromJson(request.data);
      }
      return startMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return StartMorningTripModel(message: e.toString());
    }
  }

  Future<EndMorningTripModel> endMorningTripRepo({
    int? tripId,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.endMorningTrip,
        body: {"trip_id": tripId, 'notify': isChecked},
        isAuth: true,
      );
      EndMorningTripModel? endMorningTripModel;
      if (request.statusCode == 200) {
        endMorningTripModel = EndMorningTripModel.fromJson(request.data);
      } else {
        endMorningTripModel = EndMorningTripModel.fromJson(request.data);
      }
      return endMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return EndMorningTripModel(message: e.toString());
    }
  }

  Future<WaitingMorningTripModel> waitingMorningTripRepo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.waitingMorningTrip,
        isAuth: true,
      );
      WaitingMorningTripModel? morningTripWaitingModel;
      if (request.statusCode == 200) {
        morningTripWaitingModel =
            WaitingMorningTripModel.fromJson(request.data);
      } else {
        morningTripWaitingModel =
            WaitingMorningTripModel.fromJson(request.data);
      }
      return morningTripWaitingModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return WaitingMorningTripModel(message: e.toString());
    }
  }

  Future<OnBusMorningTripModel> onBusMorningTripRepo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.onBusMorningTrip,
        isAuth: true,
      );
      OnBusMorningTripModel? onBusMorningTripModel;
      if (request.statusCode == 200) {
        onBusMorningTripModel = OnBusMorningTripModel.fromJson(request.data);
      } else {
        onBusMorningTripModel = OnBusMorningTripModel.fromJson(request.data);
      }
      return onBusMorningTripModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return OnBusMorningTripModel(message: e.toString());
    }
  }

  Future<PresentOnBusMorningTripModel> presentOnBusMorningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: "${ConfigBase.presentOnBusMorningTrip}$studentId",
        isAuth: true,
      );
      PresentOnBusMorningTripModel? studentOnBusMorningTripModel;
      if (request.statusCode == 200) {
        studentOnBusMorningTripModel =
            PresentOnBusMorningTripModel.fromJson(request.data);
      } else {
        studentOnBusMorningTripModel =
            PresentOnBusMorningTripModel.fromJson(request.data);
      }
      return studentOnBusMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return PresentOnBusMorningTripModel(message: e.toString());
    }
  }

  Future<AbsentMorningTripModel> absentMorningTripRepo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.absentMorningTrip,
        isAuth: true,
      );
      AbsentMorningTripModel? absentMorningTripModel;
      if (request.statusCode == 200) {
        absentMorningTripModel = AbsentMorningTripModel.fromJson(request.data);
      } else {
        absentMorningTripModel = AbsentMorningTripModel.fromJson(request.data);
      }
      return absentMorningTripModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return AbsentMorningTripModel();
    }
  }

  Future<StudentAbsentMorningTripModel> studentAbsentMorningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        //
        url: "${ConfigBase.studentAbsentMorningTrip}$studentId",
        isAuth: true,
      );

      StudentAbsentMorningTripModel? studentAbsentMorningTripModel;
      if (request.statusCode == 200) {
        studentAbsentMorningTripModel =
            StudentAbsentMorningTripModel.fromJson(request.data);
      } else {
        studentAbsentMorningTripModel =
            StudentAbsentMorningTripModel.fromJson(request.data);
      }
      return studentAbsentMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return StudentAbsentMorningTripModel();
    }
  }

  Future<RemovePresentOnBusMorningTripModel> removePresentOnBusMorningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.removePresentOnBusMorningTrip,
        body: {
          "student_id": studentId,
        },
        isAuth: true,
      );
      // Logger().w(request.data);
      RemovePresentOnBusMorningTripModel? removePresentOnBusMorningTripModel;
      if (request.statusCode == 200) {
        removePresentOnBusMorningTripModel =
            RemovePresentOnBusMorningTripModel.fromJson(request.data);
      } else {
        removePresentOnBusMorningTripModel =
            RemovePresentOnBusMorningTripModel.fromJson(request.data);
      }
      return removePresentOnBusMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return RemovePresentOnBusMorningTripModel();
    }
  }

  Future<RemoveAbsenceMorningTripModel> removeAbsenceMorningTripRepo({
    String? studentId,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.removeAbsenceMorningTrip,
        body: {
          "student_id": studentId,
        },
        isAuth: true,
      );
      RemoveAbsenceMorningTripModel? removeAbsenceMorningTripModel;
      if (request.statusCode == 200) {
        removeAbsenceMorningTripModel =
            RemoveAbsenceMorningTripModel.fromJson(request.data);
      } else {
        removeAbsenceMorningTripModel =
            RemoveAbsenceMorningTripModel.fromJson(request.data);
      }
      return removeAbsenceMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return RemoveAbsenceMorningTripModel();
    }
  }

  // Future <SendMessageMorningTripModel> sendMessageEveningTripRepo({
  //   int? studentId,
  //   int? messageId,
  //   // required String notificationsType,

  // }) async {
  //   try {
  //     final request = await _dio.post(
  //       url: '${ConfigBase.sendMessageMorningTrip}$studentId',
  //       body: {

  //         "static_message_id": messageId,
  //         // "notifications_type":notificationsType

  //       },
  //       isAuth: true,
  //     );
  //     SendMessageMorningTripModel? sendMessageEveningTripModel;
  //     if (request.statusCode == 200) {
  //       sendMessageEveningTripModel = SendMessageMorningTripModel.fromJson(request.data);
  //     } else {
  //       sendMessageEveningTripModel = SendMessageMorningTripModel.fromJson(request.data);
  //     }
  //     return sendMessageEveningTripModel;
  //   } on Exception catch (e , stackTrace) {
  //     debugPrint("error $e");
  //     return SendMessageMorningTripModel();
  //   }
  // }

  // Future <SendMessageMorningTripModel> sendMessageMorningTripRepo({
  //   // int? studentId,
  //   int? messageId,
  //   // required String notificationsType,

  // }) async {
  //   try {
  //     final request = await _dio.post(//https://test.busatyapp.com/trips/morning/parents/messages/store/studentId
  //       url: '${ConfigBase.sendMessageMorningTrip}',
  //       body: {
  //         "static_message_id": messageId,
  //         // "notifications_type":notificationsType
  //       },
  //       isAuth: true,
  //     );
  //     print(request.data);
  //     Logger().e(request.data);
  //     SendMessageMorningTripModel? sendMessageMorningTripModel;
  //     if (request.statusCode == 200) {

  //       sendMessageMorningTripModel = SendMessageMorningTripModel.fromJson(request.data);
  //     } else {
  //       sendMessageMorningTripModel = SendMessageMorningTripModel.fromJson(request.data);
  //     }
  //     return sendMessageMorningTripModel;
  //   } on Exception catch (e , stackTrace) {
  //     debugPrint("error $e");
  //     return SendMessageMorningTripModel();
  //   }
  // }

  Future<SendMessageMorningTripModel> sendMessagemorningTripRepo({
    String? studentId,
    int? messageId,
    // required String notificationsType,
  }) async {
    try {
      final request = await _dio.post(
        url: '${ConfigBase.sendMessageMorningTrip}$studentId',
        body: {
          "static_message_id": messageId,
          // "notifications_type":notificationsType
        },
        isAuth: true,
      );
      SendMessageMorningTripModel sendMessageMorningTripModel =
          SendMessageMorningTripModel();
      if (request.statusCode == 200) {
        sendMessageMorningTripModel =
            SendMessageMorningTripModel.fromJson(request.data);
      } else {
        sendMessageMorningTripModel =
            SendMessageMorningTripModel.fromJson(request.data);
      }
      return sendMessageMorningTripModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return SendMessageMorningTripModel();
    }
  }

  Future<SendMessageForAllMorningTripModel> sendMessageForAllMorningTripRepo({
    int? staticMessageId,

    // String? message_en,
  }) async {
    try {
      final request = await _dio.post(
        url: ConfigBase.sendMessageForAllMorningTrip,
        body: {
          "static_message_id": staticMessageId,
          // "notifications_type" : notificationType,
        },
        isAuth: true,
      );
      print("===================== ${request.data} ======= ");
      // Logger().w(request.data);
      SendMessageForAllMorningTripModel? sendMessageForAllMorningTrip;
      if (request.statusCode == 200) {
        sendMessageForAllMorningTrip =
            SendMessageForAllMorningTripModel.fromJson(request.data);
      } else {
        sendMessageForAllMorningTrip =
            SendMessageForAllMorningTripModel.fromJson(request.data);
      }
      return sendMessageForAllMorningTrip;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return SendMessageForAllMorningTripModel();
    }
  }

  Future<GroupAndSingleMessageModel> groupOrSingleMessage({
    String? type_day,
  }) async {
    try {
      final request = await _dio.get(
        url:
            'trips/notifylist?model_additional=$type_day',
        isAuth: true,
      );
      print(request.data);
      // print(headers);


      GroupAndSingleMessageModel? groupAndSingleMessageModel;
      if (request.statusCode == 200) {
        groupAndSingleMessageModel =
            GroupAndSingleMessageModel.fromJson(request.data);
      } else {
        groupAndSingleMessageModel =
            GroupAndSingleMessageModel.fromJson(request.data);
      }
      return groupAndSingleMessageModel;
    } on Exception catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("error $e");
      return GroupAndSingleMessageModel();
    }
  }
}
