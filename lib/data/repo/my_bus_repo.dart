import '../../../config/config_base.dart';
import '../../../helper/network_serviecs.dart';
import '../models/my_bus_model.dart';

class MyBusRepo {
  final _dio = NetworkService();

  Future<MyBusModel> repo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.myBus,
        isAuth: true,
      );
      // Logger().w(request.data);
      MyBusModel? myBusModel;
      if (request.statusCode == 200) {
        myBusModel = MyBusModel.fromJson(request.data);
      } else {
        myBusModel = MyBusModel.fromJson(request.data);
      }
      return myBusModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      print("catch error $e");
      return MyBusModel(message: e.toString());
    }
  }
}
