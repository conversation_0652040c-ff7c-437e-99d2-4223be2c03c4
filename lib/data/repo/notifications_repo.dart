import 'package:bus_driver/config/config_base.dart';
import 'package:flutter/material.dart';

import '../../helper/network_serviecs.dart';
import '../models/notifications_models/notifications_model.dart';

class NotificationsRepo {
  final _dio = NetworkService();

  // Future<FCMResponse> sendNotification({
  //   required List<String> deviceTokens,
  //   String? title,
  //   String? body,

  // }) async {
  //   try {
  //     Dio dio = Dio();

  //     Map<String, dynamic> requestBody = {
  //       'registration_ids': deviceTokens,
  //       'notification': {
  //         'body': '$body',
  //         'priority': 'high',
  //         'subtitle': '',
  //         'title': '$title',
  //         'sound': 'default',
  //       },
  //       'data': {
  //         'priority': 'high',
  //         'urls': '',
  //       },
  //     };

  //     Options options = Options(
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Authorization': 'key=$firebaseServerKey',
  //       },
  //     );

  //     Response response = await dio.post(
  //       ConfigBase.fcmUrl,
  //       data: requestBody,
  //       options: options,
  //     );
  //     FCMResponse? notificationsModel;
  //     if (response.statusCode == 200) {

  //       notificationsModel = FCMResponse.fromMap(response.data);
  //     } else {
  //       notificationsModel = FCMResponse.fromMap(response.data);
  //     }
  //     return notificationsModel;
  //   } catch (e , stackTrace) {
  //     return FCMResponse();
  //   }
  // }

  Future<AllNotificationsModel1> notifications({int? page}) async {
    try {
      final request = await _dio.get(
        url: "notifications?page=$page&limit=10",
        isAuth: true,
      );
      AllNotificationsModel1? notificationsModel;
      if (request.statusCode == 200) {
        notificationsModel = AllNotificationsModel1.fromJson(request.data);
      } else {
        notificationsModel = AllNotificationsModel1.fromJson(request.data);
      }
      return notificationsModel;
    } catch (e) {
      return AllNotificationsModel1();
    }
  }

  // Future<FcmTokenModel> allParentsFcmTokens() async {
  //   try {
  //     final request = await _dio.get(
  //       url: ConfigBase.allParentsFcmTokens,
  //       isAuth: true,
  //     );

  //     FcmTokenModel? fcmTokenModel;
  //     if (request.statusCode == 200) {
  //       fcmTokenModel = FcmTokenModel.fromMap(request.data);
  //     } else {
  //       fcmTokenModel = FcmTokenModel.fromMap(request.data);
  //     }
  //     return fcmTokenModel;
  //   } catch (e , stackTrace) {
  //     return FcmTokenModel(message: e.toString());
  //   }
  // }

  // Future<FcmTokenModel> parentFcmTokens({int? studentId}) async {
  //   try {
  //     final request = await _dio.post(
  //       url: '${ConfigBase.parentFcmTokens}/$studentId',
  //       isAuth: true,
  //     );

  //     FcmTokenModel? fcmTokenModel;
  //     if (request.statusCode == 200) {
  //       fcmTokenModel = FcmTokenModel.fromMap(request.data);
  //     } else {
  //       fcmTokenModel = FcmTokenModel.fromMap(request.data);
  //     }
  //     return fcmTokenModel;
  //   } catch (e , stackTrace) {
  //     return FcmTokenModel(message: e.toString());
  //   }
  // }
}
