import 'package:bus_driver/data/models/parents_model.dart';

import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';

class ParentsRepo {
  final _dio = NetworkService();
  Future<ParentsModel> repo({
    String? id,
}) async {
    try {
      final request = await _dio.get(
        url: "${ConfigBase.myParents}$id",
        isAuth: true,
      );
      ParentsModel? parentsModel;
      if (request.statusCode == 200) {
        parentsModel = ParentsModel.fromJson(request.data);
      } else {
        parentsModel = ParentsModel.fromJson(request.data);
      }
      return parentsModel;
    } catch (e , stackTrace) {
      print(stackTrace);
      print("catch error $e");
      return ParentsModel(message: e.toString());
    }
  }
}