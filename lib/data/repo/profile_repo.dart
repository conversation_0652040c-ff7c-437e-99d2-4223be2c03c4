import 'package:bus_driver/data/models/profile_models/profile_models.dart';
import 'package:flutter/material.dart';

import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';

class ProfileRepo {
  final _dio = NetworkService();

  Future<ProfileModels> repo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.profile,
        isAuth: true,
      );
      ProfileModels? profileModels;
      if (request.statusCode == 200) {
        profileModels = ProfileModels.fromJson(request.data);
      } else {
        profileModels = ProfileModels.fromJson(request.data);
      }
      return profileModels;
    } catch (e, stackTrace) {
      print(stackTrace);
      debugPrint("catch error $e");
      return ProfileModels(errors: e.toString());
    }
  }
}
