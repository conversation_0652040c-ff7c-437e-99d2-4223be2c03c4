import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/data/models/question_help_models/question_help_models.dart';
import 'package:bus_driver/helper/network_serviecs.dart';

class QuestionHelperRepo {
  final _dio = NetworkService();

  Future<QuestionHelp> repo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.questionHelp,
      );
      // Logger().w(request.data);
      QuestionHelp? questionHelp;
      if (request.statusCode == 200) {
        questionHelp = QuestionHelp.fromJson(request.data);
      } else {
        questionHelp = QuestionHelp.fromJson(request.data);
      }
      return questionHelp;
    } catch (e, stackTrace) {
      print(stackTrace);
      print(e.toString());
      return QuestionHelp();
    }
  }
}
