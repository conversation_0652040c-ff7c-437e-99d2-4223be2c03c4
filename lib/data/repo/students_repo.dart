import '../../config/config_base.dart';
import '../../helper/network_serviecs.dart';
import '../models/students_model.dart';

class StudentsRepo {
  final _dio = NetworkService();

  Future<StudentsModel> repo() async {
    try {
      final request = await _dio.get(
        url: ConfigBase.allStudents,
        isAuth: true,
      );
      // Logger().w(request.data);
      StudentsModel? studentsModel;
      if (request.statusCode == 200) {
        studentsModel = StudentsModel.fromJson(request.data);
      } else {
        studentsModel = StudentsModel.fromJson(request.data);
      }
      return studentsModel;
    } catch (e, stackTrace) {
      print(stackTrace);
      print("catch error $e");
      return StudentsModel(message: e.toString());
    }
  }
}
