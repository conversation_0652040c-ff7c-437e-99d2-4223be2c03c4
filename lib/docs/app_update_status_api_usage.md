# App Update Status API - دليل الاستخدام

## نظرة عامة
هذا النظام يتيح للتطبيق فحص حالة التحديث والصيانة من خلال API endpoint محدد. يمكن استخدامه لعرض شاشة صيانة أو منع المستخدمين من تنفيذ إجراءات معينة أثناء فترات الصيانة.

## الملفات المضافة

### 1. النماذج (Models)
- `lib/data/models/app_update_status_model.dart` - نموذج البيانات للاستجابة

### 2. المستودعات (Repositories)
- `lib/data/repo/app_update_status_repo.dart` - طبقة الوصول للبيانات

### 3. الخدمات (Services)
- `lib/services/app_update_service.dart` - منطق الأعمال الرئيسي

### 4. المساعدات (Utilities)
- `lib/utils/app_startup_helper.dart` - مساعدات بدء التطبيق

### 5. الواجهات (Widgets)
- `lib/widgets/maintenance_screen.dart` - شاشة الصيانة

### 6. الأمثلة (Examples)
- `lib/examples/main_with_app_update_check.dart` - أمثلة التطبيق

## كيفية الاستخدام

### 1. التحقق من حالة التطبيق عند البدء

```dart
import 'package:your_app/services/app_update_service.dart';
import 'package:your_app/utils/app_startup_helper.dart';

// في main.dart أو splash screen
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: AppStatusWrapper(
        appName: 'your-app-name', // اسم التطبيق الخاص بك
        appType: AppType.attendants, // نوع التطبيق
        child: YourMainScreen(),
      ),
    );
  }
}
```

### 2. التحقق قبل تنفيذ إجراءات مهمة

```dart
Future<void> performCriticalAction() async {
  final shouldBlock = await AppStartupHelper.shouldBlockUserActions(
    appName: 'your-app-name',
    appType: AppType.attendants,
  );

  if (shouldBlock) {
    // عرض رسالة صيانة
    await AppStartupHelper.showMaintenanceDialogIfNeeded(
      context: context,
      appName: 'your-app-name',
      appType: AppType.attendants,
    );
    return;
  }

  // تنفيذ الإجراء
  // Your action logic here
}
```

### 3. استخدام الخدمة مباشرة

```dart
final appUpdateService = AppUpdateService();

final result = await appUpdateService.checkCurrentAppStatus(
  appName: 'your-app-name',
  appType: AppType.attendants,
);

if (result.isSuccess && result.isUpdating) {
  // التطبيق تحت الصيانة
  showMaintenanceScreen();
} else {
  // التطبيق يعمل بشكل طبيعي
  proceedNormally();
}
```

## أنواع التطبيقات المدعومة

```dart
enum AppType { 
  attendants,  // المرافقين
  parents,     // أولياء الأمور
  schools      // المدارس
}
```

## استجابة API

### نجح الطلب - التطبيق يعمل
```json
{
  "success": true,
  "name": "app-name",
  "is_updating": false,
  "message": "App is available"
}
```

### نجح الطلب - التطبيق تحت الصيانة
```json
{
  "success": true,
  "name": "app-name",
  "is_updating": true,
  "message": "App is under maintenance"
}
```

### خطأ في التحقق
```json
{
  "success": false,
  "message": "Invalid or missing app name"
}
```

## التكوين

### إضافة endpoint في config_base.dart
```dart
static const String appUpdating = "app/updating";
```

### تحديث base URL حسب نوع التطبيق
- للمرافقين: `https://test.busatyapp.com/api/attendants/app/updating`
- لأولياء الأمور: `https://test.busatyapp.com/api/parents/app/updating`
- للمدارس: `https://test.busatyapp.com/api/schools/app/updating`

## الميزات

### ✅ المزايا
- لا يتطلب مصادقة
- يدعم أنواع تطبيقات متعددة
- معالجة أخطاء شاملة
- واجهة مستخدم جاهزة لشاشة الصيانة
- سهولة التكامل مع التطبيق الحالي

### 🔧 الاستخدامات
- عرض شاشة صيانة أثناء التحديثات
- منع الإجراءات المهمة أثناء الصيانة
- إشعار المستخدمين بحالة التطبيق
- التحكم في وصول المستخدمين

## ملاحظات مهمة

1. **اسم التطبيق**: تأكد من استخدام الاسم الصحيح للتطبيق
2. **نوع التطبيق**: اختر النوع المناسب (attendants/parents/schools)
3. **معالجة الأخطاء**: النظام يتعامل مع الأخطاء بأمان ويسمح بالمتابعة
4. **الأداء**: الفحص سريع ولا يؤثر على أداء التطبيق

## التخصيص

يمكنك تخصيص:
- شاشة الصيانة (`MaintenanceScreen`)
- رسائل الخطأ
- منطق التعامل مع الاستجابات
- تصميم واجهة المستخدم
