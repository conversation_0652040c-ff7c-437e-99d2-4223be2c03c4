// Example of how to integrate app update status check in main.dart

import 'package:flutter/material.dart';
import '../services/app_update_service.dart';
import '../utils/app_startup_helper.dart';
import '../widgets/maintenance_screen.dart';

class MyAppWithUpdateCheck extends StatelessWidget {
  const MyAppWithUpdateCheck({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Busaty - Bus',
      debugShowCheckedModeBanner: false,
      home: AppStatusWrapper(
        appName: 'busaty-attendants', // Replace with your actual app name
        appType: AppType.attendants, // Change based on your app type
        child: Builder(
          builder: (context) {
            // Your existing home logic here
            return token != null
                ? const HomeScreen()
                : const OnBoardingLanguageScreen();
          },
        ),
      ),
    );
  }
}

// Alternative approach: Check during splash screen
class SplashScreenWithUpdateCheck extends StatefulWidget {
  const SplashScreenWithUpdateCheck({Key? key}) : super(key: key);

  @override
  State<SplashScreenWithUpdateCheck> createState() =>
      _SplashScreenWithUpdateCheckState();
}

class _SplashScreenWithUpdateCheckState
    extends State<SplashScreenWithUpdateCheck> {
  final AppUpdateService _appUpdateService = AppUpdateService();

  @override
  void initState() {
    super.initState();
    _checkAppStatusAndNavigate();
  }

  Future<void> _checkAppStatusAndNavigate() async {
    try {
      // Show splash for minimum time
      await Future.delayed(const Duration(seconds: 2));

      // Check app update status
      final result = await _appUpdateService.checkCurrentAppStatus(
        appName: 'busaty-attendants', // Replace with your actual app name
        appType: AppType.attendants, // Change based on your app type
      );

      if (!mounted) return;

      if (result.isSuccess && result.isUpdating) {
        // Show maintenance screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => MaintenanceScreen(
              message: result.message,
              appName: result.appName,
              onRetry: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SplashScreenWithUpdateCheck(),
                  ),
                );
              },
            ),
          ),
        );
      } else {
        // Navigate to normal app flow
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => token != null
                ? const HomeScreen()
                : const OnBoardingLanguageScreen(),
          ),
        );
      }
    } catch (e) {
      // In case of error, proceed with normal flow
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => token != null
                ? const HomeScreen()
                : const OnBoardingLanguageScreen(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Your app logo here
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.directions_bus,
                size: 60,
                color: Colors.blue,
              ),
            ),

            const SizedBox(height: 32),

            const Text(
              'Busaty',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),

            const SizedBox(height: 48),

            const CircularProgressIndicator(),

            const SizedBox(height: 16),

            const Text(
              'جاري التحقق من حالة التطبيق...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Example of checking before performing critical actions
class ExampleActionWithUpdateCheck extends StatelessWidget {
  const ExampleActionWithUpdateCheck({Key? key}) : super(key: key);

  Future<void> _performCriticalAction(BuildContext context) async {
    // Check if actions should be blocked
    final shouldBlock = await AppStartupHelper.shouldBlockUserActions(
      appName: 'busaty-attendants',
    );

    if (shouldBlock) {
      // Show maintenance dialog
      await AppStartupHelper.showMaintenanceDialogIfNeeded(
        context: context,
        appName: 'busaty-attendants',
      );
      return;
    }

    // Proceed with the action
    // Your critical action logic here
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Action performed successfully!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Example Action')),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _performCriticalAction(context),
          child: const Text('Perform Critical Action'),
        ),
      ),
    );
  }
}

// Placeholder classes (replace with your actual classes)
class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Placeholder();
}

class OnBoardingLanguageScreen extends StatelessWidget {
  const OnBoardingLanguageScreen({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => const Placeholder();
}

// Placeholder token variable (replace with your actual token logic)
String? token;
