// مثال سريع لاستخدام نظام فحص حالة التحديث

import 'package:flutter/material.dart';
import '../services/app_update_service.dart';
import '../utils/app_startup_helper.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'App Update Check Example',
      home: AppStatusWrapper(
        appName: 'busaty-attendants', // اسم التطبيق الخاص بك
        appType: AppType.attendants, // نوع التطبيق
        child: HomeScreen(),
      ),
    );
  }
}

class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الشاشة الرئيسية'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 48),
                    SizedBox(height: 16),
                    Text(
                      'التطبيق يعمل بشكل طبيعي',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'تم فحص حالة التطبيق بنجاح',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _performCriticalAction(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'تنفيذ إجراء مهم',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            SizedBox(height: 12),
            ElevatedButton(
              onPressed: () => _checkAppStatus(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'فحص حالة التطبيق يدوياً',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            SizedBox(height: 20),
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ملاحظات:',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    SizedBox(height: 8),
                    Text('• يتم فحص حالة التطبيق تلقائياً عند البدء'),
                    Text('• في حالة الصيانة، ستظهر شاشة خاصة'),
                    Text('• الإجراءات المهمة محمية من التنفيذ أثناء الصيانة'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _performCriticalAction(BuildContext context) async {
    // فحص ما إذا كان يجب منع الإجراءات
    final shouldBlock = await AppStartupHelper.shouldBlockUserActions(
      appName: 'busaty-attendants',
    );

    if (shouldBlock) {
      // عرض رسالة صيانة
      await AppStartupHelper.showMaintenanceDialogIfNeeded(
        context: context,
        appName: 'busaty-attendants',
      );
      return;
    }

    // تنفيذ الإجراء المهم
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تنفيذ الإجراء بنجاح!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _checkAppStatus(BuildContext context) async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final appUpdateService = AppUpdateService();
      final result = await appUpdateService.checkCurrentAppStatus(
        appName: 'busaty-attendants',
        appType: AppType.attendants,
      );

      Navigator.pop(context); // إغلاق مؤشر التحميل

      // عرض النتيجة
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('حالة التطبيق'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('النجاح: ${result.isSuccess ? "نعم" : "لا"}'),
              Text('تحت الصيانة: ${result.isUpdating ? "نعم" : "لا"}'),
              Text('الرسالة: ${result.message}'),
              Text('اسم التطبيق: ${result.appName}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('موافق'),
            ),
          ],
        ),
      );
    } catch (e) {
      Navigator.pop(context); // إغلاق مؤشر التحميل

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فحص حالة التطبيق: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
