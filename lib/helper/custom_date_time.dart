import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomDateTime {
  static String formatTimeDifference(
      BuildContext context, DateTime backendUtcTime) {
    // Convert backend UTC time to local time
    DateTime localTime = backendUtcTime.toLocal();

    // Get the current local time
    DateTime now = DateTime.now();

    // If the backend time is in the future, clamp it to now
    if (localTime.isAfter(now)) {
      localTime = now;
    }

    // Calculate the time difference
    Duration difference = now.difference(localTime);

    debugPrint("Backend Local Time: ${localTime.toString()}");
    debugPrint("Current Time: ${now.toString()}");
    debugPrint("Time Difference: ${difference.toString()}");

    // Format the time difference
    if (difference.inMinutes < 1) {
      return context.locale.toString() == "ar" ? "الآن" : "just now";
    } else if (difference.inMinutes < 60) {
      return context.locale.toString() == "ar"
          ? "منذ ${difference.inMinutes} دقيقة"
          : "since ${difference.inMinutes} minutes ago";
    } else if (difference.inHours < 24) {
      return context.locale.toString() == "ar"
          ? "منذ ${difference.inHours} ساعة"
          : "since ${difference.inHours} hours ago";
    } else if (difference.inDays < 30) {
      return context.locale.toString() == "ar"
          ? "منذ ${difference.inDays} يوم"
          : "since ${difference.inDays} days ago";
    } else if (difference.inDays < 365) {
      int months = (difference.inDays / 30).floor();
      return context.locale.toString() == "ar"
          ? "منذ $months شهر"
          : "since $months months ago";
    } else {
      int years = (difference.inDays / 365).floor();
      return context.locale.toString() == "ar"
          ? "منذ $years سنة"
          : "since $years years ago";
    }
  }


  static String formatDate(DateTime? date) {
    if (date == null) {
      return 'غير محدد';
    } else {
      final DateFormat formatter = DateFormat('yyyy-MM-dd');
      final String formattedDate = formatter.format(date);
      return formattedDate;
    }
  }

  static String formatTime(DateTime? time) {
    if (time == null) {
      return 'غير محدد';
    } else {
      final DateFormat formatter = DateFormat('hh:mm a');
      final String formattedDate = formatter.format(time);
      return formattedDate;
    }
  }

  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) {
      return 'غير محدد';
    } else {
      final DateFormat formatter = DateFormat('yyyy-MM-dd "at" hh:mm a');
      final String formattedDate = formatter.format(dateTime);
      return formattedDate;
    }
  }

}