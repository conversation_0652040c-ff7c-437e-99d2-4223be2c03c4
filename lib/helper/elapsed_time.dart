/// You can use it...
///  int seconds = 0;
///  Timer.periodic(const Duration(seconds: 1), (timer) {
///     debugPrint('Elapsed time: ${ElapsedTime.elapsedTime(seconds)}');
///     seconds++;
///   });

class ElapsedTime {
  static String elapsedTime(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int remainingSeconds = seconds % 60;

    return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(remainingSeconds)}';
  }
}

String twoDigits(int n) {
  return n.toString().padLeft(2, '0');
}