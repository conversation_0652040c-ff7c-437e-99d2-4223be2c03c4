import 'package:bus_driver/helper/cache_helper.dart';
import 'package:bus_driver/utils/get_it_injection.dart';
import 'package:bus_driver/utils/navigation_helper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../config/global_variable.dart';
import '../config/config_base.dart';
import '../views/screens/login_screen/login_screen.dart';

class ApiLogger extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final requestPath = '${options.baseUrl}${options.path}';
    final method = options.method;
    final queryParams = options.queryParameters;
    final headers = options.headers;
    final requestData = options.data;

    debugPrint(
        '┌------------------------------------------------------------------------------');
    debugPrint('| 🚀 Request: $method $requestPath');
    debugPrint('| Headers: $headers');
    if (queryParams.isNotEmpty) {
      debugPrint('| Query Parameters: $queryParams');
    }
    if (requestData != null) {
      debugPrint('| Body: $requestData');
    }
    debugPrint(
        '└------------------------------------------------------------------------------');

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final statusCode = response.statusCode;
    final requestPath =
        '${response.requestOptions.baseUrl}${response.requestOptions.path}';
    final method = response.requestOptions.method;

    debugPrint(
        '┌------------------------------------------------------------------------------');
    debugPrint('| ✅ Response: $method $requestPath');
    debugPrint('| Status Code: $statusCode');
    debugPrint('| Response Data: ${response.data}');
    debugPrint(
        '└------------------------------------------------------------------------------');

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final statusCode = err.response?.statusCode;
    final requestPath =
        '${err.requestOptions.baseUrl}${err.requestOptions.path}';
    final method = err.requestOptions.method;

    debugPrint(
        '┌------------------------------------------------------------------------------');
    debugPrint('| ❌ Error: $method $requestPath');
    debugPrint('| Status Code: $statusCode');
    debugPrint('| Error Message: ${err.message}');
    if (err.response?.data != null) {
      debugPrint('| Error Response: ${err.response?.data}');
    }
    debugPrint(
        '└------------------------------------------------------------------------------');

    super.onError(err, handler);
  }
}

class NetworkService {
  final dio = Dio();

  NetworkService() {
    dio.options.baseUrl = ConfigBase.baseUrl;
    dio.interceptors.add(ApiLogger());
  }

  Future<Response> get({
    required String? url,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
  }) async {
    Response? response;
    try {
      response = await dio.get(
        url!,
        options: Options(headers: {
          'Authorization': "Bearer $token",
          'ln': CacheHelper.getString('lang') ?? 'en'
        }),
        queryParameters: queryParameters,
      );
    } on DioException catch (e) {
      if (e.response != null) {
        response = e.response;
        if (e.response?.statusCode == 403) {
          CacheHelper.remove('token');
          token = '';
          Navigator.pushNamedAndRemoveUntil(
            getIt<NavHelper>().navigatorKey.currentContext!,
            LoginScreen.routeName,
            (route) => false,
          );
        }
      }
    }
    return Future.value(handleResponse(response));
  }

  Future<Response> post({
    required String? url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    encoding,
  }) async {
    Response? response;
    try {
      response = await dio.post(
        url!,
        data: body,
        options: Options(headers: {
          'Authorization': "Bearer $token",
          'ln': CacheHelper.getString('lang') ?? 'en'
        }),
        queryParameters: queryParameters,
      );
    } on DioException catch (e) {
      if (e.response != null) {
        response = e.response;
        if (e.response?.statusCode == 403) {
          CacheHelper.remove('token');
          token = '';
          Navigator.pushNamedAndRemoveUntil(
            getIt<NavHelper>().navigatorKey.currentContext!,
            LoginScreen.routeName,
            (route) => false,
          );
        }
      }
    }
    return Future.value(handleResponse(response));
  }

  Future<Response> put({
    required String? url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    encoding,
  }) async {
    final authHeaders = {'Authorization': "Bearer $token"};

    Response? response;
    try {
      response = await dio.put(
        url!,
        data: body,
        queryParameters: queryParameters,
        options: Options(
          headers: isAuth ? authHeaders : headers,
          requestEncoder: encoding,
        ),
      );
    } on DioException catch (e) {
      if (e.response != null) {
        response = e.response;
      }
    }
    return Future.value(handleResponse(response));
  }

  Future<Response?> patch({
    required String? url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    encoding,
  }) async {
    final authHeaders = {'Authorization': "Bearer $token"};

    Response? response;
    try {
      response = await dio.patch(
        url!,
        data: body,
        queryParameters: queryParameters,
        options: Options(
          headers: isAuth ? authHeaders : headers,
          requestEncoder: encoding,
        ),
      );
    } on DioException catch (e) {
      if (e.response != null) {
        response = e.response;
      }
    }
    return handleResponse(response);
  }

  Future<Response?> delete({
    required String? url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, dynamic>? queryParameters,
    bool isAuth = false,
    encoding,
  }) async {
    final authHeaders = {'Authorization': "Bearer $token"};
    Response? response;
    try {
      response = await dio.delete(
        url!,
        data: body,
        queryParameters: queryParameters,
        options: Options(
          headers: isAuth ? authHeaders : headers,
          requestEncoder: encoding,
        ),
      );
    } on DioException catch (e) {
      if (e.response != null) {
        response = e.response;
      }
    }
    return handleResponse(response);
  }

  Response? handleResponse(Response? response) {
    final int statusCode = response?.statusCode ?? 500;
    if (statusCode >= 200 && statusCode < 300) {
      return response;
    } else {
      return response;
    }
  }
}
