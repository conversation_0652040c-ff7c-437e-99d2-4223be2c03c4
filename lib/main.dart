// main.dart

import 'package:bus_driver/bloc/cubit/login_cubit/login_cubit.dart';
import 'package:bus_driver/bloc/cubit/students_cubit/students_cubit.dart';
import 'package:bus_driver/bloc/cubit/trip_evening_cubit/trip_evening_cubit.dart';
import 'package:bus_driver/bloc/cubit/trip_morning_cubit/trip_morning_cubit.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:bus_driver/config/route.dart';
import 'package:bus_driver/firebase_options.dart';
import 'package:bus_driver/helper/cache_helper.dart';
import 'package:bus_driver/services/firebase_messaging_service.dart';
import 'package:bus_driver/services/notification_service/local_notification_service.dart';
import 'package:bus_driver/services/notification_service/utils/logger.dart';
import 'package:bus_driver/translations/codegen_loader.g.dart';
import 'package:bus_driver/utils/get_it_injection.dart';
import 'package:bus_driver/utils/navigation_helper.dart';
import 'package:bus_driver/views/screens/home_screen/home.dart';
import 'package:bus_driver/widgets/carousel_widget/carousel_cubit/carousel_cubit.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'bloc/cubit/ads_cubit/ads_cubit.dart';
import 'bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
import 'bloc/cubit/logout_cubit/logout_cubit.dart';
import 'bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
import 'bloc/cubit/my_bus_cubit/my_bus_cubit.dart';
import 'bloc/cubit/notifications_cubit/notifications_cubit.dart';
import 'bloc/cubit/parents_cubit/parents_cubit.dart';
import 'views/screens/languages_screen/on_boarding_language_screen.dart';

// Global variable to track Firebase initialization
bool _isFirebaseInitialized = false;

// Initialize Firebase only once
Future<void> _initializeFirebase() async {
  if (!_isFirebaseInitialized) {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      _isFirebaseInitialized = true;
      Logger.i('Firebase Core initialized');
    } catch (e) {
      if (e.toString().contains('duplicate-app')) {
        _isFirebaseInitialized = true;
        Logger.i('Firebase was already initialized');
      } else {
        rethrow;
      }
    }
  }
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await _initializeFirebase();
  Logger.firebase('Handling background message: ${message.messageId}');

  final notificationService = LocalNotificationService(
    FlutterLocalNotificationsPlugin(),
  );
  await notificationService.initialize();
  // Handle the notification...
}

Future<void> main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    Logger.i('Flutter binding initialized');

    try {
      await EasyLocalization.ensureInitialized();
      Logger.i('Localization initialized');
    } catch (e) {
      Logger.e('Failed to initialize localization: $e');
      // Fall back to default locale
      await EasyLocalization.ensureInitialized();
    }

    try {
      await _initializeFirebase();
    } catch (e) {
      Logger.e('Failed to initialize Firebase: $e');
      // Continue without Firebase for now
    }

    try {
      await init();
      await CacheHelper.init();
      Logger.i('Cache initialized');
    } catch (e) {
      Logger.e('Failed to initialize cache: $e');
      // Continue without cache
    }

    try {
      tz.initializeTimeZones();
      Logger.i('Timezone initialized');
    } catch (e) {
      Logger.e('Failed to initialize timezone: $e');
      // Continue without timezone
    }

    try {
      await FirebaseMessagingService.instance.initialize();
      Logger.i('Firebase Messaging initialized');
    } catch (e) {
      Logger.e('Failed to initialize Firebase Messaging: $e');
      // Continue without messaging
    }

    final notificationService = LocalNotificationService(
      FlutterLocalNotificationsPlugin(),
    );
    try {
      await notificationService.initialize();
      Logger.i('Notification service initialized');
    } catch (e) {
      Logger.e('Failed to initialize notifications: $e');
      // Continue without notifications
    }
    initializeFCMToken();
    token = CacheHelper.getString("token");
    Logger.i('Token retrieved: ${token != null ? 'exists' : 'null'}');

    runApp(
      EasyLocalization(
        supportedLocales: const [Locale('en'), Locale('ar')],
        path: 'assets/translations',
        fallbackLocale: const Locale('en'),
        assetLoader: const CodegenLoader(),
        child: MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => LoginCubit()),
            BlocProvider(create: (context) => LogoutCubit()),
            BlocProvider(create: (context) => StudentsCubit()),
            BlocProvider(create: (context) => ParentsCubit()),
            BlocProvider(create: (context) => MyBusCubit()),
            BlocProvider(create: (context) => MorningTripCubit()),
            BlocProvider(create: (context) => EveningTripCubit()),
            BlocProvider(create: (context) => TripMorningCubit()),
            BlocProvider(create: (context) => TripEveningCubit()),
            BlocProvider(create: (context) => NotificationsCubit()),
            BlocProvider(create: (context) => AdsCubit()),
            BlocProvider(create: (context) => CarouselCubit()),
          ],
          child: const MyApp(),
        ),
      ),
    );
  } catch (e, stackTrace) {
    Logger.e('Fatal error during app initialization', e, stackTrace);
    // Show error UI instead of crashing
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text(
              'Failed to start app. Please try again.\nError: $e',
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(428, 926),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'Busaty - Bus'.tr(),
          scaffoldMessengerKey: snackBarKey,
          navigatorKey: getIt<NavHelper>().navigatorKey,
          debugShowCheckedModeBanner: false,
          localizationsDelegates: context.localizationDelegates,
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          theme: ThemeData(
            primarySwatch: Colors.blue,
            platform: TargetPlatform.iOS, // Ensure proper iOS rendering
          ),
          routes: routes,
          builder: (context, child) {
            // Add error boundary
            ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
              return Scaffold(
                body: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Something went wrong.\nPlease restart the app.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.red[700]),
                    ),
                  ),
                ),
              );
            };
            return child ?? const SizedBox.shrink();
          },
          home: Builder(
            builder: (context) {
              try {
                return token != null
                    ? const HomeScreen()
                    : const OnBoardingLanguageScreen();
              } catch (e) {
                Logger.e('Error building initial screen: $e');
                return const Scaffold(
                  body: Center(
                    child: Text(
                      'Failed to load initial screen.\nPlease restart the app.',
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }
            },
          ),
        );
      },
    );
  }
}
