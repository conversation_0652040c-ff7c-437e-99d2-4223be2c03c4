import 'package:flutter/material.dart';

import '../data/repo/app_update_status_repo.dart';

enum AppType { attendants, parents, schools }

class AppUpdateService {
  final AppUpdateStatusRepo _appUpdateStatusRepo = AppUpdateStatusRepo();

  /// Check if the current app is under maintenance
  /// This method should be called during app startup
  Future<AppUpdateResult> checkCurrentAppStatus({
    required String appName,
    required AppType appType,
  }) async {
    try {
      // Since this app is specifically for attendants, we use the simplified method
      final result = await _appUpdateStatusRepo.checkCurrentAppStatus(
        appName: appName,
      );

      return AppUpdateResult(
        isSuccess: result.success ?? false,
        isUpdating: result.isUpdating ?? false,
        message: result.message ?? '',
        appName: result.name ?? appName,
      );
    } catch (e) {
      debugPrint('Error checking app update status: $e');
      return AppUpdateResult(
        isSuccess: false,
        isUpdating: false,
        message: 'Failed to check app status: $e',
        appName: appName,
      );
    }
  }

  /// Check if app should show maintenance screen
  Future<bool> shouldShowMaintenanceScreen({
    required String appName,
    required AppType appType,
  }) async {
    final result = await checkCurrentAppStatus(
      appName: appName,
      appType: appType,
    );

    return result.isSuccess && result.isUpdating;
  }

  /// Check if app actions should be blocked
  Future<bool> shouldBlockActions({
    required String appName,
    required AppType appType,
  }) async {
    final result = await checkCurrentAppStatus(
      appName: appName,
      appType: appType,
    );

    return result.isSuccess && result.isUpdating;
  }
}

/// Result wrapper for app update status check
class AppUpdateResult {
  final bool isSuccess;
  final bool isUpdating;
  final String message;
  final String appName;

  AppUpdateResult({
    required this.isSuccess,
    required this.isUpdating,
    required this.message,
    required this.appName,
  });

  @override
  String toString() {
    return 'AppUpdateResult(isSuccess: $isSuccess, isUpdating: $isUpdating, message: $message, appName: $appName)';
  }
}
