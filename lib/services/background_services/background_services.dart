// // background_services.dart
// import 'dart:async';
// import 'dart:ui';

// import 'package:bus_driver/views/screens/home_screen/location_service.dart';
// import 'package:bus_driver/views/screens/home_screen/socket_service.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_background_service/flutter_background_service.dart';
// import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
// import 'package:logger/logger.dart';
// import 'package:workmanager/workmanager.dart';

// class BackgroundLocationService {
//   static final Logger _logger = Logger();
//   static Timer? _connectionTimer;
//   static StreamSubscription? _locationSubscription;
//   static bool _isTracking = false;

//   static Future<void> initializeService() async {
//     final service = FlutterBackgroundService();

//     // Initialize Workmanager for Android with better battery optimization
//     await Workmanager().initialize(
//       callbackDispatcher,
//       isInDebugMode: false,
//     );

//     // Register periodic task with adaptive frequency
//     await Workmanager().registerPeriodicTask(
//       'locationTracking',
//       'locationTrackingTask',
//       frequency: Duration(minutes: 30), // Increased to save battery
//       constraints: Constraints(
//         networkType: NetworkType.connected,
//         requiresBatteryNotLow: true,
//         requiresCharging: false,
//         requiresDeviceIdle: false,
//       ),
//       existingWorkPolicy: ExistingWorkPolicy.replace,
//     );

//     // Configure the background service with better power management
//     await service.configure(
//       androidConfiguration: AndroidConfiguration(
//         onStart: onAndroidStart,
//         isForegroundMode: true,
//         autoStart: true,
//         autoStartOnBoot: true,
//         initialNotificationTitle: 'Location Service',
//         initialNotificationContent: 'Running in background',
//         foregroundServiceNotificationId: 888,
//       ),
//       iosConfiguration: IosConfiguration(
//         autoStart: true,
//         onForeground: onIosStart,
//         onBackground: onIosBackground,
      
//       ),
//     );

//     await service.startService();
//     await performLocationTracking();
//   }

//   @pragma('vm:entry-point')
//   static void callbackDispatcher() {
//     Workmanager().executeTask((task, inputData) async {
//       switch (task) {
//         case 'locationTrackingTask':
//           await performLocationTracking();
//           break;
//       }
//       return Future.value(true);
//     });
//   }

//   static Future<void> performLocationTracking() async {
//     if (_isTracking) return; // Prevent multiple tracking instances

//     try {
//       _isTracking = true;
//       final NewLocationService locationService = NewLocationService();

//       // Initialize with retry mechanism
//       int retryCount = 0;
//       while (retryCount < 3) {
//         try {
//           await locationService.determinePosition();
//           break;
//         } catch (e) {
//           retryCount++;
//           _logger.w("Retry $retryCount: Failed to determine position: $e");
//           await Future.delayed(Duration(seconds: 5));
//           if (retryCount == 3) throw e;
//         }
//       }

//       // Clean up existing timer
//       _connectionTimer?.cancel();

//       // Initialize socket connection with retry mechanism
//       SocketService.initialize();
//       _connectionTimer = Timer.periodic(Duration(seconds: 30), (timer) {
//         SocketService.ensureConnection();
//       });

//       // Clean up existing subscription
//       await _locationSubscription?.cancel();

//       // Start location tracking with error handling
//       _locationSubscription = locationService.locationStream.listen(
//         (GeoPoint location) {
//           final locationData = {
//             'latitude': location.latitude,
//             'longitude': location.longitude,
//             'timestamp': DateTime.now().toIso8601String(),
//             'batteryLevel': 0, // TODO: Implement battery level monitoring
//           };
//           SocketService.sendLocationUpdate(locationData);
//         },
//         onError: (error) {
//           _logger.e("Location stream error: $error");
//           _restartTracking();
//         },
//       );
//     } catch (e) {
//       _logger.e("Error in performLocationTracking: $e");
//       _isTracking = false;
//       // Attempt to restart tracking after delay
//       Future.delayed(Duration(minutes: 1), _restartTracking);
//     }
//   }

//   static void _restartTracking() async {
//     _isTracking = false;
//     await stopTracking();
//     await performLocationTracking();
//   }

//   static Future<void> stopTracking() async {
//     _isTracking = false;
//     await _locationSubscription?.cancel();
//     _locationSubscription = null;
//     _connectionTimer?.cancel();
//     _connectionTimer = null;
//   }

//   @pragma('vm:entry-point')
//   static Future<void> onAndroidStart(ServiceInstance service) async {
//     DartPluginRegistrant.ensureInitialized();
//     // Enable wake lock to prevent device sleep
//     if (service is AndroidServiceInstance) {
//       service.setAsForegroundService();
//       service.setForegroundNotificationInfo(
//         title: 'Location Tracking Active',
//         content: 'Your location is being tracked in background',
//       );
//     }

//     // Initialize socket connection with retry mechanism
//     SocketService.initialize();
//     _connectionTimer = Timer.periodic(Duration(seconds: 30), (timer) {
//       SocketService.ensureConnection();
//     });

//     // Initialize the location service
//     final NewLocationService locationService = NewLocationService();
//     await locationService.determinePosition();

//     // Start location tracking and send updates through socket
//     _locationSubscription =
//         locationService.locationStream.listen((GeoPoint location) {
//       if (service is AndroidServiceInstance) {
//         service.setForegroundNotificationInfo(
//           title: 'Location Tracking Active',
//           content: 'Location: ${location.latitude}, ${location.longitude}',
//         );
//       }

//       final locationData = {
//         'latitude': location.latitude,
//         'longitude': location.longitude,
//         'timestamp': DateTime.now().toIso8601String(),
//         'batteryLevel': 0, // TODO: Implement battery level monitoring
//       };
//       SocketService.sendLocationUpdate(locationData);

//       // Update notification with latest location
//       service.invoke(
//         'update',
//         locationData,
//       );
//     });

//     locationService.startLocationTracking();

//     service.on('stopService').listen((event) {
//       locationService.dispose();
//       SocketService.disconnect();
//       service.stopSelf();
//     });

//     // More frequent periodic updates to keep the service alive
//     Timer.periodic(const Duration(seconds: 30), (timer) async {
//       if (service is AndroidServiceInstance) {
//         if (await service.isForegroundService()) {
//           // Ensure socket connection is maintained
//           SocketService.ensureConnection();

//           // Keep the service alive
//           service.setForegroundNotificationInfo(
//             title: 'Location Tracking Active',
//             content: 'Service is running...',
//           );
//         }
//       }
//     });
//   }

//   @pragma('vm:entry-point')
//   static void onIosStart(ServiceInstance service) async {
//     DartPluginRegistrant.ensureInitialized();

//     try {
//       final NewLocationService locationService = NewLocationService();
//       await locationService.determinePosition();

//       // Initialize socket connection with retry mechanism
//       SocketService.initialize();
      
//       // More robust connection management
//       _connectionTimer?.cancel();
//       _connectionTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
//         try {
//           SocketService.ensureConnection();
//         } catch (e) {
//           _logger.e("Error maintaining socket connection: $e");
//         }
//       });

//       // Clean up existing subscription
//       await _locationSubscription?.cancel();

//       // Start location tracking with error handling
//       _locationSubscription = locationService.locationStream.listen(
//         (GeoPoint location) {
//           try {
//             final locationData = {
//               'latitude': location.latitude,
//               'longitude': location.longitude,
//               'timestamp': DateTime.now().toIso8601String(),
//               'batteryLevel': 0,
//             };
//             SocketService.sendLocationUpdate(locationData);
//           } catch (e) {
//             _logger.e("Error sending location update: $e");
//           }
//         },
//         onError: (error) {
//           _logger.e("Location stream error: $error");
//           _restartTracking();
//         },
//         cancelOnError: false,
//       );

//       locationService.startLocationTracking();

//       service.on('stopService').listen((event) {
//         try {
//           locationService.dispose();
//           SocketService.disconnect();
//           service.stopSelf();
//         } catch (e) {
//           _logger.e("Error stopping service: $e");
//         }
//       });
//     } catch (e) {
//       _logger.e("Error in iOS background service: $e");
//       // Attempt to recover
//       Future.delayed(Duration(seconds: 30), () => onIosStart(service));
//     }
//   }

//   @pragma('vm:entry-point')
//   static Future<bool> onIosBackground(ServiceInstance service) async {
//     WidgetsFlutterBinding.ensureInitialized();
//     DartPluginRegistrant.ensureInitialized();
//     return true;
//   }

//   // Method to start the background service
//   static void startBackgroundService() {
//     try {
//       FlutterBackgroundService().startService();
//       _logger.e("Background service started");
//     } catch (e) {
//       _logger.e("Error starting background service: $e");
//     }
//   }

//   // Method to stop the background service
//   static void stopBackgroundService() {
//     try {
//       FlutterBackgroundService().invoke('stopService');
//       _logger.e("Background service stopped");
//     } catch (e) {
//       _logger.e("Error stopping background service: $e");
//     }
//   }
// }
