// import 'package:bus_driver/config/config_base.dart';
// import 'package:bus_driver/config/global_variable.dart';
// import 'package:flutter_background_geolocation/flutter_background_geolocation.dart' as bg;
// import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:socket_io_client/socket_io_client.dart' as io;

// class BackgroundService {
//   io.Socket? socket;
//   Position? position;

//   // إيقاف الاتصال وقطع الخدمة عند غلق التطبيق
//   void stopService() {
//     bg.BackgroundGeolocation.stop();
//   }

//   // إرسال بيانات الموقع إلى الـ Socket.IO
//   void sendLocationToSocket(Position position) {
//     Map<String, dynamic> locationJson = {
//       "latitude": position.latitude,
//       "longitude": position.longitude,
//       // أضف أي بيانات أخرى تحتاجها هنا
//     };
//     socket?.emit('location', locationJson);
//   }

//   // بدء الاتصال وبدء خدمة الخلفية
//   void startBackgroundService() {
//     // إعداد الـ Socket.IO
//     // socket = io.io('https://yourserver.com', <String, dynamic>{
//     //   'transports': ['websocket'],
//     //   'autoConnect': true,
//     // });

//  socket = io.io(ConfigBase.socketUrl, <String, dynamic>{
//     'query': {
//       'token': socketToken,
//     },
//     'transports': ['websocket'],
//     'autoConnect': true,
//   });
//     // تفعيل خدمات الموقع في الخلفية
//     bg.BackgroundGeolocation.onLocation((bg.Location location) {
//       // عندما يتم تحديث الموقع في الخلفية
//       sendLocationToSocket(
        
//         GeoPoint(
//               latitude: position!.latitude, longitude: position!.longitude) as Position);
//     });

//     // تفعيل خدمة الخلفية
//     bg.BackgroundGeolocation.ready(bg.Config(
//       enableHeadless: true,
//       desiredAccuracy: 3,
//       distanceFilter: 10.0,
//       stopOnTerminate: false, // استمر في العمل حتى إذا تم إغلاق التطبيق
//       startOnBoot: true, // بدء الخدمة تلقائيًا عند إعادة تشغيل الجهاز
//       notificationTitle: 'Tracking Location',
//       notificationText: 'Sending location data in the background',
    
//     )).then((bg.State state) {
//       bg.BackgroundGeolocation.start(); // بدء تتبع الموقع
//     });
//   }
// }
