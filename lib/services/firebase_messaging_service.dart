import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import '../helper/cache_helper.dart';
import 'notification_service/utils/logger.dart';

class FirebaseMessagingService {
  static FirebaseMessagingService? _instance;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String? _token;

  FirebaseMessagingService._();

  static FirebaseMessagingService get instance {
    _instance ??= FirebaseMessagingService._();
    return _instance!;
  }

  Future<void> initialize() async {
    try {
      // Request permission first
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Configure foreground notification presentation options
        await _firebaseMessaging.setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: true,
        );

        // On iOS, we need to wait for the APNS token
        if (Platform.isIOS) {
          // Wait for APNS token with retry
          for (int i = 0; i < 3; i++) {
            final apnsToken = await _firebaseMessaging.getAPNSToken();
            if (apnsToken != null) {
              Logger.firebase('APNS Token received: $apnsToken');
              break;
            }
            if (i < 2) {
              await Future.delayed(const Duration(seconds: 1));
            }
          }
        }

        // Now try to get FCM token
        _token = await _firebaseMessaging.getToken();
        if (_token != null) {
          Logger.firebase('FCM Token received: $_token');
          CacheHelper.putString("fcmToken", _token!);
        }
      } else {
        Logger.e('Push notification permission denied');
      }
    } catch (e) {
      Logger.e('Error initializing Firebase Messaging: $e');
    }
  }

  Future<String?> getToken() async {
    if (_token != null) return _token;
    
    try {
      if (Platform.isIOS) {
        final apnsToken = await _firebaseMessaging.getAPNSToken();
        if (apnsToken == null) {
          Logger.e('APNS token not available');
          return null;
        }
      }
      
      _token = await _firebaseMessaging.getToken();
      if (_token != null) {
        CacheHelper.putString("fcmToken", _token!);
      }
      return _token;
    } catch (e) {
      Logger.e('Error getting FCM token: $e');
      return null;
    }
  }
}
