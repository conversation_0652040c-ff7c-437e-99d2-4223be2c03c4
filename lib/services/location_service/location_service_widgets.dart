import 'package:geolocator/geolocator.dart';

import '../../../utils/helper.dart';
import '../../../widgets/custom_alert_dialog.dart';


class LocationServiceWidgets {
  static Future enableLocationServiceAlert() async {
    globalAlertDialogue("Please enable location services to get the best routing experience",);

  }

  static Future enableLocationAccessAlert() async {
    globalAlertDialogue("Please give the app location permission to get the best routing experience",
        onOk: ()async{
      goBack();
      await Geolocator.openLocationSettings();
    });
  }
}
