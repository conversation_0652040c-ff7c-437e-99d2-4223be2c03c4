import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'notification_exception.dart';

class ErrorHandler {
  static NotificationException handleError(dynamic error,
      [StackTrace? stackTrace]) {
    if (error is NotificationException) {
      return error;
    }

    if (error is FirebaseException) {
      return NotificationException(
        'Firebase Error: ${error.message}',
        code: error.code,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (error is PlatformException) {
      return NotificationException(
        'Platform Error: ${error.message}',
        code: error.code,
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    return NotificationException(
      error?.toString() ?? 'An unknown error occurred',
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  static void logError(NotificationException error) {
    // In a real app, you would send this to a logging service
    debugPrint('ERROR: ${error.toString()}');
    if (error.originalError != null) {
      debugPrint('Original error: ${error.originalError}');
    }
    if (error.stackTrace != null) {
      debugPrint('Stack trace: ${error.stackTrace}');
    }
  }
}
