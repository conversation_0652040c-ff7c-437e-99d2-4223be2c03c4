import 'dart:async';
import 'dart:io';
import 'package:bus_driver/helper/cache_helper.dart';
import 'package:bus_driver/services/notification_service/utils/logger.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'errors/error_handler.dart';
import 'errors/notification_exception.dart';
import 'notification_payload.dart';
import 'notification_service_interface.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import '../firebase_messaging_service.dart';

class LocalNotificationService implements INotificationService {
  final FlutterLocalNotificationsPlugin _notifications;
  final _notificationController =
      StreamController<NotificationPayload>.broadcast();
  final _notificationTapController =
      StreamController<NotificationPayload>.broadcast();

  LocalNotificationService(this._notifications);

  static Future<String?> get firebaseToken async {
    return FirebaseMessagingService.instance.getToken();
  }

  @override
  Future<void> initialize() async {
    try {
      Logger.i('Initializing notification service');
      const androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      final initialized = await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (!initialized!) {
        Logger.e('Failed to initialize notifications');
        throw NotificationException(
          'Failed to initialize local notifications',
          code: 'init_failed',
        );
      }

      // Configure how to handle messages when the app is in foreground
      await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
        alert: false,  // Don't automatically show alert
        badge: true,
        sound: true,
      );

      // Get the token after everything is set up
      final token = await firebaseToken;
      Logger.firebase('Firebase initialized with token: $token');

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        Logger.firebase('Received foreground message: ${message.notification?.title}');
        final notification = message.notification;
        if (notification != null) {
          showNotification(
            NotificationPayload(
              title: notification.title ?? '',
              body: notification.body ?? '',
              data: message.data,
            ),
          );
        }
      });
    } catch (error, stackTrace) {
      Logger.e('Notification initialization failed', error, stackTrace);
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    try {
      if (response.payload != null) {
        final notification = NotificationPayload(
          title: response.notificationResponseType ==
                  NotificationResponseType.selectedNotification
              ? 'Notification Tapped'
              : 'Notification Action',
          body: response.actionId ?? 'Notification tapped',
          data: {'payload': response.payload},
        );
        _notificationTapController.add(notification);
      }
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
    }
  }

  @override
  Future<void> showNotification(NotificationPayload payload) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'default_channel',
        'Default Channel',
        channelDescription: 'Default notification channel',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final id = DateTime.now().millisecondsSinceEpoch.remainder(100000);
      await _notifications.show(
        id,
        payload.title,
        payload.body,
        details,
        payload: payload.data?.toString(),
      );

      _notificationController.add(payload);
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Future<void> showScheduledNotification({
    required NotificationPayload payload,
    required DateTime scheduledDate,
  }) async {
    try {
      if (scheduledDate.isBefore(DateTime.now())) {
        throw NotificationException(
          'Scheduled date must be in the future',
          code: 'invalid_schedule_time',
        );
      }

      const androidDetails = AndroidNotificationDetails(
        'scheduled_channel',
        'Scheduled Channel',
        channelDescription: 'Channel for scheduled notifications',
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final id = DateTime.now().millisecondsSinceEpoch.remainder(100000);
      await _notifications.zonedSchedule(
        id,
        payload.title,
        payload.body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        details,
        androidScheduleMode: AndroidScheduleMode.alarmClock,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload.data?.toString(),
      );
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Future<void> cancelNotification(int id) async {
    try {
      await _notifications.cancel(id);
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  @override
  Stream<NotificationPayload> get onNotificationReceived =>
      _notificationController.stream;

  @override
  Stream<NotificationPayload> get onNotificationTapped =>
      _notificationTapController.stream;

  void dispose() {
    _notificationController.close();
    _notificationTapController.close();
  }

  Future<void> _setupNotificationChannels() async {
    const androidChannel = AndroidNotificationChannel(
      'default_channel', // same as in AndroidManifest.xml
      'Default Channel',
      description: 'Default notification channel',
      importance: Importance.high,
      enableVibration: true,
      enableLights: true,
      showBadge: true,
    );

    await _notifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  Logger.firebase(
      'Handling background message: ${message.notification?.title}');
  final notification = message.notification;
  if (notification != null) {
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    await flutterLocalNotificationsPlugin.show(
      message.hashCode,
      notification.title,
      notification.body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel',
          'Default Channel',
          channelDescription: 'Default notification channel',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
    );
  }
}
