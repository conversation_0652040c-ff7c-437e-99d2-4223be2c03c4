class NotificationPayload {
  final String title;
  final String body;
  final Map<String, dynamic>? data;
  final String? imageUrl;

  NotificationPayload({
    required this.title,
    required this.body,
    this.data,
    this.imageUrl,
  });

  factory NotificationPayload.fromMap(Map<String, dynamic> map) {
    return NotificationPayload(
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      data: map['data'],
      imageUrl: map['imageUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'body': body,
      if (data != null) 'data': data,
      if (imageUrl != null) 'imageUrl': imageUrl,
    };
  }
}
