import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'errors/error_handler.dart';
import 'errors/notification_exception.dart';
import 'fcm_notification_service.dart';
import 'local_notification_service.dart';
import 'notification_service_interface.dart';

class NotificationServiceFactory {
  static Future<void> initialize() async {
    try {
      final getIt = GetIt.instance;

      // Register FCM Service
      if (!getIt.isRegistered<FCMNotificationService>()) {
        final fcmService = FCMNotificationService(FirebaseMessaging.instance);
        await fcmService.initialize().catchError((error, stackTrace) {
          final notificationError = ErrorHandler.handleError(error, stackTrace);
          ErrorHandler.logError(notificationError);
          throw notificationError;
        });
        getIt.registerSingleton<FCMNotificationService>(fcmService);
      }

      // Register Local Notification Service
      if (!getIt.isRegistered<LocalNotificationService>()) {
        final localService = LocalNotificationService(FlutterLocalNotificationsPlugin());
        await localService.initialize().catchError((error, stackTrace) {
          final notificationError = ErrorHandler.handleError(error, stackTrace);
          ErrorHandler.logError(notificationError);
          throw notificationError;
        });
        getIt.registerSingleton<LocalNotificationService>(localService);
      }
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }

  static INotificationService getService(NotificationServiceType type) {
    try {
      final getIt = GetIt.instance;
      switch (type) {
        case NotificationServiceType.fcm:
          if (!getIt.isRegistered<FCMNotificationService>()) {
            throw NotificationException(
              'FCM service not initialized',
              code: 'fcm_not_initialized',
            );
          }
          return getIt<FCMNotificationService>();
        case NotificationServiceType.local:
          if (!getIt.isRegistered<LocalNotificationService>()) {
            throw NotificationException(
              'Local notification service not initialized',
              code: 'local_not_initialized',
            );
          }
          return getIt<LocalNotificationService>();
      }
    } catch (error, stackTrace) {
      final notificationError = ErrorHandler.handleError(error, stackTrace);
      ErrorHandler.logError(notificationError);
      rethrow;
    }
  }
}

enum NotificationServiceType {
  fcm,
  local,
}
