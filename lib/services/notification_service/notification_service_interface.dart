import 'notification_payload.dart';

abstract class INotificationService {
  Future<void> initialize();
  Future<void> showNotification(NotificationPayload payload);
  Future<void> showScheduledNotification({
    required NotificationPayload payload,
    required DateTime scheduledDate,
  });
  Future<void> cancelNotification(int id);
  Future<void> cancelAllNotifications();
  Stream<NotificationPayload> get onNotificationReceived;
  Stream<NotificationPayload> get onNotificationTapped;
}
