import 'package:flutter/material.dart';

class Logger {
  static void d(String message) {
    debugPrint('💡 DEBUG: $message');
  }

  static void i(String message) {
    debugPrint('ℹ️ INFO: $message');
  }

  static void w(String message) {
    debugPrint('⚠️ WARN: $message');
  }

  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    debugPrint('❌ ERROR: $message');
    if (error != null) {
      debugPrint('Error details: $error');
    }
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }
  }

  static void api(String message) {
    debugPrint('🌐 API: $message');
  }

  static void firebase(String message) {
    debugPrint('🔥 FIREBASE: $message');
  }
}
