// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader {
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>> load(String fullPath, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String, dynamic> en = {
    "Busaty - Bus": "Busaty - Bus",
    "home": "Home",
    "login": "Login",
    "email": "Email",
    "username": "Userna<PERSON>",
    "password": "Password",
    "forgetPassword": "Forget Password ? ",
    "remember": "Remember",
    "notHaveAccount": "Not Have Account ? ",
    "busName": "Bus name",
    "createAccount": "Create Account",
    "newPassword": "New Password",
    "againPassword": "Write Again Password",
    "notFound": "not Found",
    "changePassword": "Change Password",
    "signup": "Signup",
    "name": "Name",
    "phoneNumber": "Phone Number",
    "confirmPassword": "Confirm Password",
    "haveAccount": "Have Account ? ",
    "forget": "Forget Password",
    "sendCodeRegister":
        "code will be sent to the registered email to recover the account",
    "sendCodeAgain": "send code again",
    "passwordChangeSuccess": "Password Change Success",
    "goHome": "Go to Home",
    "oldPassword": "Old Password",
    "save": "Save",
    "sendCode": "Send Code",
    "next": "Next",
    "trips": "Trips",
    "busLocation": "Bus Current Location",
    "setting": "Setting",
    "myBus": "My Bus",
    "morningTrips": "Morning Trip",
    "eveningTrips": "Evening Trip",
    "busData": "Bus Data",
    "showStudentBus": "Show Bus students",
    "addressOnMap": "Address on the map",
    "parents": "Parents",
    "profile": "Profile",
    "languages": "Languages",
    "help": "Help",
    "english": "English",
    "arabic": "Arabic",
    "updateProfile": "Update Profile",
    "add": "Add",
    "showStudent": "Show Student",
    "showParent": "Show Parent",
    "addressStudentOnMap": "Address Student on the map",
    "searchForStudent": "Search for student",
    "studentBus": "Bus Students",
    "address": "Address",
    "stage": "Stage",
    "show": "Show",
    "startTrip": "Start Trip",
    "sendNotification": "Send Notification",
    "students": "Students",
    "absence": "Absence",
    "endTrip": "End Trip",
    "studentData": "Student Data",
    "logout": "Logout",
    "driver": "Driver",
    "supervisor": "Supervisor",
    "good_morning": "Good Morning",
    "good_evening": "Good Evening",
    "line": "Line",
    "school": "School",
    "bus_number": "Bus Number",
    "students_not_found": "Students Not Found",
    "parents_not_found": "Parents Not Found",
    "blood_type": "Blood Type",
    "birth_date": "Birth Date",
    "code": "First Code",
    "secret_code": "Secret Code",
    "notification": "Notify",
    "call": "Call",
    "waiting": "Waiting",
    "absent_students": "Absent",
    "absent": "Absent",
    "on_bus": "On Bus",
    "move_to_present": "Move To Present",
    "move_to_waiting": "Move To Waiting",
    "move_to_bus": "Move To Bus",
    "student_address": "Student Address",
    "arrived_home": "Arrived Home",
    "student_arrived_home": "Arrived Home",
    "notifications": "Notifications",
    "sure_end_trip": "Are you sure to end the trip?",
    "sure_start_trip": "Are you sure to start the trip?",
    "note": "notes",
    "yes": "Yes",
    "no": "No",
    "deleteAccountTitle": "Delete Account Confirmation",
    "deleteAccountConfirm":
        "Are you sure you want to delete your account permanently?",
    "deleteAccountNote":
        "All your data will be deleted from our system automatically after 30 days",
    "deleteAccount": "Delete Account",
    "cancel": "Cancel",
    "contact_us": "Contact Us",
    "get_in_touch": "Get in Touch",
    "wed_love_to_hear": "We'd love to hear from you",
    "enter_your_name": "Enter your name",
    "enter_your_email": "Enter your email",
    "describe_problem": "Describe your problem or feedback",
    "please_enter_name": "Please enter your name",
    "please_enter_email": "Please enter your email",
    "please_valid_email": "Please enter a valid email",
    "please_describe_problem": "Please describe your problem",
    "message_too_long": "Message cannot be longer than 1000 characters",
    "contact_directly": "Or contact us directly:",
    "copy": "Copy",
    "email_copied": "Email copied to clipboard",
    "sending": "Sending...",
    "email_sent": "Email sent successfully!",
    "failed_to_send": "Failed to send email: {error}",
    "trip_closed_start_trip": "Trip is closed now, click start trip to show students"
  };
  static const Map<String, dynamic> ar = {
    "Busaty - Bus": "باصاتي - الباص",
    "home": "رئيسية",
    "login": "تسجيل الدخول",
    "email": "البريد الالكتروني",
    "username": "اسم المستخدم",
    "note": "ملاحطات",
    "password": "كلمة المرور",
    "forgetPassword": "نسيت كلمة المرور ؟ ",
    "remember": "تذكر",
    "notHaveAccount": "ليس لديك حساب ؟ ",
    "createAccount": "إنشاء حساب",
    "newPassword": "كلمة السر الجديدة",
    "againPassword": "إعادة كلمة المرور",
    "changePassword": "تغيير كلمة المرور",
    "signup": "تسجيل",
    "name": "الاسم",
    "phoneNumber": "رقم الهاتف",
    "confirmPassword": "تأكيد كلمة المرور",
    "haveAccount": "لديك حساب ؟ ",
    "forget": "نسيت كلمة المرور",
    "sendCodeRegister":
        "سيتم ارسال كود الي البريد الالكتروني المسجل به لإستعادة الحساب",
    "sendCodeAgain": "إرسال الكود مرة أخرى",
    "passwordChangeSuccess": "لقد تم تغيير كلمة السر بنجاح",
    "goHome": "الذهاب إلى الرئيسية",
    "oldPassword": "كلمة السر القديمة",
    "save": "حفظ",
    "sendCode": "إرسال الكود",
    "next": "متابعة",
    "trips": "الرحلات",
    "busLocation": "موقع الباص الحالي",
    "setting": "الإعدادات",
    "myBus": "الباص الخاص بي",
    "morningTrips": "رحلة الصباح",
    "eveningTrips": "رحلة المساء",
    "notFound": "لايوجد",
    "busData": "بيانات الباص",
    "showStudentBus": "عرض طلاب الباص",
    "addressOnMap": "العنوان على الخريطة",
    "parents": "أولياء الأمور",
    "profile": "الملف الشخصي",
    "languages": "اللغة",
    "busName": "اسم الباص",
    "help": "مساعدة",
    "english": "انجليزي",
    "arabic": "عربي",
    "updateProfile": "تعديل الملف الشخصي",
    "add": "إضافة",
    "showStudent": "عرض الطالب",
    "showParent": "عرض أولياء الأمور",
    "addressStudentOnMap": "عنوان الطالب على الخريطة",
    "searchForStudent": "بحث عن طالب",
    "studentBus": "طلاب الباص",
    "address": "العنوان",
    "stage": "المرحلة",
    "show": "عرض",
    "startTrip": "بدء الرحلة",
    "sendNotification": "إرسال إشعار",
    "students": "الطلاب",
    "absence": "الغياب",
    "endTrip": "إنهاء الرحلة",
    "studentData": "بيانات الطالب",
    "logout": "تسجيل الخروج",
    "driver": "سائق",
    "supervisor": "مشرف",
    "good_morning": "صباح الخير",
    "good_evening": "مساء الخير",
    "line": "خط",
    "school": "مدرسة",
    "bus_number": "رقم اللوحة",
    "students_not_found": "لا يوجد طلاب",
    "parents_not_found": "لا يوجد أولياء أمور",
    "blood_type": "فصبلة الدم",
    "birth_date": "تاريخ الميلاد",
    "code": "الكود الأول",
    "secret_code": "الكود السري",
    "notification": "إشعار",
    "call": "اتصال",
    "waiting": "منتظرين",
    "absent_students": "غائبين",
    "absent": "غائب",
    "on_bus": "ركب الباص",
    "move_to_present": "نقل إلى حاضر",
    "move_to_waiting": "نقل إلى منتظرين",
    "move_to_bus": "نقل إلى الباص",
    "student_address": "عنوان الطالب",
    "arrived_home": "وصلوا المنزل",
    "student_arrived_home": "وصل المنزل",
    "notifications": "الإشعارات",
    "sure_end_trip": "هل أنت متأكد من إنهاء الرحلة؟",
    "sure_start_trip": "هل أنت متأكد من بدء الرحلة؟",
    "yes": "نعم",
    "no": "لا",
    "deleteAccountTitle": "تأكيد حذف الحساب",
    "deleteAccountConfirm": "هل أنت متأكد من حذف حسابك بشكل نهائي؟",
    "deleteAccountWarning": "هل أنت متأكد من حذف حسابك نهائياً؟",
    "deleteAccountNote":
        "سيتم حذف جميع بياناتك من نظامنا تلقائياً بعد 30 يوماً",
    "deleteAccount": "حذف الحساب",
    "cancel": "إلغاء",
    "contact_us": "اتصل بنا",
    "get_in_touch": "ابقى على تواصل",
    "wed_love_to_hear": "يسعدنا أن نسمع منك",
    "enter_your_name": "أدخل اسمك",
    "enter_your_email": "أدخل بريدك الإلكتروني",
    "describe_problem": "صف مشكلتك أو ملاحظاتك",
    "please_enter_name": "الرجاء إدخال اسمك",
    "please_enter_email": "الرجاء إدخال بريدك الإلكتروني",
    "please_valid_email": "الرجاء إدخال بريد إلكتروني صحيح",
    "please_describe_problem": "الرجاء وصف مشكلتك",
    "message_too_long": "لا يمكن أن تكون الرسالة أطول من 1000 حرف",
    "contact_directly": "أو اتصل بنا مباشرة:",
    "copy": "نسخ",
    "email_copied": "تم نسخ البريد الإلكتروني",
    "sending": "جاري الإرسال...",
    "email_sent": "تم إرسال البريد الإلكتروني بنجاح!",
    "failed_to_send": "فشل في إرسال البريد الإلكتروني: {error}",
    "trip_closed_start_trip": "الرحلة مغلقة الان ، قم بالنقر على بدء الرحلة لعرض الطلاب"
  };
  static const Map<String, Map<String, dynamic>> mapLocales = {
    "en": en,
    "ar": ar
  };
}
