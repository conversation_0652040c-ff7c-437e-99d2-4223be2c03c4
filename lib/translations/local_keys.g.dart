// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class AppStrings {
  static const home = 'home';
  static const login = 'login';
  static const email = 'email';
  static const username = 'username';
  static const password = 'password';
  static const forgetPassword = 'forgetPassword';
  static const remember = 'remember';
  static const notHaveAccount = 'notHaveAccount';
  static const createAccount = 'createAccount';
  static const newPassword = 'newPassword';
  static const againPassword = 'againPassword';
  static const changePassword = 'changePassword';
  static const signup = 'signup';
  static const name = 'name';
  static const busName = 'busName';

  static const phoneNumber = 'phoneNumber';
  static const confirmPassword = 'confirmPassword';
  static const haveAccount = 'haveAccount';
  static const forget = 'forget';
  static const sendCodeRegister = 'sendCodeRegister';
  static const sendCodeAgain = 'sendCodeAgain';
  static const passwordChangeSuccess = 'passwordChangeSuccess';
  static const goHome = 'goHome';
  static const oldPassword = 'oldPassword';
  static const save = 'save';
  static const sendCode = 'sendCode';
  static const next = 'next';
  static const trips = 'trips';
  static const busLocation = 'busLocation';
  static const setting = 'setting';
  static const myBus = 'myBus';
  static const morningTrips = 'morningTrips';
  static const eveningTrips = 'eveningTrips';
  static const busData = 'busData';
  static const showStudentBus = 'showStudentBus';
  static const addressOnMap = 'addressOnMap';
  static const parents = 'parents';
  static const profile = 'profile';
  static const languages = 'languages';
  static const help = 'help';
  static const english = 'english';
  static const arabic = 'arabic';
  static const updateProfile = 'updateProfile';
  static const add = 'add';
  static const showStudent = 'showStudent';

  static const showParent = 'showParent';
  static const addressStudentOnMap = 'addressStudentOnMap';
  static const searchForStudent = 'searchForStudent';
  static const studentBus = 'studentBus';
  static const address = 'address';
  static const stage = 'stage';
  static const show = 'show';
  static const startTrip = 'startTrip';
  static const notFound = "notFound";
  static const note = "note";

  static const sendNotification = 'sendNotification';
  static const students = 'students';
  static const absence = 'absence';
  static const endTrip = 'endTrip';
  static const studentData = 'studentData';
  static const logout = 'logout';
  static const driver = 'driver';
  static const supervisor = 'supervisor';
  static const goodMorning = 'good_morning';
  static const goodEvening = 'good_evening';
  static const line = 'line';
  static const school = 'school';
  static const busNumber = 'bus_number';
  static const studentsNotFound = 'students_not_found';
  static const parentsNotFound = 'parents_not_found';
  static const bloodType = 'blood_type';
  static const birthDate = 'birth_date';
  static const code = 'code';
  static const secretCode = 'secret_code';
  static const call = 'call';
  static const notification = 'notification';
  static const studentAddress = 'student_address';
  static const waiting = 'waiting';
  static const absentStudents = 'absent_students';
  static const absent = 'absent';
  static const moveToPresent = 'move_to_present';
  static const moveToWaiting = 'move_to_waiting';
  static const moveToBus = 'move_to_bus';
  static const onBus = 'on_bus';
  static const arrivedHome = 'arrived_home';
  static const studentArrivedHome = 'student_arrived_home';
  static const notifications = 'notifications';
  static const sureEndTrip = 'sure_end_trip';
  static const sureStartTrip = 'sure_start_trip';
  static const yes = 'yes';
  static const no = 'no';
  static const deleteAccountTitle = 'deleteAccountTitle';
  static const deleteAccountConfirm = 'deleteAccountConfirm';
  static const deleteAccountNote = 'deleteAccountNote';
  static const deleteAccount = 'deleteAccount';
  static const cancel = 'cancel';
  static const contactUs = 'contact_us';
  static const getInTouch = 'get_in_touch';
  static const wedLoveToHear = 'wed_love_to_hear';
  static const enterYourName = 'enter_your_name';
  static const enterYourEmail = 'enter_your_email';
  static const describeProblem = 'describe_problem';
  static const pleaseEnterName = 'please_enter_name';
  static const pleaseEnterEmail = 'please_enter_email';
  static const pleaseValidEmail = 'please_valid_email';
  static const pleaseDescribeProblem = 'please_describe_problem';
  static const messageTooLong = 'message_too_long';
  static const contactDirectly = 'contact_directly';
  static const copy = 'copy';
  static const emailCopied = 'email_copied';
  static const sending = 'sending';
  static const emailSent = 'email_sent';
  static const failedToSend = 'failed_to_send';
  static const privacyPolicy = 'privacy_policy';
  static const privacyPolicyTitle = 'privacy_policy_title';
  static const privacyPolicyContent = 'privacy_policy_content';
  static const playStore = 'play_store';
  static const rateApp = 'rate_app';
  static const shareApp = 'share_app';
  static const moreApps = 'more_apps';
  static const tripClosedStartTrip = 'trip_closed_start_trip';
  static const classroom = 'classroom';
}
