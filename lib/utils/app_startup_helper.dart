import 'package:flutter/material.dart';

import '../services/app_update_service.dart';
import '../widgets/maintenance_screen.dart';

class AppStartupHelper {
  static final AppUpdateService _appUpdateService = AppUpdateService();

  /// Check app status during startup and handle maintenance mode
  /// This should be called in your main app or splash screen
  static Future<Widget> checkAppStatusAndGetWidget({
    required String appName,
    required AppType appType,
    required Widget normalApp,
    VoidCallback? onRetry,
  }) async {
    try {
      final result = await _appUpdateService.checkCurrentAppStatus(
        appName: appName,
        appType: appType,
      );

      // If API call was successful and app is updating, show maintenance screen
      if (result.isSuccess && result.isUpdating) {
        return MaintenanceScreen(
          message: result.message,
          appName: result.appName,
          onRetry: onRetry,
        );
      }

      // If API call failed or app is not updating, proceed with normal app
      return normalApp;
    } catch (e) {
      debugPrint('Error during app startup check: $e');
      // In case of error, proceed with normal app
      return normalApp;
    }
  }

  /// Check if actions should be blocked (for use in app logic)
  static Future<bool> shouldBlockUserActions({
    required String appName,
    required AppType appType,
  }) async {
    return await _appUpdateService.shouldBlockActions(
      appName: appName,
      appType: appType,
    );
  }

  /// Show maintenance dialog if app is under maintenance
  static Future<void> showMaintenanceDialogIfNeeded({
    required BuildContext context,
    required String appName,
    required AppType appType,
  }) async {
    final shouldShow = await _appUpdateService.shouldShowMaintenanceScreen(
      appName: appName,
      appType: appType,
    );

    if (shouldShow && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('تحت الصيانة'),
          content: const Text(
            'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }
  }
}

/// Wrapper widget that checks app status before showing content
class AppStatusWrapper extends StatefulWidget {
  final String appName;
  final AppType appType;
  final Widget child;

  const AppStatusWrapper({
    Key? key,
    required this.appName,
    required this.appType,
    required this.child,
  }) : super(key: key);

  @override
  State<AppStatusWrapper> createState() => _AppStatusWrapperState();
}

class _AppStatusWrapperState extends State<AppStatusWrapper> {
  final AppUpdateService _appUpdateService = AppUpdateService();
  bool _isLoading = true;
  bool _isUnderMaintenance = false;
  String _maintenanceMessage = '';

  @override
  void initState() {
    super.initState();
    _checkAppStatus();
  }

  Future<void> _checkAppStatus() async {
    try {
      final result = await _appUpdateService.checkCurrentAppStatus(
        appName: widget.appName,
        appType: widget.appType,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
          _isUnderMaintenance = result.isSuccess && result.isUpdating;
          _maintenanceMessage = result.message;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isUnderMaintenance = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_isUnderMaintenance) {
      return MaintenanceScreen(
        message: _maintenanceMessage,
        appName: widget.appName,
        onRetry: _checkAppStatus,
      );
    }

    return widget.child;
  }
}
