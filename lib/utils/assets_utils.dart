String assets(String name) => 'assets/$name';

String assetsImages(String image) => 'assets/images/$image';

String assetsImagesIcons(String icon) => 'assets/images/icons/$icon';

String assetsImagesDrawer(String icon) => 'assets/images/drawer/$icon';

String assetsImagesOnBoarding(String image) =>
    'assets/images/on_boarding/$image';

const String pathAssetsImages = 'assets/images/';

class AppAssets {
  static const String allDriverIcon = '${pathAssetsImages}all_driver_icon.svg';
  static const String supervisorIcon = '${pathAssetsImages}supervisor_icon.svg';
  static const String arrowBack = '${pathAssetsImages}back-arrow.svg';
  static const String forwardArrow = '${pathAssetsImages}forward-arrow.svg';
  static const String busHomeIcon = '${pathAssetsImages}bus_home_icon.svg';
  static const String driverHomeIcon = '${pathAssetsImages}driver_home_icon.svg';
  static const String supervisorHomeIcon = '${pathAssetsImages}supervisor_home_icon.svg';
  static const String locationIcon = '${pathAssetsImages}location_icon.svg';
  static const String parintIcon = '${pathAssetsImages}parint_icon.svg';
  static const String groupIcon = '${pathAssetsImages}groups.svg';
}
