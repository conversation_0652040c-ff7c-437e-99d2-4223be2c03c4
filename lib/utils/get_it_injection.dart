import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'navigation_helper.dart';

final getIt = GetIt.instance;

Future<void> init() async {

  //! ----------- app -----------
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
  // getIt.registerLazySingleton<FirebaseNotificationService>(() => FirebaseNotificationService(),);
  // getIt.registerLazySingleton<FlutterLocalNotificationService>(() => FlutterLocalNotificationService(),);
  getIt.registerSingleton<NavHelper>(NavHelper());
}