import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../config/theme_colors.dart';
import 'custom_text.dart';

class BuildTableRowWidget extends TableRow {
  const BuildTableRowWidget(
      {Key? key,
      this.cell,
      this.header = false,
      this.isIcon = true,
      this.is2Icon = false,
      this.isFirstIcon = false,
      this.isSecondIcon = false,
      this.isTabDown = false,
      this.onTapLastCell,
      this.onTapBeforeLastCell,
      this.onTapFirstCell,
      this.onTapSecondCell,
      this.onTapDown,
      this.color,
      this.isLoading = false,
      this.isFirstButtonLoading = false,
      this.isSecondButtonLoading = false,
      this.isThirdButtonLoading = false});

  final List<dynamic>? cell;
  final bool header;
  final bool isIcon;
  final bool is2Icon;
  final bool isFirstIcon;
  final bool isSecondIcon;
  final bool isTabDown;
  final Color? color;
  final Function()? onTapLastCell;
  final Function()? onTapBeforeLastCell;
  final Function()? onTapFirstCell;
  final Function()? onTapSecondCell;
  final void Function(TapDownDetails)? onTapDown;
  final bool isLoading;
  final bool isFirstButtonLoading;
  final bool isSecondButtonLoading;
  final bool isThirdButtonLoading;

  TableRow build(BuildContext context) {
    return TableRow(
      children: cell!.map(
        (e) {
          int index = cell!.indexOf(e);
          return header == true
              ? Container(
                  height: 59.w,
                  padding: EdgeInsets.symmetric(horizontal: 5.w),
                  decoration: BoxDecoration(
                    color: TColor.mainColor,
                    border: Border.all(color: TColor.mainColor, width: 0.1),
                  ),
                  child: Center(
                    child: FittedBox(
                      child: CustomText(
                        text: e,
                        color: TColor.white,
                        fontSize: 18,
                        fontW: FontWeight.w400,
                      ),
                    ),
                  ),
                )
              : (index == cell!.length - 1 && isIcon == true ||
                      index == cell!.length - 2 && is2Icon == true ||
                      index == 1 && isFirstIcon == true ||
                      index == 2 && isSecondIcon == true)
                  ? SizedBox(
                      height: 59.w,
                      child: AbsorbPointer(
                        absorbing: (index == 1 &&
                                isFirstIcon &&
                                isFirstButtonLoading) ||
                            (index == 2 &&
                                isSecondIcon &&
                                isSecondButtonLoading) ||
                            (index == cell!.length - 2 &&
                                is2Icon &&
                                isThirdButtonLoading),
                        child: InkWell(
                          onTap: () {
                            if (index == cell!.length - 1 &&
                                isIcon == true &&
                                isTabDown == false) {
                              onTapLastCell!();
                            } else if (index == cell!.length - 2 &&
                                is2Icon == true) {
                              onTapBeforeLastCell!();
                            } else if (index == 1 && isFirstIcon == true) {
                              onTapFirstCell!();
                            } else if (index == 2 && isSecondIcon == true) {
                              onTapSecondCell!();
                            }
                          },
                          onTapDown: (index == cell!.length - 1 &&
                                  isIcon == true &&
                                  isTabDown)
                              ? onTapDown
                              : null,
                          child: Center(
                            child: (index == 1 &&
                                        isFirstIcon &&
                                        isFirstButtonLoading) ||
                                    (index == 2 &&
                                        isSecondIcon &&
                                        isSecondButtonLoading) ||
                                    (index == cell!.length - 2 &&
                                        is2Icon &&
                                        isThirdButtonLoading)
                                ? SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: color ?? TColor.tabColors,
                                    ),
                                  )
                                : Icon(
                                    e,
                                    color: color ?? TColor.tabColors,
                                  ),
                          ),
                        ),
                      ),
                    )
                  : Container(
                      height: 59.w,
                      color: TColor.white,
                      child: Center(
                        child: e is Widget
                            ? e
                            : CustomText(
                                text: e.toString(),
                                color: color ?? TColor.tabColors,
                                fontSize: 15,
                                fontW: FontWeight.w400,
                              ),
                      ),
                    );
        },
      ).toList(),
    );
  }
}
