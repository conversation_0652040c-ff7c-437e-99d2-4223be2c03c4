import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../config/theme_colors.dart';
import 'custom_text.dart';

class CustomFormFieldWithBorder extends StatelessWidget {
  String? hintText;
  bool security;
  TextInputType inputType;
  String? validation;
  Function(dynamic)? saved;
  int maxLine;
  Widget? prefix;
  Widget? suffix;
  double? radiusNumber;
  Function(String)? onChanged;
  Function()? onComplete;
  int? requiredNumber;
  bool isTitled;
  double? paddingLeft;
  double? paddingRight;
  double? formFieldWidth;
  double? contentPaddingVertical;
  double? contentPaddingHorizontal;
  Color? fillColor;
  Color? borderColor;
  bool? height;
  double? heightA;
  TextEditingController? controller;

  CustomFormFieldWithBorder({
    this.isTitled = false,
    this.controller,
    this.onComplete,
    super.key,
    this.hintText,
    this.inputType = TextInputType.text,
    this.saved,
    this.validation,
    this.security = false,
    this.maxLine = 1,
    this.prefix,
    this.suffix,
    this.radiusNumber = 5,
    this.onChanged,
    this.fillColor = TColor.fillFormField,
    this.paddingLeft = 20,
    this.paddingRight = 20,
    this.formFieldWidth = 328,
    this.contentPaddingVertical = 0,
    this.contentPaddingHorizontal = 10,
    this.borderColor = TColor.black,
    this.height = false,
    this.heightA = 48,
    this.requiredNumber = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isTitled
            ? Padding(
                padding: context.locale.toString() == "ar"
                    ? EdgeInsets.only(
                        left: paddingLeft!.w, right: paddingRight!.w)
                    : EdgeInsets.only(
                        left: paddingLeft!.w, right: paddingRight!.w),
                child: const CustomText(
                  text: "title",
                  fontSize: 15,
                  textAlign: TextAlign.center,
                  fontW: FontWeight.bold,
                  color: TColor.black,
                ),
              )
            : Container(),
        Padding(
          padding: context.locale.toString() == "ar"
              ? EdgeInsets.only(left: paddingLeft!.w, right: paddingRight!.w)
              : EdgeInsets.only(left: paddingLeft!.w, right: paddingRight!.w),
          child: SizedBox(
            width: formFieldWidth!.w,
            // height: height == true ? 46.21.w : heightA!.w,
            child: TextFormField(
              controller: controller,
              onEditingComplete: onComplete,
              onChanged: onChanged,
              decoration: InputDecoration(
                prefixIcon: prefix,
                suffixIcon: suffix,
                filled: true,
                fillColor: fillColor,
                contentPadding: EdgeInsets.symmetric(
                    vertical: contentPaddingVertical!,
                    horizontal: contentPaddingHorizontal!),
                hintText: hintText,
                hintStyle: const TextStyle(
                  color: TColor.tabColors,
                ),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
                errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(radiusNumber!),
                    borderSide: BorderSide(color: borderColor!, width: 1)),
              ),
              validator: (value) {
                try {
                  if (value!.isEmpty) {
                    print(validation);
                    return validation;
                  } else if (value.length < requiredNumber!) {
                    return "this field should be more than $requiredNumber characters long";
                  }
                  return null;
                } catch (e , stackTrace) {
      print(stackTrace);
                  return "not valid";
                }
              },
              onSaved: saved,
              obscureText: security,
              maxLines: maxLine,
              keyboardType: inputType,
            ),
          ),
        ),
      ],
    );
  }
}
