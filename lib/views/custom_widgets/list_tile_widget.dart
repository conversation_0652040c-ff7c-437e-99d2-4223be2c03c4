import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../config/theme_colors.dart';
import 'custom_text.dart';

class ListTileWidget extends StatelessWidget {
  final void Function() onTap;
  final String title;
  final String? subtitle;
  final String leadingUrl;
  final Color? textButtonColor;
  final bool arrowIcon;
  final Widget? leading;

  const ListTileWidget({
    super.key,
    required this.onTap,
    required this.title,
    required this.leadingUrl,
    this.textButtonColor,
    this.arrowIcon = true,
    this.subtitle,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
        onTap: onTap,
        leading: leading?? Image.asset(
          leadingUrl,
          width: 24.w,
          height: 24.w,
        ),
        minLeadingWidth: 0,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 0,
        ),
        title: CustomText(
          text: title,
          maxLine: 2,
          fontSize: 18,
          textAlign: TextAlign.start,
          fontW: FontWeight.w700,
          color: textButtonColor??TColor.text,
        ),
        subtitle: subtitle == null
            ? null
            : CustomText(
          text: subtitle??'',
          fontSize: 14,
          textAlign: TextAlign.start,
          fontW: FontWeight.w500,
          color: textButtonColor??TColor.textForm,
        ),
        trailing: arrowIcon
            ? Icon(
          Icons.arrow_forward_ios,
          color: TColor.white,
          size: 24.sp,
        )
            : null);
  }
}