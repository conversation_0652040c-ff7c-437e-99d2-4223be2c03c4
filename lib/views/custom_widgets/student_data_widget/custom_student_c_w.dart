import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../utils/sized_box.dart';

class CustomStudentCW extends StatelessWidget {
  final String? name;
  final String? label;
  final bool? isLabel;

  const CustomStudentCW({
    Key? key,
    this.name,
    this.label = '',
    this.isLabel = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isLabel == true
            ? CustomText(
          text: label,
          fontW: FontWeight.w500,
          fontSize: 15,
          color: TColor.namePersonal,
        )
            : const SizedBox(),
        isLabel == true ? const Sbox(h: 3) : const SizedBox(),
        Container(
          width: 428.w,
          height: 43.w,
          decoration: BoxDecoration(
            border: Border.all(color: TColor.dialogName, width: 1.w),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.w),
            child: CustomText(
              text: name ?? "",
              fontSize: 13,
              fontW: FontWeight.w400,
              color: TColor.namePersonal,
            ),
          ),
        ),
      ],
    );
  }
}
