import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/widgets/bus_data_widgets/custom_container_bus_s_w.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../bloc/cubit/my_bus_cubit/my_bus_cubit.dart';
import '../../../bloc/cubit/students_cubit/students_cubit.dart';

class BusDataScreen extends StatelessWidget {
  static const String routeName = PathRouteName.busData;

  const BusDataScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.busData.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: BlocBuilder<MyBusCubit, MyBusState>(
        builder: (context, state) {
          if (state is MyBusLoadingState) {
            return const Center(
              child: CircularProgressIndicator(
                color: TColor.mainColor,
              ),
            );
          } else if (state is MyBusSuccessState) {
            return SingleChildScrollView(
              child: SizedBox(
                width: 1.sw,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Sbox(h: 30),

                    CustomContainerBusSW(
                      name: state.myBusModel?.data?.bus?.name,
                      label: AppStrings.busName.tr(),
                      isLabel: true,
                    ),

                    const Sbox(h: 15),

                    CustomContainerBusSW(
                      name: state.myBusModel?.data?.bus?.carNumber,
                      label: AppStrings.busNumber.tr(),
                      isLabel: true,
                    ),

                    const Sbox(h: 15),

                    CustomContainerBusSW(
                      name: state.myBusModel?.data?.bus?.notes ??
                          AppStrings.notFound.tr(),
                      label: AppStrings.note.tr(),
                      isLabel: true,
                    ),

                    const Sbox(h: 15),

                    CustomContainerBusSW(
                      name: state.myBusModel?.data?.bus?.schools?.name,
                      label: AppStrings.school.tr(),
                      isLabel: true,
                    ),

                    const Sbox(h: 15),

                    // CustomContainerBusSW(
                    //   name: state.myBusModel?.data?.bus?.name,
                    //   label: AppStrings.line.tr(),
                    //   isLabel: true,
                    // ),

                    const Sbox(h: 15),
                    CustomButton(
                      onTap: () {
                        BlocProvider.of<StudentsCubit>(context)
                            .getBusStudents();
                        Navigator.pushNamed(context, PathRouteName.studentBus);
                      },
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      width: 354,
                      height: 43,
                      bgColor: TColor.mainColor,
                      borderColor: TColor.mainColor,
                      text: AppStrings.showStudentBus.tr(),
                    )
                  ],
                ),
              ),
            );
          } else if (state is MyBusErrorState) {
            return const Center(
              child: CustomText(
                text: "تعذر الوصول",
                textAlign: TextAlign.start,
                fontSize: 17,
                fontW: FontWeight.w600,
              ),
            );
          } else {
            return const SizedBox();
          }
        },
      ),
    );
  }
}
