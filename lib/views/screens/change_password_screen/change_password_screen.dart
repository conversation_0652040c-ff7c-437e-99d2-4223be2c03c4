import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/widgets/change_password_widgets/custom_change_password_c_w.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';



class ChangePasswordScreen extends StatefulWidget {
  static const String routeName = PathRouteName.changePassword;
  const ChangePasswordScreen({Key? key}) : super(key: key);

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  bool securityCheckNew = true;
  bool securityCheckConfirm = true;
  bool securityCheckOld = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.changePassword.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          width: 1.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Sbox(h: 120),
              CustomText(
                text: AppStrings.newPassword.tr(),
                color: TColor.dialogName,
                fontSize: 20,
                fontW: FontWeight.w600,
              ),
              const Sbox(h: 20),
              CustomChangePasswordCW(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.newPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheckNew = !securityCheckNew;
                          });
                        },
                        child: securityCheckNew
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                    const Sbox(h: 20),
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.confirmPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheckConfirm = !securityCheckConfirm;
                          });
                        },
                        child: securityCheckConfirm
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                    const Sbox(h: 20),
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.oldPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheckOld = !securityCheckOld;
                          });
                        },
                        child: securityCheckOld
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                  ],
                ),
              ),
             const  Sbox(h: 180),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 37.w),
                child: CustomButton(
                  text: AppStrings.save.tr(),
                  onTap: () {},
                  width: 428,
                  height: 53,
                  radius: 15,
                  borderColor: TColor.mainColor,
                  bgColor: TColor.mainColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
