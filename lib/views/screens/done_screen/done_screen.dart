import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';



class DoneScreen extends StatelessWidget {
  static const String routeName = PathRouteName.done;
  const DoneScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const Sbox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const Sbox(h: 100),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                height: 310.w,
                child: Column(
                  children: [
                    const Sbox(h: 40),
                    Image.asset(
                      assetsImages("done.png"),
                      width: 80.w,
                      height: 80.w,
                    ),
                    const Sbox(h: 40),
                    CustomText(
                      text: AppStrings.passwordChangeSuccess.tr(),
                      fontSize: 16,
                      fontW: FontWeight.w400,
                    ),
                    const Sbox(h: 50),
                    CustomButton(
                      text: AppStrings.goHome.tr(),
                      onTap: () {
                        print("login");
                      },
                      width: 307,
                      height: 48,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                    const Sbox(h: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
