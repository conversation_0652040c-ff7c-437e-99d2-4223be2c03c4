import 'dart:io';

import 'package:bus_driver/bloc/cubit/ads_cubit/ads_cubit.dart';
import 'package:bus_driver/bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
import 'package:bus_driver/bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
import 'package:bus_driver/bloc/cubit/trip_evening_cubit/trip_evening_cubit.dart';
import 'package:bus_driver/bloc/cubit/trip_evening_cubit/trip_evening_states.dart';
import 'package:bus_driver/bloc/cubit/trip_morning_cubit/trip_morning_cubit.dart';
import 'package:bus_driver/bloc/cubit/trip_morning_cubit/trip_morning_states.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/helper/cache_helper.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/screens/home_screen/socket_service.dart';
import 'package:bus_driver/views/screens/home_screen/user_data_service.dart';
import 'package:bus_driver/widgets/carousel_widget/carousel_widget.dart';
import 'package:bus_driver/widgets/home_widgets/custom_name_w.dart';
import 'package:bus_driver/widgets/home_widgets/custom_wrap_h_w.dart';
import 'package:bus_driver/widgets/loading_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import 'location_service.dart';

class HomeScreen extends StatefulWidget {
  static const String routeName = PathRouteName.home;
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final UserDataService _userDataService = UserDataService();
  final NewLocationService _locationService = NewLocationService();
  final SocketService _socketService = SocketService();
  final AdsCubit _adsCubit = AdsCubit();

  bool isUserDataLoaded = false;
  bool hasError = false;
  Position? position;
  bool isChecked = false;

  @override
  void initState() {
    super.initState();
    _initialize();
    _locationService.locationStream.listen((GeoPoint location) {
      setState(() {});
    });
    _userDataService.userDataStream.listen((userData) {
      setState(() {
        username = userData["username"];
        name = userData["name"];
        type = userData["type"];
        busId = userData["bus_id"];
        id = userData["id"];
        userImageUrl = userData["logo_path"];
        userPhone = userData["phone"];
        userAddress = userData["address"];
        isUserDataLoaded = true;
        if (CacheHelper.getBool('isFirst') != true) {
          _userDataService.showCustomDialog(context);
        }
        batteryOptimizationDialog(context);
      });
    }, onError: (error) {
      setState(() {
        isUserDataLoaded = true;
        hasError = true;
      });
    });
  }

  Future<void> _initialize() async {
    context.read<TripMorningCubit>().getCurrentMorning();
    context.read<TripEveningCubit>().getCurrentEvening();
    await _userDataService.getUserData();
    _socketService.connect();
    _adsCubit.getAds();
  }

  Future<void> batteryOptimizationDialog(BuildContext context) async {
    if (Platform.isAndroid) {
      final status = await Permission.ignoreBatteryOptimizations.status;
      if (!status.isGranted) {
        _userDataService.showBatteryOptimizationDialog(context);
      }
    }
  }

  @override
  void dispose() {
    _locationService.dispose();
    _userDataService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return name == null
        ? const LoadingScreen()
        : Scaffold(
            body: RefreshIndicator(
              onRefresh: () async {
                await batteryOptimizationDialog(context);
                _adsCubit.getAds();
                context.read<TripMorningCubit>().getCurrentMorning();
                context.read<TripEveningCubit>().getCurrentEvening();
              },
              child: SingleChildScrollView(
                child: SafeArea(
                  child: Stack(
                    children: [
                      Container(
                        width: 1.sw,
                        height: 1.sh,
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                              image: AssetImage("assets/images/bg.png"),
                              fit: BoxFit.cover),
                        ),
                        child: CustomNameW(name1: name!),
                      ),
                      Positioned(
                        top: 65.w,
                        child: Container(
                          width: 1.sw,
                          height: 1.sh,
                          decoration: BoxDecoration(
                            color: TColor.white,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(30.r),
                              topLeft: Radius.circular(30.r),
                            ),
                          ),
                          child: Column(
                            children: [
                              20.verticalSpace,
                              BlocBuilder<AdsCubit, AdsState>(
                                builder: (context, state) {
                                  final ads = _adsCubit.ads?.data;
                                  return CarouselWidget(
                                      items: List<Widget>.generate(
                                    ads?.length ?? 0,
                                    (index) => InkWell(
                                      onTap: () async {
                                        final Uri url = Uri.parse(
                                            ads?[index].ads?.link ?? '');
                                        if (!await launchUrl(url)) {
                                          throw Exception(
                                              'Could not launch $url');
                                        }
                                      },
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            width: double.infinity,
                                            height: 170.h,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  const BorderRadius.only(
                                                      topLeft:
                                                          Radius.circular(10),
                                                      topRight:
                                                          Radius.circular(10)),
                                              image: DecorationImage(
                                                image: NetworkImage(ads?[index]
                                                        .ads
                                                        ?.imagePath ??
                                                    ''),
                                                fit: BoxFit.fill,
                                              ),
                                            ),
                                          ),
                                          Container(
                                            width: double.infinity,
                                            padding: EdgeInsets.only(
                                                bottom: 6.h, top: 6.h),
                                            decoration: const ShapeDecoration(
                                                gradient: LinearGradient(
                                                    begin:
                                                        Alignment(0.00, -1.00),
                                                    end: Alignment(0, 1),
                                                    colors: [
                                                      TColor.mainColor,
                                                      TColor.borderAvatar
                                                    ]),
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.only(
                                                            bottomLeft: Radius
                                                                .circular(10),
                                                            bottomRight:
                                                                Radius.circular(
                                                                    10)))),
                                            child: CustomText(
                                              text: ads?[index].ads?.title,
                                              textAlign: TextAlign.center,
                                              fontSize: 18.sp,
                                              fontW: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ));
                                },
                              ),
                              30.verticalSpace,
                              const CustomWrapHW(),
                              BlocBuilder<TripMorningCubit, TripMorningStates>(
                                  builder: (context, states) {
                                if (states is TripMorningLoadingStates) {
                                  return const SizedBox();
                                } else if (states is TripMorningSuccessStates) {
                                  if (states.tripOpenModels!.status == true) {
                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 20.w),
                                      child: Column(
                                        children: [
                                          40.verticalSpace,
                                          ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(13.w),
                                            child: InkWell(
                                              onTap: () {
                                                MorningTripCubit.get()
                                                    .groupOrSingleMessage(
                                                        type_day: 'start_day');

                                                MorningTripCubit.get()
                                                    .getMorningTripStatus();
                                                Navigator.pushNamed(
                                                    context,
                                                    PathRouteName
                                                        .morningTripScreen);
                                              },
                                              child: Container(
                                                width: 373.w,
                                                height: 115.w,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          12.r),
                                                  image: const DecorationImage(
                                                    image: AssetImage(
                                                        "assets/images/trips.png"),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Image.asset(
                                                      assetsImages(
                                                          "group1.png"),
                                                      width: 56.w,
                                                      height: 56.w,
                                                    ),
                                                    const Sbox(h: 10),
                                                    CustomText(
                                                      text: AppStrings
                                                          .morningTrips
                                                          .tr(),
                                                      fontW: FontWeight.w400,
                                                      fontSize: 15,
                                                      color: TColor.white,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return const SizedBox();
                                  }
                                } else if (states is TripMorningErrorStates) {
                                  return const SizedBox();
                                } else {
                                  return const SizedBox();
                                }
                              }),
                              BlocBuilder<TripEveningCubit, TripEveningStates>(
                                  builder: (context, states) {
                                if (states is TripEveningLoadingStates) {
                                  return const SizedBox();
                                } else if (states is TripEveningSuccessStates) {
                                  if (states.tripOpenModels!.status == true) {
                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 20.w),
                                      child: Column(
                                        children: [
                                          40.verticalSpace,
                                          ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(13.w),
                                            child: InkWell(
                                              onTap: () {
                                                MorningTripCubit.get()
                                                    .groupOrSingleMessage(
                                                        type_day: 'end_day');
                                                EveningTripCubit.get(context)
                                                    .getEveningTripStatus();
                                                Navigator.pushNamed(
                                                    context,
                                                    PathRouteName
                                                        .eveningTripScreen);
                                              },
                                              child: Container(
                                                width: 373.w,
                                                height: 115.w,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          12.r),
                                                  image: const DecorationImage(
                                                    image: AssetImage(
                                                        "assets/images/trips.png"),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Image.asset(
                                                      assetsImages(
                                                          "group1.png"),
                                                      width: 56.w,
                                                      height: 56.w,
                                                    ),
                                                    const Sbox(h: 10),
                                                    CustomText(
                                                      text: AppStrings
                                                          .eveningTrips
                                                          .tr(),
                                                      fontW: FontWeight.w400,
                                                      fontSize: 15,
                                                      color: TColor.white,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return const SizedBox();
                                  }
                                } else if (states is TripEveningErrorStates) {
                                  return const SizedBox();
                                } else {
                                  return const SizedBox();
                                }
                              }),

                              // ElevatedButton(
                              //   onPressed:(){ },
                              //   child: Text("Request Ignore Battery Optimization"),
                              // ),
                              30.verticalSpace,
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
  }
}
