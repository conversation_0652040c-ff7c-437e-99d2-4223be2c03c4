// import 'dart:async';
// import 'dart:convert';
//
// import 'package:bus_driver/bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
// import 'package:bus_driver/bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
// import 'package:bus_driver/bloc/cubit/trip_evening_cubit/trip_evening_cubit.dart';
// import 'package:bus_driver/bloc/cubit/trip_evening_cubit/trip_evening_states.dart';
// import 'package:bus_driver/bloc/cubit/trip_morning_cubit/trip_morning_cubit.dart';
// import 'package:bus_driver/bloc/cubit/trip_morning_cubit/trip_morning_states.dart';
// import 'package:bus_driver/config/config_base.dart';
// import 'package:bus_driver/config/global_variable.dart';
// import 'package:bus_driver/config/theme_colors.dart';
// import 'package:bus_driver/constant/path_route_name.dart';
// import 'package:bus_driver/helper/cache_helper.dart';
// import 'package:bus_driver/helper/network_serviecs.dart';
// import 'package:bus_driver/services/backgroung_test.dart';
// import 'package:bus_driver/services/location_service/location_service.dart';
// import 'package:bus_driver/translations/local_keys.g.dart';
// import 'package:bus_driver/utils/assets_utils.dart';
// import 'package:bus_driver/utils/sized_box.dart';
// import 'package:bus_driver/widgets/home_widgets/custom_wrap_h_w.dart';
// import 'package:bus_driver/widgets/home_widgets/custom_name_w.dart';
// import 'package:bus_driver/widgets/open_street_map_widget.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:logger/logger.dart';
// import 'package:socket_io_client/socket_io_client.dart';
// import 'package:url_launcher/url_launcher.dart';
// import '../../../bloc/cubit/ads_cubit/ads_cubit.dart';
// import '../../../widgets/carousel_widget/carousel_widget.dart';
// import '../../../widgets/loading_screen.dart';
// import '../../custom_widgets/custom_text.dart';
// import 'package:socket_io_client/socket_io_client.dart' as io;
//
// class HomeScreen extends StatefulWidget {
//   static const String routeName = PathRouteName.home;
//   const HomeScreen({Key? key}) : super(key: key);
//
//   @override
//   State<HomeScreen> createState() => _HomeScreenState();
// }
//
// class _HomeScreenState extends State<HomeScreen> {
//   bool isUserDataLoaded = false;
//   bool hasError = false;
//   Position? position;
//
//
//   bool isChecked = false;
//
// // bool isFirst=  false;
//
//   void _showCustomDialog(context) {
//
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return
//
//
//          AlertDialog(
//           title: const Text("Busaty - Bus: Real-Time Guardian Location Monitoring",
//           textAlign: TextAlign.center,
//
//           ),
//           content: const Text(
//             "Busaty - Bus collects background location data to allow the guardian to monitor their child's trip in real-time, even when the app is in the background or closed.",
//               textAlign: TextAlign.center,
//               style: TextStyle(
//                 height: 1.6
//               ),
//               ),
//           actions: <Widget>[
//            TextButton(
//               onPressed: () {
//                 CacheHelper.putBool('isFirst', true);
//                 Navigator.of(context).pop();
//                 print('---------------------------------${CacheHelper.getBool('isFirst')}');
//               },
//               child: Text('OK',
//
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 fontSize: 15
//               ),
//
//               ),
//             ),
//           ],
//         );
//
//
//       },
//     );
//
//   }
//
//
//
//
//   Future<void> _determinePosition() async {
//     bool serviceEnabled;
//     LocationPermission permission;
//
//     // Test if location services are enabled.
//
//     serviceEnabled = await Geolocator.isLocationServiceEnabled();
//     if (!serviceEnabled) {
//
//
//       return Future.error('Location services are disabled.');
//
//     }
//
//     permission = await Geolocator.checkPermission();
//     if (permission == LocationPermission.denied) {
//       permission = await Geolocator.requestPermission();
//       if (permission == LocationPermission.denied) {
//
//         return Future.error('Location permissions are denied');
//       }
//     }
//
//     if (permission == LocationPermission.deniedForever) {
//       // Permissions are denied forever, handle appropriately.
//       return Future.error(
//           'Location permissions are permanently denied, we cannot request permissions.');
//     }
//
//     // When we reach here, permissions are granted and we can
//     // continue accessing the position of the device.
//     position = await Geolocator.getCurrentPosition();
//     setState(() {});
//     // getAddress(LatLng(position!.latitude, position!.longitude));
//   }
//
//   Position? currentPosition;
//
// final io.Socket socket = io.io(ConfigBase.socketUrl, <String, dynamic>{
//     'query': {
//       'token': socketToken,
//     },
//
//     'transports': ['websocket'],
//
//   });
//
//   GeoPoint? currentLocation;
//   GeoPoint? previousLocation;
//   StreamSubscription<Position>? _positionStreamSubscription;
//   void sendLocationToSocket() {
//     print("sendLocationToSocket method starts");
//     if (currentLocation != null) {
//       print("currentLocation: $currentLocation");
//
//       final locationJson = jsonEncode({
//         'latitude': currentLocation!.latitude.toString(),
//         'longitude': currentLocation!.longitude.toString(),
//         'type': type,
//         'bus_id': busId,
//       });
//     
//       final String? sendEvent = type;
//
//       if (sendEvent == null) {
//         throw ArgumentError("sendEvent (type) is null");
//       }
//
//       try {
//         if (!socket.connected) {
//           socket.onConnect((_) {
//             print('sendLocationToSocket: Connected to WebSocket');
//             socket.emit(sendEvent!, [busId, locationJson]);
//           });
//         } else {
//           socket.emit(sendEvent!, [busId, locationJson]);
//         }
//
//         socket.on('connecting', (_) {
//           print('sendLocationToSocket: On Connecting...');
//         });
//
//         socket.onConnectError((error) {
//           print('sendLocationToSocket: On Connect Error - $error');
//           socket.connect();
//           SnackBar snackBar = SnackBar(
//             backgroundColor: TColor.redAccent,
//             content: CustomText(
//               text: "On Connect Error - $error",
//               color: TColor.white,
//               maxLine: 3,
//             ),
//           );
//           snackBarKey.currentState?.showSnackBar(snackBar);
//         });
//
//         socket.onReconnect((data) {
//           print('sendLocationToSocket: On Reconnect..... data: $data');
//         });
//
//         socket.onDisconnect((reason) => print('sendLocationToSocket: Disconnected from WebSocket - $reason'));
//       } catch (e , stackTrace) {
//       print(stackTrace);
//         print('sendLocationToSocket: Error - $e');
//       }
//     }
//     print("sendLocationToSocket method ends");
//   }
//
//
//   @override
//   void initState() {
//     super.initState();
//
//     debugPrint('token: $token');
//     getUserData();
//     AdsCubit.get(context).getAds();
//     _determinePosition();
//   startLocationTracking();
//     socket.connect();
//     // BackgroundService();
//
//   }
//
// void startLocationTracking() {
//     print("startLocationTracking method starts");
//     _positionStreamSubscription = Geolocator.getPositionStream().listen(
//       (Position position) async {
//         setState(() {
//           // Save the current location data
//           currentLocation = GeoPoint(
//               latitude: position.latitude, longitude: position.longitude);
//           // print("currentLocation in startLocationTracking: $currentLocation");
//         });
//         print("Location Updated");
//
//         sendLocationToSocket();
//
//         // Update map controller with new current position
//         // await mapController.goToLocation(currentLocation!);
//       },
//     );
//     print("startLocationTracking method ends");
//   }
//
//
//   getUserData() async {
//     debugPrint('getUserData---------');
//     try {
//       var respo =
//           await NetworkService().get(url: ConfigBase.profile, isAuth: true);
//       // Logger().w(respo.data);
//       username = respo.data["username"];
//       name = respo.data["name"];
//       type = respo.data["type"];
//       busId = respo.data["bus_id"];
//       id = respo.data["id"];
//       userImageUrl = respo.data["logo_path"];
//       userPhone = respo.data["phone"];
//       userAddress = respo.data["address"];
//       isUserDataLoaded = true;
//       if(CacheHelper.getBool('isFirst') != true){
//       _showCustomDialog(context);
//       }
//       setState(() {});
//       debugPrint(respo.data.toString());
//     } catch (e , stackTrace) {
//       print(stackTrace);
//       isUserDataLoaded = true;
//       hasError = true;
//       setState(() {});
//     }
//     setState(() {});
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     debugPrint(token);
//     Logger().w('--------------------------------------------$name');
//     return name == null
//         ? const LoadingScreen()
//         : Scaffold(
//             body: RefreshIndicator(
//               onRefresh: () async {
//                 AdsCubit.get(context).getAds();
//                 context.read<TripMorningCubit>().getCurrentMorning();
//                 context.read<TripEveningCubit>().getCurrentEvening();
//               },
//               child: SingleChildScrollView(
//                 child: SafeArea(
//                   child: Stack(
//                     children: [
//                       Container(
//                         width: 1.sw,
//                         height: 1.sh,
//                         decoration: const BoxDecoration(
//                           image: DecorationImage(
//                               image: AssetImage("assets/images/bg.png"),
//                               fit: BoxFit.cover),
//                         ),
//                         child:  CustomNameW(name1: name!),
//                       ),
//                       Positioned(
//                         top: 65.w,
//                         child: Container(
//                           width: 1.sw,
//                           height: 1.sh,
//                           decoration: BoxDecoration(
//                             color: TColor.white,
//                             borderRadius: BorderRadius.only(
//                               topRight: Radius.circular(30.r),
//                               topLeft: Radius.circular(30.r),
//                             ),
//                           ),
//                           child: Column(
//                             children: [
//                               20.verticalSpace,
//                               BlocBuilder<AdsCubit, AdsState>(
//                                 builder: (context, state) {
//                                   final ads = AdsCubit.get(context).ads?.data;
//                                   return CarouselWidget(
//                                       items: List<Widget>.generate(
//                                     ads?.length ?? 0,
//                                     (index) => InkWell(
//                                       onTap: () async {
//                                         final Uri url = Uri.parse(
//                                             ads?[index].ads?.link ?? '');
//                                         if (!await launchUrl(url)) {
//                                           throw Exception(
//                                               'Could not launch $url');
//                                         }
//                                       },
//                                       child: Column(
//                                         crossAxisAlignment:
//                                             CrossAxisAlignment.start,
//                                         children: [
//                                           Container(
//                                             width: double.infinity,
//                                             height: 170.h,
//                                             decoration: BoxDecoration(
//                                               borderRadius:
//                                                   const BorderRadius.only(
//                                                       topLeft:
//                                                           Radius.circular(10),
//                                                       topRight:
//                                                           Radius.circular(10)),
//                                               image: DecorationImage(
//                                                 image: NetworkImage(
//                                                     ads?[index].ads?.imagePath ??
//                                                         ''),
//                                                 fit: BoxFit.fill,
//                                               ),
//                                             ),
//                                           ),
//                                           Container(
//                                             width: double.infinity,
//                                             padding: EdgeInsets.only(
//                                                 bottom: 6.h, top: 6.h),
//                                             decoration: const ShapeDecoration(
//                                                 gradient: LinearGradient(
//                                                     begin: Alignment(0.00, -1.00),
//                                                     end: Alignment(0, 1),
//                                                     colors: [
//                                                       TColor.mainColor,
//                                                       TColor.borderAvatar
//                                                     ]),
//                                                 shape: RoundedRectangleBorder(
//                                                     borderRadius:
//                                                         BorderRadius.only(
//                                                             bottomLeft:
//                                                                 Radius.circular(
//                                                                     10),
//                                                             bottomRight:
//                                                                 Radius.circular(
//                                                                     10)))),
//                                             child: CustomText(
//                                               text: ads?[index].ads?.title,
//                                               textAlign: TextAlign.center,
//                                               fontSize: 18.sp,
//                                               fontW: FontWeight.w500,
//                                               color: Colors.white,
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                     ),
//                                   ));
//                                 },
//                               ),
//                               30.verticalSpace,
//                               const CustomWrapHW(),
//                               BlocBuilder<TripMorningCubit, TripMorningStates>(
//                                   builder: (context, states) {
//                                 if (states is TripMorningLoadingStates) {
//                                   return const SizedBox();
//                                 } else if (states is TripMorningSuccessStates) {
//                                   if (states.tripOpenModels!.status == true) {
//     //
//                                     print('1111111111111111111111111111111111111');
//                                     return Padding(
//                                       padding:
//                                           EdgeInsets.symmetric(horizontal: 20.w),
//                                       child: Column(
//                                         children: [
//                                           40.verticalSpace,
//                                           ClipRRect(
//                                             borderRadius:
//                                                 BorderRadius.circular(13.w),
//                                             child: InkWell(
//                                               onTap: () {
//                                                   MorningTripCubit.get().groupOrSingleMessage(type_day: 'start_day');
//
//                                                 MorningTripCubit.get().getMorningTripStatus();
//                                                 Navigator.pushNamed(context, PathRouteName.morningTripScreen);
//                                               },
//                                               child: Container(
//                                                 width: 373.w,
//                                                 height: 115.w,
//                                                 decoration: BoxDecoration(
//                                                   borderRadius:
//                                                       BorderRadius.circular(12.r),
//                                                   image: const DecorationImage(
//                                                     image: AssetImage(
//                                                         "assets/images/trips.png"),
//                                                     fit: BoxFit.cover,
//                                                   ),
//                                                 ),
//                                                 child: Column(
//                                                   mainAxisAlignment:
//                                                       MainAxisAlignment.center,
//                                                   children: [
//                                                     Image.asset(
//                                                       assetsImages("group1.png"),
//                                                       width: 56.w,
//                                                       height: 56.w,
//                                                     ),
//                                                     const Sbox(h: 10),
//                                                     CustomText(
//                                                       text: AppStrings
//                                                           .morningTrips
//                                                           .tr(),
//                                                       fontW: FontWeight.w400,
//                                                       fontSize: 15,
//                                                       color: TColor.white,
//                                                     ),
//                                                   ],
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                     );
//                                   } else {
//                                     print("************************************* false");
//                                     return const SizedBox();
//                                   }
//                                 } else if (states is TripMorningErrorStates) {
//                                   return const SizedBox();
//                                 } else {
//                                   return const SizedBox();
//                                 }
//                               }),
//                               BlocBuilder<TripEveningCubit, TripEveningStates>(
//                                   builder: (context, states) {
//                                     if (states is TripEveningLoadingStates) {
//                                       return const SizedBox();
//                                     } else if (states is TripEveningSuccessStates) {
//                                       if (states.tripOpenModels!.status == true) {
//                                         return Padding(
//                                           padding:
//                                           EdgeInsets.symmetric(horizontal: 20.w),
//                                           child: Column(
//                                             children: [
//                                               40.verticalSpace,
//                                               ClipRRect(
//                                                 borderRadius:
//                                                 BorderRadius.circular(13.w),
//                                                 child: InkWell(
//                                                   onTap: () {
//                                                   MorningTripCubit.get().groupOrSingleMessage(type_day:'end_day' );
//                                                     EveningTripCubit.get(context).getEveningTripStatus();
//                                                     Navigator.pushNamed(
//                                                         context, PathRouteName.eveningTripScreen);
//                                                   },
//                                                   child: Container(
//                                                     width: 373.w,
//                                                     height: 115.w,
//                                                     decoration: BoxDecoration(
//                                                       borderRadius:
//                                                       BorderRadius.circular(12.r),
//                                                       image: const DecorationImage(
//                                                         image: AssetImage(
//                                                             "assets/images/trips.png"),
//                                                         fit: BoxFit.cover,
//                                                       ),
//                                                     ),
//                                                     child: Column(
//                                                       mainAxisAlignment:
//                                                       MainAxisAlignment.center,
//                                                       children: [
//                                                         Image.asset(
//                                                           assetsImages("group1.png"),
//                                                           width: 56.w,
//                                                           height: 56.w,
//                                                         ),
//                                                         const Sbox(h: 10),
//                                                         CustomText(
//                                                           text: AppStrings
//                                                               .eveningTrips
//                                                               .tr(),
//                                                           fontW: FontWeight.w400,
//                                                           fontSize: 15,
//                                                           color: TColor.white,
//                                                         ),
//                                                       ],
//                                                     ),
//                                                   ),
//                                                 ),
//                                               ),
//                                             ],
//                                           ),
//                                         );
//                                       } else {
//                                         return const SizedBox();
//                                       }
//                                     } else if (states is TripEveningErrorStates) {
//                                       return const SizedBox();
//                                     } else {
//                                       return const SizedBox();
//                                     }
//                                   }),
//                               30.verticalSpace,
//                             ],
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           );
//   }
// }