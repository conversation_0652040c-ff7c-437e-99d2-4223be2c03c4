// location_service.dart
import 'dart:async';
import 'dart:convert';

import 'package:bus_driver/config/global_variable.dart';
import 'package:bus_driver/views/screens/home_screen/socket_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:geolocator/geolocator.dart';

class NewLocationService {
  Position? currentPosition;
  GeoPoint? currentLocation;
  GeoPoint? previousLocation;
  StreamSubscription<Position>? _positionStreamSubscription;

  final StreamController<GeoPoint> _locationStreamController =
      StreamController<GeoPoint>.broadcast();
  Stream<GeoPoint> get locationStream => _locationStreamController.stream;

  // Singleton pattern for easier access
  static final NewLocationService _instance = NewLocationService._internal();
  factory NewLocationService() => _instance;
  NewLocationService._internal();

  Future<bool> checkAndRequestLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      debugPrint("Location services are disabled.");
      return false;
    }

    // Check location permissions
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        debugPrint("Location permissions are denied.");
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      debugPrint("Location permissions are permanently denied.");
      return false;
    }

    return true;
  }

  Future<void> determinePosition() async {
    try {
      bool permissionGranted = await checkAndRequestLocationPermission();
      if (!permissionGranted) {
        throw Exception('Location permissions not granted');
      }

      currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
      );

      currentLocation = GeoPoint(
        latitude: currentPosition!.latitude,
        longitude: currentPosition!.longitude,
      );

      _locationStreamController.add(currentLocation!);
      debugPrint(
          "Current Position: ${currentLocation!.latitude}, ${currentLocation!.longitude}");
    } catch (e) {
      debugPrint("Error determining position: $e");
      rethrow;
    }
  }

  void startLocationTracking() {
    try {
      // Cancel any existing subscription
      _positionStreamSubscription?.cancel();

      // Start a new location stream
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: LocationSettings(
          accuracy: LocationAccuracy.best,
          distanceFilter: 10, // Update every 10 meters
        ),
      ).listen(
        (Position position) {
          currentLocation = GeoPoint(
            latitude: position.latitude,
            longitude: position.longitude,
          );

          _locationStreamController.add(currentLocation!);
          sendLocationToSocket();
        },
        onError: (error) {
          debugPrint("Location tracking error: $error");
        },
      );

      debugPrint("Location tracking started");
    } catch (e) {
      debugPrint("Error starting location tracking: $e");
    }
  }

  void sendLocationToSocket() {
    try {
      if (currentLocation == null) return;

      final locationJson = jsonEncode({
        'latitude': currentLocation!.latitude.toString(),
        'longitude': currentLocation!.longitude.toString(),
        'type': type.toString(),
        'bus_id': busId,
      });

      final String sendEvent = type.toString();

      if (sendEvent.isEmpty) {
        throw ArgumentError("Send event (type) is empty");
      }

      // Ensure socket connection and emit location
      if (!SocketService.socket.connected) {
        SocketService.socket.connect();
      }

      SocketService.socket.emit(sendEvent, {
        'busId': busId,
        'location': locationJson,
      });

      debugPrint("Location sent to socket: $locationJson");
    } catch (e) {
      debugPrint("Error sending location to socket: $e");
    }
  }

  void dispose() {
    try {
      _positionStreamSubscription?.cancel();
      _locationStreamController.close();
      debugPrint("Location service disposed");
    } catch (e) {
      debugPrint("Error disposing location service: $e");
    }
  }
}
