import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:socket_io_client/socket_io_client.dart';
import 'dart:async';

class SocketService {
  static final Socket socket = io(ConfigBase.socketUrl, <String, dynamic>{
    'query': {
      'token': socketToken,
    },
    'transports': ['websocket'],
    'autoConnect': true,
    'reconnection': true,
    'reconnectionAttempts': 10,
    'reconnectionDelay': 1000,
  });

  static bool _isInitialized = false;

  static void initialize() {
    if (!_isInitialized) {
      socket.onConnect((_) {
        print('Socket connected');
      });

      socket.onDisconnect((_) {
        print('Socket disconnected');
        ensureConnection();
      });

      socket.onError((error) {
        print('Socket error: $error');
        ensureConnection();
      });

      socket.onConnectError((error) {
        print('Socket connection error: $error');
        ensureConnection();
      });

      _isInitialized = true;
    }
  }

  void connect() {
    initialize();
    socket.connect();
  }

  // Send location updates through socket with retry mechanism
  static void sendLocationUpdate(Map<String, dynamic> locationData) {
    try {
      if (!socket.connected) {
        socket.connect();
        // Wait for connection before sending
        socket.onConnect((_) {
          socket.emit('location_update', locationData);
        });
      } else {
        socket.emit('location_update', locationData);
      }
    } catch (e) {
      print('Error sending location update: $e');
      // Retry connection after error
      Future.delayed(Duration(seconds: 5), () {
        ensureConnection();
      });
    }
  }

  // Ensure socket connection with retry mechanism
  static void ensureConnection() {
    if (!_isInitialized) {
      initialize();
    }
    if (!socket.connected) {
      socket.connect();
    }
  }

  // Properly disconnect socket
  static void disconnect() {
    socket.disconnect();
    _isInitialized = false;
  }
}