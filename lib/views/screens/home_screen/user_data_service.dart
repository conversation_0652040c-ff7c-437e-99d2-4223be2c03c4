import 'dart:async';

import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/helper/cache_helper.dart';
import 'package:bus_driver/helper/network_serviecs.dart';
import 'package:bus_driver/services/background_services/background_services.dart';
import 'package:bus_driver/views/screens/home_screen/location_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class UserDataService {
  final NewLocationService _locationService = NewLocationService();
  final StreamController<Map<String, dynamic>> _userDataStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get userDataStream =>
      _userDataStreamController.stream;

  Future<void> getUserData() async {
    try {
      var respo =
          await NetworkService().get(url: ConfigBase.profile, isAuth: true);
      Map<String, dynamic> userData = {
        "username": respo.data["username"],
        "name": respo.data["name"],
        "type": respo.data["type"],
        "bus_id": respo.data["bus_id"],
        "id": respo.data["id"],
        "logo_path": respo.data["logo_path"],
        "phone": respo.data["phone"],
        "address": respo.data["address"],
      };
      _userDataStreamController.add(userData);
      debugPrint(respo.data.toString());
    } catch (e, stackTrace) {
      print(stackTrace);
      _userDataStreamController.addError(e);
    }
  }

  void showCustomDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevents dismissal by tapping outside
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            "Enable Background Location",
            textAlign: TextAlign.center,
          ),
          content: const Text(
            "Busaty - Bus collects background location data to allow guardians to monitor their child's trip in real-time, even when the app is in the background or closed. This ensures accurate tracking and timely updates.",
            textAlign: TextAlign.left,
            style: TextStyle(height: 1.5),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Optionally, inform the user about limited functionality without permission
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Background location access is necessary for real-time tracking.',
                    ),
                    duration: Duration(seconds: 3),
                  ),
                );
              },
              child: const Text(
                'Cancel',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                CacheHelper.putBool('isFirst', true);
                Navigator.of(context).pop();
                _locationService.determinePosition();
                _locationService.startLocationTracking();
              },
              child: const Text(
                'OK',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue, // Customize button color
              ),
            ),
          ],
        );
      },
    );
  }

  static const platform = MethodChannel('battery_optimization');

  Future<void> showBatteryOptimizationDialog(BuildContext context) async {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevents dismissal by tapping outside
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            "Disable Battery Optimization",
            textAlign: TextAlign.center,
          ),
          content: const Text(
            "Busaty - Bus requires you to disable battery optimization to ensure uninterrupted location tracking and real-time updates. This allows the app to function optimally in the background.",
            textAlign: TextAlign.left,
            style: TextStyle(height: 1.5),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Optionally inform the user about limited functionality with battery optimization enabled
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Battery optimization must be disabled for accurate real-time tracking.',
                    ),
                    duration: Duration(seconds: 3),
                  ),
                );
              },
              child: const Text(
                'Cancel',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                try {
                  // Check if battery optimization is already disabled
                  final bool isDisabled = await platform.invokeMethod('isIgnoringBatteryOptimizations');
                  if (isDisabled) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Battery optimization is already disabled.',
                        ),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  } else {
                    // Request user to disable battery optimization
                    await platform.invokeMethod('requestIgnoreBatteryOptimization');
                  }
                } on PlatformException catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Failed to request battery optimization: ${e.message}',
                      ),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
              child: const Text(
                'OK',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue, // Customize button color
              ),
            ),
          ],
        );
      },
    );
  }





  Future<void> requestBackgroundLocationPermission(BuildContext context) async {
    // Check if the permission is already granted
    if (await Permission.locationAlways.isGranted) {
      // Permission already granted, proceed with location services
      _locationService.startLocationTracking();
    } else {
      // Request the background location permission
      PermissionStatus status = await Permission.locationAlways.request();

      if (status.isGranted) {
        // Permission granted, proceed with location services
        _locationService.startLocationTracking();
      } else if (status.isDenied) {
        // Permission denied, show a message or prompt again if necessary
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Background location permission denied. Real-time tracking will be limited.',
            ),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (status.isPermanentlyDenied) {
        // Permission permanently denied, direct user to app settings
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Background location permission permanently denied. Please enable it in the app settings.',
            ),
            duration: Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Settings',
              onPressed: () {
                openAppSettings();
              },
            ),
          ),
        );
      }
    }
  }

  void dispose() {
    _userDataStreamController.close();
  }
}
