import 'package:bus_driver/helper/cache_helper.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../utils/sized_box.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/language_widgets/custom_container_l_w.dart';
import '../../custom_widgets/custom_text.dart';

class LanguagesScreen extends StatelessWidget {
  static const String routeName = PathRouteName.languageScreen;

  const LanguagesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.languages.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Sbox(h: 30),
            CustomContainerLW(
              name: AppStrings.arabic.tr(),
              url: 'palestine-flag-icon.png',
              icons: context.locale.toString() == "ar"
                  ? const Icon(
                      Icons.check,
                      color: TColor.mainColor,
                    )
                  : const SizedBox(),
              onTap: () {
                CacheHelper.putString('lang', 'ar');
                context.setLocale(const Locale("ar"));
              },
            ),
            const Sbox(h: 15),
            CustomContainerLW(
              name: AppStrings.english.tr(),
              url: 'gb.png',
              icons: context.locale.toString() == "ar"
                  ? const SizedBox()
                  : const Icon(
                      Icons.check,
                      color: TColor.mainColor,
                    ),
              onTap: () {
                CacheHelper.putString('lang', 'en');

                context.setLocale(const Locale("en"));
              },
            ),
          ],
        ),
      ),
    );
  }
}
