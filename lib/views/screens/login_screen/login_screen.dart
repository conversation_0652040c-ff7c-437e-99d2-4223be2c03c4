import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/screens/forget_password_screen/forget_password_screen.dart';
import 'package:bus_driver/views/screens/home_screen/home.dart';
import 'package:bus_driver/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../bloc/cubit/login_cubit/login_cubit.dart';
import '../../../config/global_variable.dart';
import '../../../helper/cache_helper.dart';
import '../../../helper/response_state.dart';

class LoginScreen extends StatefulWidget {
  static const String routeName = PathRouteName.login;

  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool securityCheck = true;
  bool isChecked = false;
  String? username;
  String? password;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    debugPrint('**token: $token');
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const Sbox(h: 10),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              CustomText(
                text: 'Busaty - Bus'.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const Sbox(h: 40),
              CustomText(
                text: AppStrings.login.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const Sbox(h: 30),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                height: 389.w,
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        const Sbox(h: 40),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.person,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.username.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          validation: AppStrings.username.tr(),
                          radiusNumber: 15.0,
                          onChanged: (value) {
                            username = value;
                          },
                        ),
                        const Sbox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          hintText: AppStrings.password.tr(),
                          security: securityCheck,
                          onChanged: (value) {
                            password = value;
                          },
                          validation: AppStrings.password.tr(),
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck = !securityCheck;
                              });
                            },
                            child: securityCheck
                                ? const Icon(
                                Icons.visibility_off,
                              color: TColor.iconInputColor,
                            )
                                : const Icon(Icons.visibility_outlined,
                              color: TColor.iconInputColor,),
                          ),
                        ),
                        const Sbox(h: 15),
                        SizedBox(
                          width: 307.w,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, ForgetPasswordScreen.routeName);
                                },
                                child: CustomText(
                                  text: AppStrings.forgetPassword.tr(),
                                  color: TColor.textLogin,
                                  fontW: FontWeight.w500,
                                  fontSize: 14,
                                ),
                              ),
                              Row(
                                children: [
                                  CustomText(
                                    text: AppStrings.remember.tr(),
                                    color: TColor.textLogin,
                                    fontW: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                                  SizedBox(
                                    child: Checkbox(
                                      activeColor: TColor.mainColor,
                                      value: isChecked,
                                      onChanged: (value) {
                                        setState(() {
                                          isChecked = value!;
                                        });
                                      },
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                        const Sbox(h: 50),
                        BlocConsumer<LoginCubit, LoginState>(
                          listener: (context, state) {
                            if (state.rStates == ResponseState.success) {
                              if (isChecked) {
                                CacheHelper.putString(
                                    "token", state.loginModel!.token!);
                              }
                              token = state.loginModel!.token;
                               Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const HomeScreen(),));
                             
                              // Navigator.pushReplacementNamed(
                              //     context, HomeScreen.routeName);
                            } else if (state.rStates == ResponseState.failure) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text:
                                        "please enter correct username or password",
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            }
                          },
                          builder: (context, state) {
                            if (state.rStates != ResponseState.loading) {
                              return CustomButton(
                                text: AppStrings.login.tr(),
                                onTap: () {
                                  // Navigator.pushNamed(context, PathRouteName.home);
                                  if (_formKey.currentState!.validate()) {
                                    _formKey.currentState!.save();
                                    context.read<LoginCubit>().login(
                                          username: username,
                                          password: password,
                                        );
                                  }
                                },
                                width: 307,
                                height: 48,
                                radius: 15,
                                borderColor: TColor.mainColor,
                                bgColor: TColor.mainColor,
                              );
                            } else {
                              return const CircularProgressIndicator(
                                color: TColor.mainColor,
                              );
                            }
                          },
                        ),
                        const Sbox(h: 40),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
