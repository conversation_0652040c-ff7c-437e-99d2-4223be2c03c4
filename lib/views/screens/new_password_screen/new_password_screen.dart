import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/screens/done_screen/done_screen.dart';
import 'package:bus_driver/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class NewPasswordScreen extends StatefulWidget {
  static const String routeName = PathRouteName.newPassword;
  const NewPasswordScreen({Key? key}) : super(key: key);

  @override
  State<NewPasswordScreen> createState() => _NewPasswordScreenState();
}

class _NewPasswordScreenState extends State<NewPasswordScreen> {
  bool securityCheck = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const Sbox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const Sbox(h: 60),
              CustomText(
                text: AppStrings.newPassword.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const Sbox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                height: 310.w,
                child: Column(
                  children: [
                    const Sbox(h: 60),
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.newPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheck = !securityCheck;
                          });
                        },
                        child: securityCheck
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                    const Sbox(h: 15),
                    CustomFormFieldWithBorder(
                      prefix: const Icon(
                        Icons.lock_outline,
                        color: TColor.iconInputColor,
                      ),
                      formFieldWidth: 307,
                      borderColor: TColor.fillFormFieldB,
                      fillColor: TColor.fillFormFieldB,
                      radiusNumber: 15.0,
                      hintText: AppStrings.againPassword.tr(),
                      suffix: InkWell(
                        onTap: () {
                          setState(() {
                            securityCheck = !securityCheck;
                          });
                        },
                        child: securityCheck
                            ? const Icon(Icons.visibility_off)
                            : const Icon(Icons.visibility_outlined),
                      ),
                    ),
                    const Sbox(h: 50),
                    CustomButton(
                      text: AppStrings.changePassword.tr(),
                      onTap: () {
                        print("login");
                        Navigator.pushNamed(context, DoneScreen.routeName);
                      },
                      width: 307,
                      height: 48,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                    const Sbox(h: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
