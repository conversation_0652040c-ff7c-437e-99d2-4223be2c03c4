import 'package:bus_driver/bloc/cubit/parents_cubit/parents_cubit.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';

class ParentsScreen extends StatelessWidget {
  static const String routeName = PathRouteName.parentsScreen;

  const ParentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.parents.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: BlocBuilder<ParentsCubit, ParentsState>(
          builder: (context, state) {
            if (state is ParentsLoadingState) {
              return const Center(
                child: CircularProgressIndicator(
                  color: TColor.mainColor,
                ),
              );
            } else if (state is ParentsSuccessState) {
              if (state.parentsModel?.data?.students?.isEmpty ?? true) {
                return SizedBox(
                  width: 300.w,
                  height: 300.w,
                  child: Center(
                    child: CustomText(
                      text: AppStrings.parentsNotFound.tr(),
                      fontSize: 17,
                      fontW: FontWeight.w600,
                    ),
                  ),
                );
              } else {
                int length = state.parentsModel!.data!.students!.length;
                debugPrint("length is: $length");
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2.5),
                      1: FlexColumnWidth(2),
                      2: FlexColumnWidth(1),
                      // 3: FlexColumnWidth(1),
                    },
                    border: TableBorder.all(
                        color: TColor.tabColors,
                        borderRadius: BorderRadius.circular(15.0)),
                    children: List.generate(length  + 1 , (index) {
                      if (index == 0) {
                        return BuildTableRowWidget(
                          cell: [
                            (AppStrings.name.tr()),
                            (AppStrings.address.tr()),
                            (AppStrings.call.tr()),
                            // (AppStrings.notification.tr()),
                          ],
                          header: true,
                        ).build(context);
                      } else {
                        final newParent =
                            state.parentsModel?.data?.students?[index - 1];
                        return BuildTableRowWidget(
                           onTapDown: (p0) async{
                              if (!await launchUrl(
                                  Uri.parse("tel:${newParent?.phone}"))) {
                                throw Exception('Could not launch url');
                              }
                           },
                            is2Icon: false,
                            isTabDown: true,
                            cell: [
                              newParent?.name ?? "--",
                              newParent?.address ?? "لا يوجد عنوان",
                              Icons.call,
                              
                              // Icons.notifications_active,
                            ],
                            // onTapDown: (TapDownDetails details) {
                            //   //Notify
                            //   print('Notify.............');
                            // },
                            // onTapBeforeLastCell: () async{
                            //   print('-----------------------------');
                            //   if (!await launchUrl(Uri.parse(
                            //                         "tel:01022952483"))) {
                            //                       throw Exception(
                            //                           'Could not launch url');
                            //                     }
                              
                              // if (!await launchUrl(
                              //     Uri.parse("tel:${newParent?.phone}"))) {
                              //   throw Exception('Could not launch url');
                              // }
                            // }
                            
                            ).build(context);
                      }
                    }),
                  ),
                );
              }
            } else if (state is ParentsErrorState) {
              debugPrint("${state.error}");
              if (state.error == "parents not found") {
                return Center(
                  child: CustomText(
                    text: AppStrings.parentsNotFound.tr(),
                    fontSize: 17,
                    fontW: FontWeight.w600,
                  ),
                );
              } else {
                return SizedBox(
                  width: 300.w,
                  height: 300.w,
                  child: Center(
                    child: CustomText(
                      text: AppStrings.parentsNotFound.tr(),
                      fontSize: 17,
                      fontW: FontWeight.w600,
                    ),
                  ),
                );
              }
            } else {
              return const SizedBox();
            }
          },
        ),
      ),
    );
  }
}
