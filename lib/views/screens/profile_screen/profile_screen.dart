import 'package:bus_driver/bloc/cubit/profile_cubit/profile_cubit.dart';
import 'package:bus_driver/bloc/cubit/profile_cubit/profile_states.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/custom_widgets/student_data_widget/custom_student_c_w.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileCubit()..getProfile(),
      child: Scaffold(
        appBar: CustomAppBar(
          rightWidget: const SizedBox(),
          titleWidget: Row(
            children: [
              CustomText(
                text: AppStrings.profile.tr(),
                fontSize: 18,
                textAlign: TextAlign.center,
                fontW: FontWeight.w600,
                color: TColor.white,
              ),
              const SizedBox(width: 10.0),
            ],
          ),
          leftWidget: context.locale.toString() == "ar"
              ? InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(AppAssets.arrowBack),
                )
              : InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: SvgPicture.asset(
                    AppAssets.forwardArrow,
                    colorFilter:
                        const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                    width: 20.w,
                    height: 20.w,
                  ),
                ),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 37.w),
          child: BlocBuilder<ProfileCubit, ProfileStates>(
            builder: (context, states) {
              if (states is ProfileLoadingStates) {
                return Center(
                  child: CircularProgressIndicator(),
                );
              } else if (states is ProfileSuccessStates) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      radius: 50.r,
                      backgroundColor: TColor.borderContainer,
                      child: CircleAvatar(
                        radius: 49.r,
                        backgroundColor: TColor.white,
                        backgroundImage: NetworkImage(userImageUrl ??
                            "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                      ),
                    ),
                    const Sbox(h: 30),
                    CustomStudentCW(
                      name: states.profileModels!.name,
                      label: AppStrings.name.tr(),
                      isLabel: true,
                    ),
                    const Sbox(h: 20),
                    CustomStudentCW(
                      name: states.profileModels!.address,
                      label: AppStrings.address.tr(),
                      isLabel: true,
                    ),
                    const Sbox(h: 20),
                    CustomStudentCW(
                      name: states.profileModels!.phone,
                      label: AppStrings.phoneNumber.tr(),
                      isLabel: true,
                    ),
                    const Sbox(h: 20),
                    CustomStudentCW(
                      name: states.profileModels!.username,
                      label: AppStrings.username.tr(),
                      isLabel: true,
                    ),
                    const Sbox(h: 40),
                    // CustomButton(
                    //   text: LocaleKeys.updateProfile.tr(),
                    //   onTap: () {
                    //     Navigator.pushNamed(
                    //         context, UpdateProfileScreen.routeName);
                    //   },
                    //   width: 428,
                    //   height: 45,
                    //   radius: 15,
                    //   borderColor: TColor.mainColor,
                    //   bgColor: TColor.mainColor,
                    // ),
                    // const Sbox(h: 10),
                    // CustomButton(
                    //   text: LocaleKeys.changePassword.tr(),
                    //   onTap: () {
                    //     Navigator.pushNamed(
                    //         context, ChangePasswordScreen.routeName);
                    //   },
                    //   width: 428,
                    //   height: 45,
                    //   radius: 15,
                    //   borderColor: TColor.mainColor,
                    //   bgColor: TColor.mainColor,
                    // ),
                  ],
                );
              } else if (states is ProfileErrorStates) {
                return SizedBox.shrink();
              } else {
                return SizedBox.shrink();
              }
            },
          ),
        ),
      ),
    );
  }
}
