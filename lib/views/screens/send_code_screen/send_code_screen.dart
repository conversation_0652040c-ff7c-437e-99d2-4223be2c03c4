import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/screens/new_password_screen/new_password_screen.dart';
import 'package:bus_driver/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../../config/theme_colors.dart';

class SendCodeScreen extends StatelessWidget {
  static const String routeName = PathRouteName.sendCode;
  SendCodeScreen({Key? key}) : super(key: key);
  String? smsCode;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const Sbox(h: 60),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const Sbox(h: 60),
              CustomText(
                text: AppStrings.sendCode.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const Sbox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                height: 310.w,
                child: Column(
                  children: [
                    const Sbox(h: 30),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 8.0, horizontal: 30.w),
                      child: PinCodeTextField(
                        appContext: context,
                        autoFocus: true,
                        cursorColor: TColor.mainColor,
                        keyboardType: TextInputType.number,
                        length: 4,
                        obscureText: false,
                        animationType: AnimationType.scale,
                        pinTheme: PinTheme(
                          shape: PinCodeFieldShape.box,
                          borderRadius: BorderRadius.circular(5),
                          fieldHeight: 57.w,
                          fieldWidth: 57.w,
                          borderWidth: 1,
                          activeColor: TColor.mainColor,
                          inactiveColor: TColor.mainColor,
                          inactiveFillColor: Colors.white,
                          activeFillColor: Colors.white,
                          selectedColor: TColor.mainColor,
                          selectedFillColor: Colors.white,
                        ),
                        animationDuration: const Duration(milliseconds: 300),
                        backgroundColor: Colors.white,
                        enableActiveFill: true,
                        onCompleted: (submitCode) {
                          smsCode = submitCode;
                          print("Completed");
                        },
                        onChanged: (value) {
                          print(value);
                        },
                        beforeTextPaste: (text) {
                          print("Allowing to paste $text");
                          //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
                          //but you can show anything you want here, like your pop up saying wrong paste format or etc
                          return true;
                        },
                      ),
                    ),
                    CustomText(
                      text: AppStrings.sendCodeRegister.tr(),
                      color: TColor.textLogin,
                      fontW: FontWeight.w400,
                      fontSize: 10,
                    ),
                    const Sbox(h: 50),
                    CustomButton(
                      text: AppStrings.next.tr(),
                      onTap: () {
                        Navigator.pushNamed(
                            context, NewPasswordScreen.routeName);
                      },
                      width: 307,
                      height: 48,
                      radius: 15,
                      borderColor: TColor.mainColor,
                      bgColor: TColor.mainColor,
                    ),
                    const Sbox(h: 20),
                    // InkWell(
                    //   onTap: () {
                    //     Navigator.pushNamed(context, PathRouteName.signup);
                    //   },
                    //   child: CustomText(
                    //     text: LocaleKeys.sendCodeAgain.tr(),
                    //     fontSize: 13,
                    //     fontW: FontWeight.w400,
                    //     color: TColor.mainColor,
                    //   ),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
