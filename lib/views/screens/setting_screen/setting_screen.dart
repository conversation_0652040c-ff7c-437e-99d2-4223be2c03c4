import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/screens/help_screen/help_screen.dart';
import 'package:bus_driver/views/screens/languages_screen/languages_screen.dart';
import 'package:bus_driver/views/screens/profile_screen/profile_screen.dart';
import 'package:bus_driver/views/screens/contact_us_screen/contact_us.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../bloc/cubit/logout_cubit/logout_cubit.dart';
import '../../../bloc/cubit/logout_cubit/logout_states.dart';
import '../../../config/global_variable.dart';
import '../../../helper/response_state.dart';
import '../login_screen/login_screen.dart';

class SettingScreen extends StatelessWidget {
  static const String routeName = PathRouteName.setting;
  const SettingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    print(context.locale.toString());
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 1.sh,
              height: 120.w,
              child: CustomAppBar(
                titleWidget: CustomText(
                  text: AppStrings.setting.tr(),
                  fontSize: 18,
                  textAlign: TextAlign.center,
                  fontW: FontWeight.w600,
                  color: TColor.white,
                ),
                leftWidget: context.locale.toString() == "ar"
                    ? InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.asset(AppAssets.arrowBack),
                      )
                    : InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: SvgPicture.asset(
                          AppAssets.forwardArrow,
                          colorFilter: const ColorFilter.mode(
                              TColor.white, BlendMode.srcIn),
                          width: 20.w,
                          height: 20.w,
                        ),
                      ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 31.r,
                    backgroundColor: TColor.mainColor,
                    child: CircleAvatar(
                      radius: 30.r,
                      backgroundColor: TColor.white,
                      backgroundImage: NetworkImage(userImageUrl ??
                          "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
                    ),
                  ),
                  const Sbox(w: 20),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        color: Colors.black54,
                        text: name,
                        fontSize: 17,
                        fontW: FontWeight.w600,
                      ),
                      CustomText(
                        color: Colors.black54,
                        text: type == "drivers"
                            ? AppStrings.driver.tr()
                            : AppStrings.supervisor.tr(),
                        fontSize: 13,
                        fontW: FontWeight.w600,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const Sbox(h: 15),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: const Divider(),
            ),
            ListTile(
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (ctx) {
                  return const ProfileScreen();
                }));
              },
              title: CustomText(
                color: Colors.black54,
                text: AppStrings.profile.tr(),
                fontSize: 15,
                fontW: FontWeight.w600,
              ),
              trailing: Icon(
                Icons.arrow_forward_ios_outlined,
                size: 16.sp,
                color: Colors.black54,
              ),
            ),
            ListTile(
              onTap: () {
                Navigator.pushNamed(context, LanguagesScreen.routeName);
              },
              title: CustomText(
                color: Colors.black54,
                text: AppStrings.languages.tr(),
                fontSize: 15,
                fontW: FontWeight.w600,
              ),
              trailing: Icon(
                Icons.arrow_forward_ios_outlined,
                size: 16.sp,
                color: Colors.black54,
              ),
            ),
            ListTile(
              onTap: () {
                Navigator.pushNamed(context, HelpScreen.routeName);
              },
              title: CustomText(
                text: AppStrings.help.tr(),
                fontSize: 15,
                fontW: FontWeight.w600,
                color: Colors.black54,
              ),
              trailing: Icon(
                Icons.arrow_forward_ios_outlined,
                size: 16.sp,
                color: Colors.black54,
              ),
            ),
            ListTile(
              onTap: () {
                Navigator.pushNamed(context, ContactUsScreen.routeName);
              },
              title: CustomText(
                text: AppStrings.contactUs.tr(),
                fontSize: 15,
                fontW: FontWeight.w600,
                color: Colors.black54,
              ),
              trailing: Icon(
                Icons.arrow_forward_ios_outlined,
                size: 16.sp,
                color: Colors.black54,
              ),
            ),
            BlocConsumer<LogoutCubit, LogoutStates>(
              listener: (context, states) {
                if (states.rStates == ResponseState.success) {
                  // ScaffoldMessenger.of(context).showSnackBar(
                  //   SnackBar(
                  //     backgroundColor: TColor.redAccent,
                  //     content: CustomText(
                  //       text: states.changePasswordModels
                  //           ?.changePasswordStatusModels?.messages,
                  //       color: TColor.white,
                  //     ),
                  //   ),
                  // );
                } else if (states.rStates == ResponseState.failure) {
                  print("error working");
                }
              },
              builder: (context, states) {
                if (states.rStates != ResponseState.loading) {
                  return Column(
                    children: [
                      ListTile(
                        onTap: () {
                          context.read<LogoutCubit>().logout();
                          Navigator.pop(context);
                          Navigator.pushReplacementNamed(
                              context, LoginScreen.routeName);
                        },
                        title: CustomText(
                          color: Colors.redAccent,
                          text: AppStrings.logout.tr(),
                          fontSize: 15,
                          fontW: FontWeight.w600,
                        ),
                        trailing: Icon(
                          Icons.arrow_forward_ios_outlined,
                          size: 16.sp,
                          color: Colors.redAccent,
                        ),
                      ),
                      ListTile(
                        onTap: () {
                          // Show confirmation dialog before deleting account
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: CustomText(
                                text: AppStrings.deleteAccountTitle.tr(),
                                fontSize: 16,
                                fontW: FontWeight.bold,
                              ),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                    text: AppStrings.deleteAccountConfirm.tr(),
                                    maxLine: 3,
                                    fontSize: 14,
                                  ),
                                  const Sbox(h: 10),
                                  CustomText(
                                    text: AppStrings.deleteAccountNote.tr(),
                                    maxLine: 3,
                                    fontSize: 14,
                                    color: Colors.red,
                                  ),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  child: CustomText(
                                    text: AppStrings.cancel.tr(),
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () async {
                                    context.read<LogoutCubit>().logout();
                                    Navigator.pop(context);
                                    Navigator.pushReplacementNamed(
                                        context, LoginScreen.routeName);
                                  },
                                  child: CustomText(
                                    text: AppStrings.deleteAccount.tr(),
                                    fontSize: 14,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                        title: CustomText(
                          color: Colors.red,
                          text: AppStrings.deleteAccount.tr(),
                          fontSize: 15,
                          fontW: FontWeight.w600,
                        ),
                        trailing: Icon(
                          Icons.delete_forever,
                          size: 20.sp,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  );
                } else {
                  return const CircularProgressIndicator(
                    color: TColor.mainColor,
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
