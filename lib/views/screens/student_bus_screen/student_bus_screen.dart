import 'dart:io';

import 'package:android_intent_plus/android_intent.dart';
import 'package:bus_driver/views/custom_widgets/custom_form_field_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../bloc/cubit/parents_cubit/parents_cubit.dart';
import '../../../bloc/cubit/students_cubit/students_cubit.dart';
import '../../../bloc/cubit/students_cubit/students_state.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../data/models/students_model.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/custom_container_dialog_w.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';
import '../parents_screen/parents_screen.dart';
import '../student_address_screen/student_address_screen.dart';
import '../student_data_screen/student_data_screen.dart';

class StudentBusScreen extends StatefulWidget {
  static const String routeName = PathRouteName.studentBus;

  const StudentBusScreen({Key? key}) : super(key: key);

  @override
  State<StudentBusScreen> createState() => _StudentBusScreenState();
}

class _StudentBusScreenState extends State<StudentBusScreen> {
  TextEditingController search = TextEditingController();
  bool haveData = false;
  List<Students> searchStudentData = [];

  @override
  void initState() {
    search.addListener(() {
      if (search.text.isNotEmpty) {
        haveData = true;
      } else {
        haveData = false;
      }
      setState(() {});
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    print(haveData);
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.studentBus.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            BlocBuilder<StudentsCubit, StudentsState>(
              builder: (context, state) {
                if (state is StudentsLoadingState) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: TColor.mainColor,
                    ),
                  );
                } else if (state is StudentsSuccessState) {
                  if (state.studentsModel?.data?.students?.isEmpty ?? true) {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.studentsNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    int length = state.studentsModel!.data!.students!.length;
                    debugPrint("$length");
                    return Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomFormFieldWithBorder(
                              paddingLeft: 0,
                              paddingRight: 0,
                              controller: search,
                              formFieldWidth:
                                  MediaQuery.of(context).size.width - 30.0.w,
                              height: true,
                              onChanged: (value) {
                                searchStudentData = [];
                                state.studentsModel!.data!.students!
                                    .forEach((element) {
                                  if (element.name!.contains(value)) {
                                    searchStudentData.add(element);
                                  }
                                });
                                setState(() {});
                              },
                              hintText: AppStrings.searchForStudent.tr(),
                              fillColor: TColor.fillFormFieldB,
                              borderColor: TColor.fillFormFieldB,
                              // suffix: InkWell(
                              //   onTap: () {},
                              //   child: const Icon(Icons.search),
                              // ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        haveData
                            ? Column(
                                children: [
                                  context.locale.toString() == "ar"
                                      ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                                AppAssets.supervisorIcon),
                                            const SizedBox(width: 5.0),
                                            CustomText(
                                              text:
                                                  "إجمالي عدد الطلبة: ${searchStudentData.length}",
                                              color: TColor.black,
                                              fontW: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ],
                                        )
                                      : Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'All students: ${searchStudentData.length}',
                                              style: const TextStyle(
                                                  color: TColor.black,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            const SizedBox(width: 5.0),
                                            SvgPicture.asset(
                                                AppAssets.supervisorIcon),
                                          ],
                                        ),
                                  const SizedBox(height: 5.0),
                                  Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 10.0),
                                    clipBehavior: Clip.antiAlias,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15.0),
                                    ),
                                    child: Table(
                                      columnWidths: const {
                                        0: FlexColumnWidth(2.5),
                                        1: FlexColumnWidth(2),
                                        2: FlexColumnWidth(2),
                                        3: FlexColumnWidth(1),
                                      },
                                      border: TableBorder.all(
                                          color: TColor.tabColors,
                                          borderRadius:
                                              BorderRadius.circular(15.0)),
                                      children: List.generate(
                                          searchStudentData.length + 1,
                                          (index) {
                                        if (index == 0) {
                                          return BuildTableRowWidget(
                                            cell: [
                                              (AppStrings.name.tr()),
                                              (AppStrings.address.tr()),
                                              (AppStrings.classroom.tr()),
                                              (AppStrings.show.tr()),
                                            ],
                                            header: true,
                                          ).build(context);
                                        } else {
                                          final newStudent =
                                              searchStudentData[index - 1];
                                          return BuildTableRowWidget(
                                            isTabDown: true,
                                            cell: [
                                              newStudent.name ?? '--',
                                              newStudent.cityName ?? '--',
                                              newStudent.classroom?.name ?? '--',
                                              Icons.more_horiz,
                                            ],
                                            onTapDown:
                                                (TapDownDetails details) {
                                              final RenderBox overlay =
                                                  Overlay.of(context)
                                                          .context
                                                          .findRenderObject()
                                                      as RenderBox;
                                              final Offset offset = overlay
                                                  .localToGlobal(Offset.zero);
                                              showMenu(
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                15.sp))),
                                                context: context,
                                                position: RelativeRect.fromLTRB(
                                                  context.locale.toString() ==
                                                          "ar"
                                                      ? offset.dx - 100
                                                      : offset.dx + 100,
                                                  details.globalPosition.dy +
                                                      15,
                                                  0,
                                                  0,
                                                ),
                                                items: [
                                                  PopupMenuItem(
                                                    child: Center(
                                                      child:
                                                          CustomContainerDialogW(
                                                        icons: Icons
                                                            .remove_red_eye,
                                                        name: AppStrings
                                                            .showStudent
                                                            .tr(),
                                                        onTap: () {
                                                          Navigator.of(context)
                                                            ..pop()
                                                            ..pushNamed(
                                                              StudentDataScreen
                                                                  .routeName,
                                                              arguments:
                                                                  newStudent,
                                                            );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  PopupMenuItem(
                                                    child: Center(
                                                      child:
                                                          CustomContainerDialogW(
                                                        icons:
                                                            CupertinoIcons.eye,
                                                        name: AppStrings
                                                            .showParent
                                                            .tr(),
                                                        onTap: () {
                                                          BlocProvider.of<
                                                                      ParentsCubit>(
                                                                  context)
                                                              .getParents(
                                                                  id: newStudent
                                                                      .id);
                                                          Navigator.of(context)
                                                            ..pop()
                                                            ..pushNamed(
                                                                ParentsScreen
                                                                    .routeName);
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  PopupMenuItem(
                                                    child: Center(
                                                      child:
                                                          CustomContainerDialogW(
                                                        icons: CupertinoIcons
                                                            .map_pin_ellipse,
                                                        name: AppStrings
                                                            .addressStudentOnMap
                                                            .tr(),
                                                        onTap: () async {
                                                          if (newStudent?.latitude == null || newStudent?.longitude == null) {
                                                            Fluttertoast.showToast(
                                                              msg: "Location coordinates not available",
                                                              backgroundColor: TColor.error,
                                                              textColor: TColor.white,
                                                            );
                                                            return;
                                                          }

                                                          final lat = newStudent!.latitude;
                                                          final lng = newStudent.longitude;

                                                          if (Platform.isAndroid) {
                                                            // Try Google Maps app first
                                                            final googleMapsUri = Uri.parse(
                                                              "google.navigation:q=$lat,$lng"
                                                            );

                                                            try {
                                                              if (await canLaunchUrl(googleMapsUri)) {
                                                                await launchUrl(googleMapsUri);
                                                              } else {
                                                                // Fallback to web URL
                                                                final webUrl = Uri.parse(
                                                                  "https://www.google.com/maps/search/?api=1&query=$lat,$lng"
                                                                );
                                                                await launchUrl(webUrl, mode: LaunchMode.externalApplication);
                                                              }
                                                            } catch (e) {
                                                              Fluttertoast.showToast(
                                                                msg: "Could not open maps application",
                                                                backgroundColor: TColor.error,
                                                                textColor: TColor.white,
                                                              );
                                                            }
                                                          } else if (Platform.isIOS) {
                                                            // For iOS devices
                                                            final url = Uri.parse(
                                                              "https://maps.apple.com/?q=$lat,$lng"
                                                            );

                                                            try {
                                                              if (await canLaunchUrl(url)) {
                                                                await launchUrl(url);
                                                              } else {
                                                                // Fallback to web URL
                                                                final webUrl = Uri.parse(
                                                                  "https://www.google.com/maps/search/?api=1&query=$lat,$lng"
                                                                );
                                                                await launchUrl(webUrl, mode: LaunchMode.externalApplication);
                                                              }
                                                            } catch (e) {
                                                              Fluttertoast.showToast(
                                                                msg: "Could not open maps application",
                                                                backgroundColor: TColor.error,
                                                                textColor: TColor.white,
                                                              );
                                                            }
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ).build(context);
                                        }
                                      }),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  )
                                ],
                              )
                            : Column(
                                children: [
                                  context.locale.toString() == "ar"
                                      ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                                AppAssets.supervisorIcon),
                                            const SizedBox(width: 5.0),
                                            CustomText(
                                              text:
                                                  "إجمالي عدد الطلبة: $length",
                                              color: TColor.black,
                                              fontW: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ],
                                        )
                                      : Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'All students: $length',
                                              style: const TextStyle(
                                                  color: TColor.black,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            const SizedBox(width: 5.0),
                                            SvgPicture.asset(
                                                AppAssets.supervisorIcon),
                                          ],
                                        ),
                                  const SizedBox(height: 5.0),
                                  Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 10.0),
                                    clipBehavior: Clip.antiAlias,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15.0),
                                    ),
                                    child: Table(
                                      columnWidths: const {
                                        0: FlexColumnWidth(2.5),
                                        1: FlexColumnWidth(2),
                                        2: FlexColumnWidth(2),
                                        3: FlexColumnWidth(1),
                                      },
                                      border: TableBorder.all(
                                          color: TColor.tabColors,
                                          borderRadius:
                                              BorderRadius.circular(15.0)),
                                      children:
                                          List.generate(length + 1, (index) {
                                        if (index == 0) {
                                          return BuildTableRowWidget(
                                            cell: [
                                              (AppStrings.name.tr()),
                                              (AppStrings.address.tr()),
                                              (AppStrings.classroom.tr()),
                                              (AppStrings.show.tr()),
                                            ],
                                            header: true,
                                          ).build(context);
                                        } else {
                                          final newStudent = state.studentsModel
                                              ?.data?.students?[index - 1];
                                          return BuildTableRowWidget(
                                            isTabDown: true,
                                            cell: [
                                              newStudent?.name ?? '--',
                                              newStudent?.address ?? '--',
                                              newStudent?.classroom?.name ?? '--',
                                              Icons.more_horiz,
                                            ],
                                            onTapDown:
                                                (TapDownDetails details) {
                                              final RenderBox overlay =
                                                  Overlay.of(context)
                                                          .context
                                                          .findRenderObject()
                                                      as RenderBox;
                                              final Offset offset = overlay
                                                  .localToGlobal(Offset.zero);
                                              showMenu(
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                15.sp))),
                                                context: context,
                                                position: RelativeRect.fromLTRB(
                                                  context.locale.toString() ==
                                                          "ar"
                                                      ? offset.dx - 100
                                                      : offset.dx + 100,
                                                  details.globalPosition.dy +
                                                      15,
                                                  0,
                                                  0,
                                                ),
                                                items: [
                                                  PopupMenuItem(
                                                    child: Center(
                                                      child:
                                                          CustomContainerDialogW(
                                                        icons: Icons
                                                            .remove_red_eye,
                                                        name: AppStrings
                                                            .showStudent
                                                            .tr(),
                                                        onTap: () {
                                                          Navigator.of(context)
                                                            ..pop()
                                                            ..pushNamed(
                                                              StudentDataScreen
                                                                  .routeName,
                                                              arguments:
                                                                  newStudent,
                                                            );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  PopupMenuItem(
                                                    child: Center(
                                                      child:
                                                          CustomContainerDialogW(
                                                        icons:
                                                            CupertinoIcons.eye,
                                                        name: AppStrings
                                                            .showParent
                                                            .tr(),
                                                        onTap: () {
                                                          BlocProvider.of<
                                                                      ParentsCubit>(
                                                                  context)
                                                              .getParents(
                                                                  id: newStudent
                                                                      ?.id);
                                                          Navigator.of(context)
                                                            ..pop()
                                                            ..pushNamed(
                                                                ParentsScreen
                                                                    .routeName);
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  PopupMenuItem(
                                                    child: Center(
                                                      child:
                                                          CustomContainerDialogW(
                                                        icons: CupertinoIcons
                                                            .map_pin_ellipse,
                                                        name: AppStrings
                                                            .addressStudentOnMap
                                                            .tr(),
                                                        onTap: () async {
                                                          if (newStudent?.latitude == null || newStudent?.longitude == null) {
                                                            Fluttertoast.showToast(
                                                              msg: "Location coordinates not available",
                                                              backgroundColor: TColor.error,
                                                              textColor: TColor.white,
                                                            );
                                                            return;
                                                          }

                                                          final lat = newStudent!.latitude;
                                                          final lng = newStudent.longitude;

                                                          if (Platform.isAndroid) {
                                                            // Try Google Maps app first
                                                            final googleMapsUri = Uri.parse(
                                                              "google.navigation:q=$lat,$lng"
                                                            );

                                                            try {
                                                              if (await canLaunchUrl(googleMapsUri)) {
                                                                await launchUrl(googleMapsUri);
                                                              } else {
                                                                // Fallback to web URL
                                                                final webUrl = Uri.parse(
                                                                  "https://www.google.com/maps/search/?api=1&query=$lat,$lng"
                                                                );
                                                                await launchUrl(webUrl, mode: LaunchMode.externalApplication);
                                                              }
                                                            } catch (e) {
                                                              Fluttertoast.showToast(
                                                                msg: "Could not open maps application",
                                                                backgroundColor: TColor.error,
                                                                textColor: TColor.white,
                                                              );
                                                            }
                                                          } else if (Platform.isIOS) {
                                                            // For iOS devices
                                                            final url = Uri.parse(
                                                              "https://maps.apple.com/?q=$lat,$lng"
                                                            );

                                                            try {
                                                              if (await canLaunchUrl(url)) {
                                                                await launchUrl(url);
                                                              } else {
                                                                // Fallback to web URL
                                                                final webUrl = Uri.parse(
                                                                  "https://www.google.com/maps/search/?api=1&query=$lat,$lng"
                                                                );
                                                                await launchUrl(webUrl, mode: LaunchMode.externalApplication);
                                                              }
                                                            } catch (e) {
                                                              Fluttertoast.showToast(
                                                                msg: "Could not open maps application",
                                                                backgroundColor: TColor.error,
                                                                textColor: TColor.white,
                                                              );
                                                            }
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ).build(context);
                                        }
                                      }),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  )
                                ],
                              ),
                      ],
                    );
                  }
                } else if (state is StudentsErrorState) {
                  debugPrint("${state.error}");
                  if (state.error == "students not found") {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.studentsNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  } else {
                    return SizedBox(
                      width: 300.w,
                      height: 300.w,
                      child: Center(
                        child: CustomText(
                          text: AppStrings.studentsNotFound.tr(),
                          fontSize: 17,
                          fontW: FontWeight.w600,
                        ),
                      ),
                    );
                  }
                } else {
                  return const SizedBox();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
