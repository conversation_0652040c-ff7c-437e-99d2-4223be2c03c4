import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/views/screens/parents_screen/parents_screen.dart';
import 'package:bus_driver/widgets/bus_data_widgets/custom_container_bus_s_w.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../../bloc/cubit/parents_cubit/parents_cubit.dart';
import '../../../data/models/students_model.dart';
import '../student_address_screen/student_address_screen.dart';

class StudentDataScreen extends StatelessWidget {
  static const String routeName = PathRouteName.studentData;
  const StudentDataScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as Students?;
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.studentData.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 37.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Sbox(h: 20),
            CircleAvatar(
              radius: 50.r,
              backgroundColor: TColor.borderContainer,
              child: CircleAvatar(
                radius: 49.r,
                backgroundColor: TColor.white,
                backgroundImage: NetworkImage(args?.logoPath ??
                    "https://www.nj.com/resizer/iqV2J-QFgh0227ybHBor4exTVBk=/800x0/smart/cloudfront-us-east-1.images.arcpublishing.com/advancelocal/SJGKVE5UNVESVCW7BBOHKQCZVE.jpg"),
              ),
            ),
            const Sbox(h: 30),
            CustomContainerBusSW(
              name: args?.name ?? "--",
              label: AppStrings.name.tr(),
              isLabel: true,
            ),
            const Sbox(h: 20),
            CustomContainerBusSW(
              name: args?.address ?? "--",
              label: AppStrings.address.tr(),
              isLabel: true,
            ),
            const Sbox(h: 20),
            CustomContainerBusSW(
              name: args?.grade?.name ?? "--",
              label: AppStrings.stage.tr(),
              isLabel: true,
            ),
            // const Sbox(h: 20),
            // CustomContainerBusSW(
            //   name: args?.typeBlood?.name ?? "--",
            //   label: AppStrings.bloodType.tr(),
            //   isLabel: true,
            // ),
            // const Sbox(h: 20),
            // CustomContainerBusSW(
            //   name: args?.dateBirth ?? "--",
            //   label: AppStrings.birthDate.tr(),
            //   isLabel: true,
            // ),
            const Sbox(h: 20),
            CustomContainerBusSW(
              name: args?.parentKey ?? "--",
              label: AppStrings.code.tr(),
              isLabel: true,
            ),
            const Sbox(h: 20),
            CustomContainerBusSW(
              name: args?.parentSecret ?? "--",
              label: AppStrings.secretCode.tr(),
              isLabel: true,
            ),
            const Sbox(h: 30),
            CustomButton(
              onTap: () {
                BlocProvider.of<ParentsCubit>(context).getParents(id: args?.id);
                Navigator.of(context).pushNamed(ParentsScreen.routeName);
              },
              fontSize: 13,
              fontWeight: FontWeight.w400,
              width: 354,
              height: 43,
              bgColor: TColor.mainColor,
              borderColor: TColor.mainColor,
              text: AppStrings.parents.tr(),
            ),
            const Sbox(h: 15),
            CustomButton(
              onTap: () {
                if (args!.latitude != null && args.longitude != null) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => StudentAddressScreen(
                        lat: double.parse(args.latitude!),
                        long: double.parse(args.longitude!),
                      ),
                    ),
                  );
                } else {
                  Fluttertoast.showToast(
                      msg: "Not Have any location",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.BOTTOM,
                      timeInSecForIosWeb: 1,
                      backgroundColor: TColor.error,
                      textColor: TColor.white,
                      fontSize: 16.0);
                }
              },
              fontSize: 13,
              fontWeight: FontWeight.w400,
              width: 354,
              height: 43,
              bgColor: TColor.mainColor,
              borderColor: TColor.mainColor,
              text: AppStrings.addressOnMap.tr(),
            ),
            const Sbox(h: 30),
          ],
        ),
      ),
    );
  }
}
