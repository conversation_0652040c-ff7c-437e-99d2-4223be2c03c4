import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
import '../../../../config/theme_colors.dart';
import '../../../../translations/local_keys.g.dart';
import '../../../custom_widgets/build_table_row_widget.dart';
import '../../../custom_widgets/custom_text.dart';

class AbsentBodyEveningTrip extends StatelessWidget {
  const AbsentBodyEveningTrip({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EveningTripCubit, EveningTripState>(
      builder: (context, state) {
        if (state is StartEveningTripLoadingState ||
            state is StartEveningTripSuccessState) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is EveningTripInitial ||
            state is EveningTripStatusErrorState ||
            state is StartEveningTripErrorState ||
            state is EndEveningTripSuccessState) {
          return SizedBox(
            width: 300.w,
            height: 300.w,
            child: Center(
              child: CustomText(
                text: AppStrings.tripClosedStartTrip.tr(),
                fontSize: 17,
                fontW: FontWeight.w600,
              ),
            ),
          );
        } else {
          if (EveningTripCubit.get(context)
                  .absentEveningTripModel
                  ?.absences
                  ?.isEmpty ??
              true) {
            return SizedBox(
              width: 300.w,
              height: 300.w,
              child: Center(
                child: CustomText(
                  text: AppStrings.studentsNotFound.tr(),
                  fontSize: 17,
                  fontW: FontWeight.w600,
                ),
              ),
            );
          } else {
            int length = EveningTripCubit.get(context)
                .absentEveningTripModel!
                .absences!
                .length;
            debugPrint("students absent: $length");
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(1.5),
                    },
                    border: TableBorder.all(
                        color: TColor.tabColors,
                        borderRadius: BorderRadius.circular(15.0)),
                    children: [
                      BuildTableRowWidget(
                        cell: [
                          (AppStrings.name.tr()),
                          (AppStrings.moveToPresent.tr()),
                        ],
                        header: true,
                      ).build(context),
                      ...List.generate(length, (index) {
                        final newStudent = EveningTripCubit.get(context)
                            .absentEveningTripModel
                            ?.absences?[index];
                        return BuildTableRowWidget(
                          cell: [
                            newStudent?.name,
                            Icons.compare_arrows,
                          ],
                          onTapLastCell: () async {
                            //نقل إلى حاضر
                            EveningTripCubit.get(context)
                                .removeAbsentEveningTrip(
                                    studentId: newStudent!.id);
                            // await NotificationsCubit.get(context)
                            //     .getParentFcmToken(studentId: newStudent?.id);
                            // if (context.mounted) {
                            //   if (NotificationsCubit.get(context)
                            //           .parentFcmToken
                            //           ?.data
                            //           ?.isNotEmpty ==
                            //       true) {
                            //     NotificationsRepo().sendNotification(
                            //       deviceTokens: NotificationsCubit.get(context)
                            //           .parentFcmToken!
                            //           .data!,
                            //       title: 'نعتذر عن الخطأ',
                            //       body:
                            //           'الطالب ${newStudent?.name} ما زال موجود في الباص بالفعل',
                            //     );
                            //   }
                            // }
                          },
                        ).build(context);
                      }),
                    ],
                  ),
                ),
              ),
            );
          }
        }
      },
    );
  }
}
