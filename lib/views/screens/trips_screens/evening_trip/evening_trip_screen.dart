import 'package:bus_driver/bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:geolocator/geolocator.dart';

import '../../../../bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
import '../../../../config/theme_colors.dart';
import '../../../../services/location_service/location_service.dart';
import '../../../../translations/local_keys.g.dart';
import '../../../../utils/assets_utils.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/custom_container_dialog_w.dart';
import '../../../custom_widgets/custom_text.dart';
import '../trip_alert_dialog.dart';
import 'absent_body_evening_trip.dart';
import 'arrived_home_body_evening_trip.dart';
import 'on_bus_body_evening_trip.dart';

class EveningTripScreen extends StatefulWidget {
  static const String routeName = PathRouteName.eveningTripScreen;

  const EveningTripScreen({Key? key}) : super(key: key);

  @override
  State<EveningTripScreen> createState() => _EveningTripScreenState();
}

class _EveningTripScreenState extends State<EveningTripScreen>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  Position? currentPosition;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 3, vsync: this);
    getCurrentLocation();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  void getCurrentLocation() async {
    currentPosition = await LocationService.getCurrentPosition();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EveningTripCubit, EveningTripState>(
      listener: (context, state) {
        if (state is EndEveningTripSuccessState) {
          // Navigate back to home screen when trip ends successfully
          Navigator.pushNamedAndRemoveUntil(
            context,
            PathRouteName.home,
            (route) => false,
          );
        }
      },
      child: Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.eveningTrips.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: BlocBuilder<EveningTripCubit, EveningTripState>(
        builder: (context, state) {
          if (state is EveningTripStatusLoadingState) {
            return const Center(
              child: CircularProgressIndicator(
                color: TColor.mainColor,
              ),
            );
          } else {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      state is StartEveningTripLoadingState
                          ? ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: TColor.mainColor,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                              ),
                              onPressed: () {},
                              child: const SizedBox(
                                width: 25,
                                height: 25,
                                child: CircularProgressIndicator(
                                  color: TColor.white,
                                ),
                              ))
                          : state is StartEveningTripSuccessState ||
                                  state is EndEveningTripErrorState ||
                                  state is OnBusEveningTripSuccessState ||
                                  state is ArrivedEveningTripSuccessState ||
                                  state is AbsentEveningTripSuccessState
                              ? ElevatedButton(
                                  onPressed: () async {
                                    EveningTripCubit.get(context)
                                        .endEveningTrip(tripId: tripId);
                                    // await showDialog(
                                    //     context: context,
                                    //     builder: (context) => TripAlertDialog(
                                    //           isEndTrip: true,
                                    //           isEveningTrip: true,
                                    //           title: AppStrings.endTrip.tr(),
                                    //           titleColor: TColor.redAccent,
                                    //           content:
                                    //               AppStrings.sureEndTrip.tr(),
                                    //           onTapYes: () {
                                    //             Logger()
                                    //                 .e("============== yes ");
                                    //             EveningTripCubit.get(context)
                                    //                 .endEveningTrip(
                                    //                     tripId: tripId);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: TColor.redAccent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                  ),
                                  child: CustomText(
                                    text: AppStrings.endTrip.tr(),
                                    fontSize: 18,
                                    fontW: FontWeight.w600,
                                    color: TColor.white,
                                  ),
                                )
                              : state is EndEveningTripLoadingState
                                  ? ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: TColor.redAccent,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                        ),
                                      ),
                                      onPressed: () {},
                                      child: const SizedBox(
                                        width: 25,
                                        height: 25,
                                        child: CircularProgressIndicator(
                                          color: TColor.white,
                                        ),
                                      ))
                                  : ElevatedButton(
                                      onPressed: () async {
                                        await showDialog(
                                            context: context,
                                            builder: (context) =>
                                                TripAlertDialog(
                                                  isEveningTrip: true,
                                                  title:
                                                      AppStrings.startTrip.tr(),
                                                  titleColor: TColor.mainColor,
                                                  content: AppStrings
                                                      .sureStartTrip
                                                      .tr(),
                                                  onTapYes: () {
                                                    // Logger().e("============= working");
                                                    EveningTripCubit.get(
                                                            context)
                                                        .startEveningTrip(
                                                      latitude:
                                                          '${currentPosition?.latitude}',
                                                      longitude:
                                                          '${currentPosition?.longitude}',
                                                      // notify: true
                                                    );
                                                    // NotificationsCubit.get(
                                                    //         context)
                                                    //     .getAllParentsFcmTokens();

                                                    // Navigator.pushNamed(
                                                    //     context, PathRouteName.openStreetMap);
                                                  },
                                                ));
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: TColor.mainColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                        ),
                                      ),
                                      child: CustomText(
                                        text: AppStrings.startTrip.tr(),
                                        fontSize: 18,
                                        fontW: FontWeight.w600,
                                        color: TColor.white,
                                      ),
                                    ),
                      const Spacer(),
                      (MorningTripCubit.get().groupAndSingleMessageModel !=
                              null)
                          ? InkWell(
                              onTapDown: (TapDownDetails details) {
                                final RenderBox overlay = Overlay.of(context)
                                    .context
                                    .findRenderObject() as RenderBox;
                                final Offset offset =
                                    overlay.localToGlobal(Offset.zero);
                                showMenu(
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(15.sp))),
                                    context: context,
                                    position: RelativeRect.fromLTRB(
                                      context.locale.toString() == "ar"
                                          ? offset.dx - 100
                                          : offset.dx + 100,
                                      details.globalPosition.dy + 18,
                                      0,
                                      0,
                                    ),
                                    items: (MorningTripCubit.get()
                                            .groupAndSingleMessageModel!
                                            .data!
                                            .group!
                                            .isNotEmpty)
                                        ? List.generate(
                                            MorningTripCubit.get()
                                                .groupAndSingleMessageModel!
                                                .data!
                                                .group!
                                                .length,
                                            (index) =>

                                                // [

                                                PopupMenuItem(
                                              child: Center(
                                                child: CustomContainerDialogW(
                                                  icons: Icons
                                                      .notifications_active,
                                                  name: MorningTripCubit.get()
                                                      .groupAndSingleMessageModel!
                                                      .data!
                                                      .group![index]
                                                      .body!,
                                                  onTap: () {
                                                    // MorningTripCubit.get().sendMessageMorningTrip(messageId: MorningTripCubit.get().groupAndSingleMessageModel!.data!.group![index].id!);

                                                    EveningTripCubit.get(
                                                            context)
                                                        .sendMessageForAllEveningTrip(
                                                            staticMessageId:
                                                                MorningTripCubit
                                                                        .get()
                                                                    .groupAndSingleMessageModel!
                                                                    .data!
                                                                    .group![
                                                                        index]
                                                                    .id!);

                                                    // EveningTripCubit.get(context)
                                                    //     .sendMessageForAllEveningTripBool(
                                                    //   message: 'الباص تحرك من المدرسة',
                                                    //   message_en:
                                                    //       'The bus has left the school',
                                                    //   notificationType: "tracking",
                                                    // );
                                                    //   .then((value) {
                                                    // final notificationRepo =
                                                    //     NotificationsRepo();
                                                    // notificationRepo
                                                    //     .allParentsFcmTokens()
                                                    //     .then((value) {
                                                    //   if (value.data!.isNotEmpty ==
                                                    //       true) {
                                                    //     NotificationsRepo()
                                                    //         .sendNotification(

                                                    //       deviceTokens:
                                                    //           NotificationsCubit.get(
                                                    //                   context)
                                                    //               .allParentsFcmTokens!
                                                    //               .data!,
                                                    //       title: 'مساء الخير',
                                                    //       body: 'الباص تحرك من المدرسة',
                                                    //     );
                                                    //   }
                                                    Navigator.pop(context);
                                                    // }

                                                    // );
                                                    // });
                                                  },
                                                ),
                                              ),
                                            ),

                                            // PopupMenuItem(
                                            //   child: Center(
                                            //     child: CustomContainerDialogW(
                                            //       icons: Icons.notification_add,
                                            //       name: 'الباص وصل المدرسة',
                                            //       onTap: () {
                                            //         EveningTripCubit.get(context)
                                            //             .sendMessageForAllEveningTripBool(
                                            //           message: 'الباص وصل المدرسة',
                                            //           notificationType: "no-tracking",
                                            //           message_en:
                                            //               'The bus arrived at school',
                                            //         );
                                            //         //     .then((value) {
                                            //         //   if (value = false) {
                                            //         //     final notificationRepo =
                                            //         //         NotificationsRepo();
                                            //         //     notificationRepo
                                            //         //         .allParentsFcmTokens()
                                            //         //         .then((value) {
                                            //         //       if (value.data!.isNotEmpty ==
                                            //         //           true) {
                                            //         //         NotificationsRepo()
                                            //         //             .sendNotification(
                                            //         //           deviceTokens:
                                            //         //               NotificationsCubit.get(
                                            //         //                       context)
                                            //         //                   .allParentsFcmTokens!
                                            //         //                   .data!,
                                            //         //           title: 'مساء الخير',
                                            //         //           body: 'الباص وصل المدرسة',
                                            //         //         );
                                            //         //       }
                                            //               Navigator.pop(context);
                                            //         //     });
                                            //         //   }
                                            //         // });
                                            //       },
                                            //     ),
                                            //   ),
                                            // ),

                                            // ],
                                          )
                                        : []);
                              },
                              child: Container(
                                padding: const EdgeInsetsDirectional.symmetric(
                                    horizontal: 10),
                                height: 37,
                                // width: 135,
                                decoration: ShapeDecoration(
                                    color: TColor.mainColor,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10.0),
                                    )),
                                child: Row(
                                  children: [
                                    const Icon(
                                      CupertinoIcons.bell,
                                      color: TColor.white,
                                    ),
                                    10.horizontalSpace,
                                    CustomText(
                                      text: AppStrings.sendNotification.tr(),
                                      fontSize: 18,
                                      fontW: FontWeight.w600,
                                      color: TColor.white,
                                    ),
                                  ],
                                ),
                              ))
                          : SizedBox(),
                    ],
                  ),
                ),
                CustomText(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 25.0,
                    vertical: 10.0,
                  ),
                  text: AppStrings.students.tr(),
                  fontW: FontWeight.w700,
                  fontSize: 18.sp,
                ),
                TabBar(
                  controller: tabController,
                  indicatorColor: TColor.mainColor,
                  unselectedLabelColor: TColor.tabColors,
                  labelColor: TColor.mainColor,
                  indicatorWeight: 2,
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelPadding: EdgeInsets.only(bottom: 12.h),
                  tabs: [
                    Text(
                      AppStrings.onBus.tr(),
                      style: TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 22.sp),
                    ),
                    Text(
                      AppStrings.arrivedHome.tr(),
                      style: TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 22.sp),
                    ),
                    Text(
                      AppStrings.absentStudents.tr(),
                      style: TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 22.sp),
                    ),
                  ],
                ),
                20.verticalSpace,
                Expanded(
                  child: TabBarView(
                    physics: const BouncingScrollPhysics(),
                    clipBehavior: Clip.antiAlias,
                    controller: tabController,
                    children: const [
                      OnBusBodyEveningTrip(),
                      ArrivedHomeBodyEveningTrip(),
                      AbsentBodyEveningTrip(),
                    ],
                  ),
                ),
              ],
            );
          }
        },
      ),
      ),
    );
  }
}
