import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
import '../../../../config/theme_colors.dart';
import '../../../../translations/local_keys.g.dart';
import '../../../custom_widgets/build_table_row_widget.dart';
import '../../../custom_widgets/custom_text.dart';

class OnBusBodyMorningTrip extends StatelessWidget {
  const OnBusBodyMorningTrip({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MorningTripCubit, MorningTripState>(
      builder: (context, state) {
        if (state is OnBusMorningTripLoadingState ||
            state is StartMorningTripLoadingState) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is MorningTripInitial ||
            state is MorningTripStatusErrorState ||
            state is StartMorningTripErrorState ||
            state is EndMorningTripSuccessState ||
            state is OnBusMorningTripErrorState) {
          return SizedBox(
            width: 300.w,
            height: 300.w,
            child: Center(
              child: CustomText(
                text: AppStrings.tripClosedStartTrip.tr(),
                fontSize: 17,
                fontW: FontWeight.w600,
              ),
            ),
          );
        } else {
          if (MorningTripCubit.get()
                  .onBusMorningTripModel
                  ?.presentOnBus
                  ?.isEmpty ??
              true) {
            return SizedBox(
              width: 300.w,
              height: 300.w,
              child: Center(
                child: CustomText(
                  text: AppStrings.studentsNotFound.tr(),
                  fontSize: 17,
                  fontW: FontWeight.w600,
                ),
              ),
            );
          } else {
            int length = MorningTripCubit.get()
                .onBusMorningTripModel!
                .presentOnBus!
                .length;
            debugPrint("students on bus: $length");
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(1.5),
                    },
                    border: TableBorder.all(
                        color: TColor.tabColors,
                        borderRadius: BorderRadius.circular(15.0)),
                    children: [
                      BuildTableRowWidget(
                        cell: [
                          (AppStrings.name.tr()),
                          (AppStrings.moveToWaiting.tr()),
                        ],
                        header: true,
                      ).build(context),
                      ...List.generate(length, (index) {
                        final newStudent = MorningTripCubit.get()
                            .onBusMorningTripModel
                            ?.presentOnBus?[index];
                        return BuildTableRowWidget(
                            cell: [
                              // Custom widget for student name with location icon
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Flexible(
                                    child: Text(
                                      newStudent?.name ?? '--',
                                      style: TextStyle(
                                        color: TColor.tabColors,
                                        fontSize: 15,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (newStudent?.hasCurrentAddress == true) ...[
                                    const SizedBox(width: 4),
                                    Icon(
                                      Icons.location_on,
                                      size: 16,
                                      color: Colors.green,
                                    ),
                                  ],
                                ],
                              ),
                              Icons.compare_arrows,
                            ],
                            onTapLastCell: () async {
                              MorningTripCubit.get().removePresentOnBusMorningTrip(studentId: newStudent?.id);
                            }
                            ).build(context);
                      }),
                    ],
                  ),
                ),
              ),
            );
          }
        }
      },
    );
  }
}
