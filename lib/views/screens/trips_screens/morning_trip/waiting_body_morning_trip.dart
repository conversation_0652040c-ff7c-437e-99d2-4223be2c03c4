import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:logger/logger.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';
import '../../../../bloc/cubit/notifications_cubit/notifications_cubit.dart';
import '../../../../config/theme_colors.dart';
import '../../../../translations/local_keys.g.dart';
import '../../../../widgets/custom_container_dialog_w.dart';
import '../../../custom_widgets/build_table_row_widget.dart';
import '../../../custom_widgets/custom_text.dart';
import '../../student_address_screen/student_address_screen.dart';

class WaitingBodyMorningTrip extends StatelessWidget {
  const WaitingBodyMorningTrip({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MorningTripCubit, MorningTripState>(
      builder: (context, state) {
        if (state is WaitingMorningTripLoadingState ||
            state is StartMorningTripLoadingState ||
            state is StartMorningTripSuccessState) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is MorningTripInitial ||
            state is MorningTripStatusErrorState ||
            state is StartMorningTripErrorState ||
            state is EndMorningTripSuccessState ||
            state is WaitingMorningTripErrorState) {
          return SizedBox(
            width: 300.w,
            height: 300.w,
            child: Center(
              child: CustomText(
                text: AppStrings.tripClosedStartTrip.tr(),
                fontSize: 17,
                fontW: FontWeight.w600,
              ),
            ),
          );
        } else {
          if (MorningTripCubit.get()
                  .waitingMorningTripModel
                  ?.waiting
                  ?.isEmpty ??
              true) {
            return SizedBox(
              width: 300.w,
              height: 300.w,
              child: Center(
                child: CustomText(
                  text: AppStrings.studentsNotFound.tr(),
                  fontSize: 17,
                  fontW: FontWeight.w600,
                ),
              ),
            );
          } else {
            int length =
                MorningTripCubit.get().waitingMorningTripModel!.waiting!.length;
            debugPrint("students waiting: $length");
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FlexColumnWidth(2),
                      1: FlexColumnWidth(1),
                      2: FlexColumnWidth(1),
                      3: FlexColumnWidth(1),
                      4: FlexColumnWidth(1),
                    },
                    border: TableBorder.all(
                        color: TColor.tabColors,
                        borderRadius: BorderRadius.circular(15.0)),
                    children: [
                      BuildTableRowWidget(
                        cell: [
                          (AppStrings.name.tr()),
                          (AppStrings.address.tr()),
                          (AppStrings.onBus.tr()),
                          (AppStrings.absent.tr()),
                          (AppStrings.notification.tr()),
                        ],
                        header: true,
                      ).build(context),
                      ...List.generate(length, (index) {
                        final newStudent = MorningTripCubit.get()
                            .waitingMorningTripModel
                            ?.waiting?[index];
                        return BuildTableRowWidget(
                          isFirstIcon: true,
                          isSecondIcon: true,
                          is2Icon: true,
                          isTabDown: true,
                          cell: [
                            newStudent?.name,
                            Icons.location_on,
                            Icons.directions_bus,
                            Icons.highlight_remove_outlined,
                            Icons.notifications_active,
                          ],
                          color: newStudent!.myParents!.isEmpty
                              ? TColor.darkRed
                              : null,
                          onTapFirstCell: () async {
                            if (newStudent.latitude == null ||
                                newStudent.longitude == null) {
                              Navigator.of(context).pushNamed(
                                  StudentAddressScreen.routeName,
                                  arguments: [
                                    newStudent.latitude,
                                    newStudent.longitude
                                  ]);
                            } else {
                              final Uri url = Uri.parse(
                                  "google.navigation:q=${newStudent.latitude},${newStudent.longitude}&mode=d");
                              if (!await launchUrl(url)) {
                                throw Exception('Could not launch $url');
                              }
                            }
                          },
                          onTapSecondCell: () async {
                            MorningTripCubit.get()
                                .presentOnBusMorningTrip(
                              studentId: newStudent.id,
                            )
                                .whenComplete(() {
                              MorningTripCubit.get().getWaitingMorningTrip();
                              MorningTripCubit.get().getOnBusMorningTrip();
                            });
                          },
                          onTapBeforeLastCell: () async {
                            MorningTripCubit.get()
                                .studentAbsentMorningTrip(
                                    studentId: newStudent.id)
                                .whenComplete(() {
                              MorningTripCubit.get().getWaitingMorningTrip();
                              MorningTripCubit.get().getAbsentMorningTrip();
                            });
                          },
                          onTapDown: (TapDownDetails details) {
                            final RenderBox overlay = Overlay.of(context)
                                .context
                                .findRenderObject() as RenderBox;
                            final Offset offset =
                                overlay.localToGlobal(Offset.zero);
                            showMenu(
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(15.sp))),
                                context: context,
                                position: RelativeRect.fromLTRB(
                                  context.locale.toString() == "ar"
                                      ? offset.dx - 100
                                      : offset.dx + 100,
                                  details.globalPosition.dy + 15,
                                  0,
                                  0,
                                ),
                                items: (MorningTripCubit.get()
                                        .groupAndSingleMessageModel!
                                        .data!
                                        .single!
                                        .isNotEmpty)
                                    ? List.generate(
                                        MorningTripCubit.get()
                                            .groupAndSingleMessageModel!
                                            .data!
                                            .single!
                                            .length,
                                        (index) => PopupMenuItem(
                                          child: Center(
                                            child: CustomContainerDialogW(
                                              icons: Icons.notifications_active,
                                              name: MorningTripCubit.get()
                                                  .groupAndSingleMessageModel!
                                                  .data!
                                                  .single![index]
                                                  .body!,
                                              onTap: () {
                                                MorningTripCubit.get()
                                                    .sendMessageMorningTrip(
                                                  messageId: MorningTripCubit
                                                          .get()
                                                      .groupAndSingleMessageModel!
                                                      .data!
                                                      .single![index]
                                                      .id!,
                                                  studentId: newStudent.id,

                                                  // notificationsType: "tracking",
                                                );
                                                Navigator.pop(context);
                                                // MorningTripCubit.get()
                                                //     .sendMessageMorningTrip(
                                                //       // notificationsType: 'tracking',
                                                //   messageId: 8,
                                                //   studentId: newStudent.id,
                                                // );
                                                //     .whenComplete(() {

                                                //   if (NotificationsCubit.get(context)
                                                //           .parentFcmToken
                                                //           ?.data
                                                //           ?.isNotEmpty ==
                                                //       true) {
                                                //     // NotificationsRepo()
                                                //     //     .sendNotification(
                                                //     //   deviceTokens:
                                                //     //       NotificationsCubit.get(
                                                //     //               context)
                                                //     //           .parentFcmToken!
                                                //     //           .data!,
                                                //     //   title: 'صباح الخير',
                                                //     //   body:
                                                //     //       'الباص تحرك من المدرسة من أجل الطالب ${newStudent.name}',
                                                //     // );
                                                //   }
                                                // });
                                              },
                                            ),
                                          ),
                                        ),

                                        // PopupMenuItem(
                                        //   child: Center(
                                        //     child: CustomContainerDialogW(
                                        //       icons: Icons.notification_add,
                                        //       name: 'الباص بالقرب من المنزل',
                                        //       onTap: () {
                                        //                                                   Navigator.pop(context);

                                        //         MorningTripCubit.get()
                                        //             .sendMessageMorningTrip(
                                        //               // notificationsType: 'tracking',
                                        //           messageId: 11,
                                        //           studentId: newStudent.id,
                                        //         );
                                        //           //   .whenComplete(() {
                                        //           // if (NotificationsCubit.get(context)
                                        //           //         .parentFcmToken
                                        //           //         ?.data
                                        //           //         ?.isNotEmpty ==
                                        //           //     true) {
                                        //             // NotificationsRepo()
                                        //             //     .sendNotification(
                                        //             //   deviceTokens:
                                        //             //       NotificationsCubit.get(
                                        //             //               context)
                                        //             //           .parentFcmToken!
                                        //             //           .data!,
                                        //             //   title: 'صباح الخير',
                                        //             //   body:
                                        //             //       'الباص بالقرب من المنزل بانتظار الطالب ${newStudent.name}',
                                        //             // );
                                        //           // }
                                        //         // }

                                        //         // );
                                        //       },
                                        //     ),
                                        //   ),
                                        // ),
                                        // PopupMenuItem(
                                        //   child: Center(
                                        //     child: CustomContainerDialogW(
                                        //       icons: Icons.notification_important,
                                        //       name: 'الباص بانتظارك أمام المنزل',
                                        //       onTap: () {
                                        //           Navigator.pop(context);

                                        //         MorningTripCubit.get()
                                        //             .sendMessageMorningTrip(
                                        //               // notificationsType: 'no-tracking',

                                        //           messageId: 10,
                                        //           studentId: newStudent.id,
                                        //         );
                                        //         //     .whenComplete(() {
                                        //         //   if (NotificationsCubit.get(context)
                                        //         //           .parentFcmToken
                                        //         //           ?.data
                                        //         //           ?.isNotEmpty ==
                                        //         //       true) {
                                        //         //     NotificationsRepo()
                                        //         //         .sendNotification(
                                        //         //       deviceTokens:
                                        //         //           NotificationsCubit.get(
                                        //         //                   context)
                                        //         //               .parentFcmToken!
                                        //         //               .data!,
                                        //         //       title: 'صباح الخير',
                                        //         //       body:
                                        //         //           'الباص بانتظار الطالب ${newStudent.name} أمام المنزل',
                                        //         //     );
                                        //         //   }
                                        //         // }

                                        //         // );

                                        //       },
                                        //     ),
                                        //   ),
                                        // ),
                                      )
                                    : []);
                          },
                        ).build(context);
                      }),
                    ],
                  ),
                ),
              ),
            );
          }
        }
      },
    );
  }
}
