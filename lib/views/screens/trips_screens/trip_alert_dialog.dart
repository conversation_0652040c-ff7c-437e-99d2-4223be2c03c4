import 'package:bus_driver/bloc/cubit/notifications_cubit/notifications_cubit.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../config/theme_colors.dart';
import '../../../translations/local_keys.g.dart';
import '../../custom_widgets/custom_text.dart';

class TripAlertDialog extends StatefulWidget {
  const TripAlertDialog({
    super.key,
    this.title,
    this.titleColor = TColor.titleSub,
    this.content,
    required this.onTapYes,
    this.isEndTrip = false,
    this.isEveningTrip = false,
  });

  final String? title;
  final Color titleColor;
  final String? content;
  final void Function() onTapYes;
  final bool isEndTrip;
  final bool isEveningTrip;

  @override
  State<TripAlertDialog> createState() => _TripAlertDialogState();
}

class _TripAlertDialogState extends State<TripAlertDialog> {
  late NotificationsCubit? _notificationsCubitProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _notificationsCubitProvider =
        Provider.of<NotificationsCubit>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(15.sp),
        ),
      ),
      title: CustomText(
        text: widget.title,
        fontSize: 18,
        fontW: FontWeight.w600,
        color: widget.titleColor,
        textAlign: TextAlign.center,
      ),
      content: CustomText(
        text: widget.content,
        fontSize: 18,
        fontW: FontWeight.w600,
        color: TColor.black,
        textAlign: TextAlign.center,
      ),
      actions: <Widget>[
        Row(
          children: [
            10.horizontalSpace,
            Checkbox(
              activeColor: TColor.mainColor,
              value: isChecked,
              onChanged: (value) {
                setState(() {
                  isChecked = value!;
                  print(isChecked);
                });
              },
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            CustomText(
              text: AppStrings.sendNotification.tr(),
              color: TColor.black,
              fontW: FontWeight.w500,
              fontSize: 16,
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MaterialButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: CustomText(
                text: AppStrings.no.tr(),
                fontSize: 18,
                fontW: FontWeight.w600,
                color: TColor.black,
                textAlign: TextAlign.center,
              ),
            ),
            MaterialButton(
              onPressed: () {
                Navigator.of(context).pop();
                // if (isChecked && widget.isEndTrip) {
                //   widget.isEveningTrip
                //       ? EveningTripCubit.get(context)
                //           .sendMessageForAllEveningTrip(
                //           message: 'الباص وصل المدرسة',
                //           message_en: 'The bus arrived at school',
                //           notificationType: "no-tracking",
                //         )
                //         //   .whenComplete(() {
                //         //   final notificationRepo = NotificationsRepo();
                //         //   notificationRepo.allParentsFcmTokens().then((value) {
                //         //     if (value.data!.isNotEmpty == true) {
                //         //       NotificationsRepo().sendNotification(
                //         //         deviceTokens: value.data!,
                //         //         title: widget.isEveningTrip
                //         //             ? 'مساء الخير'
                //         //             : 'صباح الخير',
                //         //         body: 'الباص وصل المدرسة',
                //         //       );
                //         //     }
                //         //   });
                //         // })
                //       : MorningTripCubit.get()
                //           .sendMessageForAllMorningTrip(
                //           message: 'الباص وصل المدرسة',
                //           notificationType: "no-tracking",
                //           message_en: 'The bus arrived at school',
                //         );
                //         //   .whenComplete(() {
                //         //   if (_notificationsCubitProvider!
                //         //           .allParentsFcmTokens?.data?.isNotEmpty ==
                //         //       true) {
                //         //     final notificationRepo = NotificationsRepo();
                //         //     notificationRepo
                //         //         .allParentsFcmTokens()
                //         //         .then((value) {
                //         //       if (value.data!.isNotEmpty == true) {
                //         //         NotificationsRepo().sendNotification(
                //         //           deviceTokens: value.data!,
                //         //           title: widget.isEveningTrip
                //         //               ? 'مساء الخير'
                //         //               : 'صباح الخير',
                //         //           body: 'الباص وصل المدرسة',
                //         //         );
                //         //       }
                //         //     });
                //         //   }
                //         // });

                // }
                widget.onTapYes();
                // if (isChecked && !widget.isEndTrip) {
                //   widget.isEveningTrip
                //       ? EveningTripCubit.get(context)
                //           .sendMessageForAllEveningTrip(
                //           message: 'الباص تحرك من المدرسة وفي طريقه إليك',
                //           message_en:
                //               'The bus has left school and is on its way to you',
                //           notificationType: "tracking",
                //         )
                //         //   .whenComplete(() {
                //   final notificationRepo = NotificationsRepo();
                //   notificationRepo.allParentsFcmTokens().then((value) {
                //     if (value.data!.isNotEmpty == true) {
                //       NotificationsRepo().sendNotification(
                //         deviceTokens: value.data!,
                //         title: widget.isEveningTrip
                //             ? 'مساء الخير'
                //             : 'صباح الخير',
                //         body: 'الباص تحرك من المدرسة وفي طريقه إليك',
                //       );
                //     }
                //   });
                // })
                // : MorningTripCubit.get()
                //     .sendMessageForAllMorningTrip(
                //   message: 'الباص تحرك من المدرسة وفي طريقه إليك',
                //   message_en:
                //       'The bus has left the school and is on its way to you',
                //   notificationType: "tracking",
                // );
                //   .whenComplete(() {
                //   final notificationRepo = NotificationsRepo();
                //   notificationRepo.allParentsFcmTokens().then((value) {
                //     if (value.data!.isNotEmpty == true) {
                //       NotificationsRepo().sendNotification(
                //         deviceTokens: value.data!,
                //         title: widget.isEveningTrip
                //             ? 'مساء الخير'
                //             : 'صباح الخير',
                //         body: 'الباص تحرك من المدرسة وفي طريقه إليك',
                //       );
                //     }
                //   });
                // });
              },
              child: CustomText(
                text: AppStrings.yes.tr(),
                fontSize: 18,
                fontW: FontWeight.w600,
                color: widget.titleColor,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        )
      ],
    );
  }
}
