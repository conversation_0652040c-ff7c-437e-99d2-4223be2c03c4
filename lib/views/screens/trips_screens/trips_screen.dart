import 'package:bus_driver/bloc/cubit/evening_trip_cubit/evening_trip_cubit.dart';
import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:bus_driver/widgets/home_widgets/custom_container_r_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../bloc/cubit/morning_trip_cubit/morning_trip_cubit.dart';

class TripsScreen extends StatelessWidget {
  static const String routeName = PathRouteName.trips;

  const TripsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.trips.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          width: 1.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Sbox(h: 30),
              Wrap(
                spacing: 10.w,
                children: [
                  CustomContainerRW(
                    onTap: () {
                      MorningTripCubit.get().getMorningTripStatus();
                      MorningTripCubit.get().groupOrSingleMessage(type_day: 'start_day');

                      Navigator.pushNamed(context, PathRouteName.morningTripScreen);
                    },
                    imageName: "group1.png",
                    name: AppStrings.morningTrips.tr(),
                  ),
                  CustomContainerRW(
                    onTap: () {
                      EveningTripCubit.get(context).getEveningTripStatus();
                      MorningTripCubit.get().groupOrSingleMessage(type_day: 'end_day');

                      Navigator.pushNamed(
                          context, PathRouteName.eveningTripScreen);
                    },
                    imageName: "group1.png",
                    name: AppStrings.eveningTrips.tr(),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
