import 'dart:io';

import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/helper/network_serviecs.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_button.dart';
import 'package:bus_driver/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:bus_driver/widgets/custom_appbar.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';

class UpdateProfileScreen extends StatefulWidget {
  static const String routeName = PathRouteName.updateProfile;
  const UpdateProfileScreen({Key? key}) : super(key: key);

  @override
  State<UpdateProfileScreen> createState() => _UpdateProfileScreenState();
}

class _UpdateProfileScreenState extends State<UpdateProfileScreen> {
  File? image;
  String? name;
  String? email;
  String? imageUrl;
  String? address;
  String? phone;
  String? currentPassword;

  Future getImage() async {
    try {
      final images = await ImagePicker().pickImage(source: ImageSource.gallery);
      final imageFile = File(images!.path);
      setState(() {
        image = imageFile;
      });
    } on PlatformException catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.updateProfile.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: InkWell(
                onTap: () {
                  getImage();
                },
                child: image != null
                    ? CircleAvatar(
                        radius: 34.r,
                        backgroundColor: TColor.white,
                        child: CircleAvatar(
                          backgroundImage: FileImage(image!),
                        ),
                      )
                    : Stack(
                        children: [
                          CircleAvatar(
                            radius: 35.r,
                            backgroundColor: TColor.borderContainer,
                            child: CircleAvatar(
                              radius: 34.r,
                              backgroundColor: TColor.white,
                              child: Image.asset(
                                assetsImages("pc.png"),
                                width: 25.w,
                                height: 25.w,
                              ),
                            ),
                          ),
                          context.locale.toString() == "ar"
                              ? Positioned(
                                  left: 0,
                                  bottom: 5.w,
                                  child: Container(
                                    width: 20.w,
                                    height: 20.w,
                                    decoration: const BoxDecoration(
                                        color: TColor.borderContainer,
                                        shape: BoxShape.circle),
                                    child: Icon(
                                      Icons.folder_copy,
                                      color: TColor.white,
                                      size: 10.sp,
                                    ),
                                  ),
                                )
                              : Positioned(
                                  right: 0,
                                  bottom: 5.w,
                                  child: Container(
                                    width: 20.w,
                                    height: 20.w,
                                    decoration: const BoxDecoration(
                                        color: TColor.borderContainer,
                                        shape: BoxShape.circle),
                                    child: Icon(
                                      Icons.folder_copy,
                                      color: TColor.white,
                                      size: 10.sp,
                                    ),
                                  ),
                                )
                        ],
                      ),
              ),
            ),
            const Sbox(h: 40),
            CustomFormFieldWithBorder(
              onChanged: (c0) {
                name = c0;
              },
              formFieldWidth: 428,
              heightA: 53,
              hintText: AppStrings.name.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
            ),
            const Sbox(h: 15),
            CustomFormFieldWithBorder(
              onChanged: (p0) {
                email = p0;
              },
              formFieldWidth: 428,
              hintText: AppStrings.email.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              heightA: 53,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
            ),
            const Sbox(h: 15),
            CustomFormFieldWithBorder(
              onChanged: (p0) {
                currentPassword = p0;
              },
              formFieldWidth: 428,
              // hintText: LocaleKeys.currentpassword.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              heightA: 53,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
            ),
            const Sbox(h: 15),
            CustomFormFieldWithBorder(
              onChanged: (p0) {
                phone = p0;
              },
              formFieldWidth: 428,
              hintText: AppStrings.phoneNumber.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              heightA: 53,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
            ),
            const Sbox(h: 15),
            CustomFormFieldWithBorder(
              onChanged: (p0) {
                address = p0;
              },
              formFieldWidth: 428,
              heightA: 53,
              // hintText: LocaleKeys.addAddress.tr(),
              borderColor: TColor.fillFormFieldB,
              fillColor: TColor.fillFormFieldB,
              radiusNumber: 15.0,
              paddingRight: 37.w,
              paddingLeft: 37.w,
              contentPaddingVertical: 15,
              contentPaddingHorizontal: 15,
            ),
            const Sbox(h: 50),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.w),
              child: CustomButton(
                text: AppStrings.updateProfile.tr(),
                onTap: () async {
                  var file__lol;
                  try {
                    String fileName = image!.path.split('/').last;
                    var file__lol = await MultipartFile.fromFile(image!.path,
                        filename: fileName);
                  } catch (e) {}
                  Map<String, dynamic> datamap = {
                    "email": email,
                    "current_password": currentPassword,
                    "name": name,
                    "phone": phone,
                    "address": address,
                  };
                  Response respo = await NetworkService().post(
                      url: ConfigBase.updateProfile,
                      body: datamap,
                      isAuth: true);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      backgroundColor: respo.statusCode == 200
                          ? TColor.greenSuccess
                          : TColor.redAccent,
                      content: CustomText(
                        text: respo.statusCode == 200
                            ? "Absence Added succsesfully"
                            : "Failed to add Absence Because of ${respo.data["messages"] ?? respo.data["message"]}",
                        fontSize: 18,
                        maxLine: 5,
                        color: TColor.white,
                      ),
                    ),
                  );
                  if (respo.statusCode == 200) {
                    Navigator.pop(context);
                  }
                },
                width: 428,
                height: 53,
                radius: 15,
                borderColor: TColor.mainColor,
                bgColor: TColor.mainColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
