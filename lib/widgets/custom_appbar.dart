import 'package:bus_driver/config/theme_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:svg_path_parser/svg_path_parser.dart';
import 'package:touchable/touchable.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    Key? key,
    this.leftWidget = const SizedBox(),
    this.rightWidget = const SizedBox(),
    this.titleWidget = const SizedBox(),
  }) : super(key: key);

  @override
  Size get preferredSize => const Size.fromHeight(100.0);

  final Widget leftWidget;
  final Widget rightWidget;
  final Widget titleWidget;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(100.0),
      child: CustomPaint(
        painter: CustomAppBarPainter(
          context: context,
          appBarPath: context.locale.toString() == "ar"
              ? appBarPathRight
              : appBarPathLeft,
          flex: 0,
          color: TColor.mainColor,
        ),
        child: SizedBox(
          height: MediaQuery.of(context).size.height,
          child: Center(
            child: Padding(
              padding:  EdgeInsets.symmetric(horizontal: 33.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  leftWidget,
                  titleWidget,
                  rightWidget,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class CustomAppBarPainter extends CustomPainter {
  final BuildContext context;
  AppBarPath? appBarPath;
  int? flex;
  Color? color;

  CustomAppBarPainter({
    required this.context,
    this.appBarPath,
    this.flex,
    this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    var myCanvas = TouchyCanvas(context, canvas);
    Paint paint = Paint()
      ..color = color ?? TColor.mainColor
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;

    var xScale = size.width / 428;
    var yScale = size.height / (flex! + size.height / 0.75);
    final Matrix4 matrix4 = Matrix4.identity();
    matrix4.scale(xScale, yScale);

    Path path = parseSvgPath(appBarPath!.path);
    paint.color = color ?? TColor.primary;
    path.transform(matrix4.storage);
    myCanvas.drawPath(path.transform(matrix4.storage), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class AppBarPath {
  String path;

  AppBarPath({required this.path});
}

String pathRight =
    'M0.038269 0H428.038L428.038 96.2798C428.038 96.2798 425.729 106.168 418.821 112.849C411.913 119.531 400.406 123.005 400.406 123.005L27.6164 123.078C27.6164 123.078 18.1252 123.444 10.3671 130.481C2.60889 137.518 0 149.945 0 149.945L0.038269 0Z';
String pathLeft =
    'M427.962 0H5.60496e-07L0.000610857 96.3149C0.000610857 96.3149 2.30857 106.207 9.21597 112.891C16.1234 119.574 27.6302 123.05 27.6302 123.05L400.386 123.123C400.386 123.123 409.876 123.489 417.634 130.529C425.391 137.569 428 150 428 150L427.962 0Z';
AppBarPath appBarPathRight = AppBarPath(path: pathRight);
AppBarPath appBarPathLeft = AppBarPath(path: pathLeft);
