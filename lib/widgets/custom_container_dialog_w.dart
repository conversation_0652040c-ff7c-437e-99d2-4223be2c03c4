import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../config/theme_colors.dart';
import '../utils/sized_box.dart';
import '../views/custom_widgets/custom_text.dart';

class CustomContainerDialogW extends StatelessWidget {
  IconData? icons;
  String? name;
  Function()? onTap;
  CustomContainerDialogW({
    Key? key,
    this.name,
    this.icons,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 55.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: TColor.fillFormFieldB,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal:0.w,vertical: 5),
          child: Row(
            children: [
              Icon(icons, color: TColor.mainColor,),
              const Sbox(w: 10),
              Expanded(
                child: CustomText(
                  text: name,
                  maxLine: 2,
                  fontW: FontWeight.w500,
                  fontSize: 16,
                  color: TColor.mainColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
