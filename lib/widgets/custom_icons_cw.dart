import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../config/theme_colors.dart';
import '../utils/assets_utils.dart';

class CustomIconsCW extends StatelessWidget {
  final String? type;
  const CustomIconsCW({Key? key, this.type,}) : super(key: key);

  _showDialogStudent(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  // TODO:
                  // child: Column(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     CustomContainerDialogW(
                  //       icons: Icons.groups_outlined,
                  //       name: LocaleKeys.addByHand.tr(),
                  //       onTap: () {
                  //         // Navigator.pushNamed(context, AddStudentScreen.routeName);
                  //       },
                  //     ),
                  //     const Sbox(),
                  //     CustomContainerDialogW(
                  //       icons: Icons.file_upload_outlined,
                  //       name: LocaleKeys.addByFile.tr(),
                  //       onTap: () {
                  //         // Navigator.pushNamed(context, AddStudentFileScreen.routeName);
                  //       },
                  //     ),
                  //   ],
                  // ),
                );
              },
            ),
          );
        });
  }

  _showDialogDriver(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  // TODO:
                  // child: Column(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     CustomContainerDialogW(
                  //       icons: Icons.groups_outlined,
                  //       name: LocaleKeys.addDriver.tr(),
                  //       onTap: () {
                  //         // Navigator.pushNamed(context, AddDriverScreen.routeName);
                  //       },
                  //     ),
                  //     // const Sbox(),
                  //     // CustomContainerDialogW(
                  //     //   icons: Icons.file_upload_outlined,
                  //     //   name: LocaleKeys.addByFile.tr(),
                  //     //   onTap: () {
                  //     //     Navigator.pushNamed(context, AddStudentFileScreen.routeName);
                  //     //   },
                  //     // ),
                  //   ],
                  // ),
                );
              },
            ),
          );
        });
  }

  _showDialogSupervisor(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  // TODO:
                  // child: Column(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     CustomContainerDialogW(
                  //       icons: Icons.groups_outlined,
                  //       name: LocaleKeys.addSupervisor.tr(),
                  //       onTap: () {
                  //         // Navigator.pushNamed(context, AddSupervisorScreen.routeName);
                  //       },
                  //     ),
                  //     // const Sbox(),
                  //     // CustomContainerDialogW(
                  //     //   icons: Icons.file_upload_outlined,
                  //     //   name: LocaleKeys.addByFile.tr(),
                  //     //   onTap: () {
                  //     //     Navigator.pushNamed(context, AddStudentFileScreen.routeName);
                  //     //   },
                  //     // ),
                  //   ],
                  // ),
                );
              },
            ),
          );
        });
  }

  _showDialogBus(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  // TODO:
                  // child: Column(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     CustomContainerDialogW(
                  //       icons: Icons.groups_outlined,
                  //       name: LocaleKeys.addBus.tr(),
                  //       onTap: () {
                  //         // Navigator.pushNamed(context, AddBusScreen.routeName);
                  //       },
                  //     ),
                  //   ],
                  // ),
                );
              },
            ),
          );
        });
  }

  _showDialogParint(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            insetPadding: context.locale.toString() == "ar"
                ? EdgeInsets.only(bottom: 350.w, left: 110.w)
                : EdgeInsets.only(bottom: 350.w, right: 110.w),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r))),
            content: Builder(
              builder: (context) {
                return Container(
                  width: 100.w,
                  height: 132.w,
                  decoration: BoxDecoration(
                    color: TColor.white,
                    border: Border.all(color: TColor.tabColors, width: 1),
                    borderRadius: BorderRadius.circular(8.0.r),
                  ),
                  // TODO:
                  // child: Column(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     CustomContainerDialogW(
                  //       icons: Icons.groups_outlined,
                  //       name: LocaleKeys.showParint.tr(),
                  //       onTap: () {
                  //         Navigator.pushNamed(context, ParintDataScreen.routeName);
                  //       },
                  //     ),
                  //   ],
                  // ),
                );
              },
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if(type == "student") {
          _showDialogStudent(context);
        } else if(type == "drive") {
          _showDialogDriver(context);
        } else if(type == "supervisor") {
          _showDialogSupervisor(context);
        } else if(type == "Bus") {
          _showDialogBus(context);
        } else if(type == "Parint") {
          _showDialogParint(context);
        }
      },
      child: SizedBox(
        height: 55.w,
        child: Stack(
          children: [
            Container(
              width: 47.w,
              height: 46.21.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: TColor.borderContainer,
              ),
            ),
            Positioned(
              right: 5,
              bottom: -2,
              child: SvgPicture.asset(
                assetsImages("Frame.svg"),
                color: TColor.white,
                width: 50.w,
                height: 50.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
