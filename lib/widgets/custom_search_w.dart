import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../config/theme_colors.dart';
import '../views/custom_widgets/custom_form_field_border.dart';

class CustomSearchW extends StatelessWidget {
  final String hintText;
  final String? type;
  const CustomSearchW({
    Key? key,
    required this.hintText,
    this.type,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomFormFieldWithBorder(
            paddingLeft: 0,
            paddingRight: 0,
            formFieldWidth: MediaQuery.of(context).size.width - 30.0.w,
            height: true,
            hintText: hintText,
            fillColor: TColor.fillFormFieldB,
            borderColor: TColor.fillFormFieldB,
            suffix: InkWell(
              onTap: () {},
              child: const Icon(Icons.search),
            ),
          )
        ],
      ),
    );
  }
}
