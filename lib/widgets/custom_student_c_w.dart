import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class CustomSonCW extends StatelessWidget {
  String? name;
  CustomSonCW({Key? key, this.name,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 428.w,
      height: 43.w,
      decoration: BoxDecoration(
        border: Border.all(color: TColor.dialogName, width: 1.w),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Padding(
        padding: EdgeInsets.only(left : 15.w, top: 12.w),
        child: CustomText(
          text: name,
          fontSize: 13,
          fontW: FontWeight.w400,
          color: TColor.namePersonal,
        ),
      ),
    );
  }
}
