import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomContainerRW extends StatelessWidget {
  final String? imageName;
  final String? name;
  final Function()? onTap;
  const CustomContainerRW({
    Key? key,
    this.imageName,
    this.onTap,
    this.name,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 179.w,
        height: 114.w,
        decoration: BoxDecoration(
          color: TColor.mainColor,
          borderRadius: BorderRadius.circular(14.r),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              assetsImages(imageName!),
              width: 56.w,
              height: 56.w,
            ),
            const Sbox(h: 10),
            CustomText(
              text: name,
              fontW: FontWeight.w400,
              fontSize: 15,
              color: TColor.white,
            ),
          ],
        ),
      ),
    );
  }
}
