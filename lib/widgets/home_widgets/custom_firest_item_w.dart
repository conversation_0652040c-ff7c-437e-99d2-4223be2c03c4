import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomFirstListItemW extends StatelessWidget {
  final Function()? onTap;
  const CustomFirstListItemW({Key? key, this.onTap,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(13.w),
        child: InkWell(
          onTap: onTap,
          child: Container(
            width: 373.w,
            height: 115.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              image: const DecorationImage(
                image: AssetImage("assets/images/trips.png"),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  assetsImages("group1.png"),
                  width: 56.w,
                  height: 56.w,
                ),
                const Sbox(h: 10),
                CustomText(
                  
                  text: AppStrings.trips.tr(),
                  fontW: FontWeight.w400,
                  fontSize: 15,
                  color: TColor.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
