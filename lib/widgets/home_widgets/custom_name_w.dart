import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../bloc/cubit/notifications_cubit/notifications_cubit.dart';
import '../../translations/local_keys.g.dart';
import '../../views/screens/notifications_screen/notifications_screen.dart';

class CustomNameW extends StatelessWidget {
  const CustomNameW({Key? key, required this.name1}) : super(key: key);
  final String name1;

  @override
  Widget build(BuildContext context) {
    int hour = DateTime.now().hour;
    return Padding(
      padding: EdgeInsets.only(top: 10.w, right: 20.w, left: 20.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: hour >= 5 && hour < 12
                    ? AppStrings.goodMorning.tr()
                    : AppStrings.goodEvening.tr(),
                color: TColor.white,
                fontW: FontWeight.w600,
                fontSize: 18,
              ),
              CustomText(
                text: name1,
                color: TColor.white,
                fontW: FontWeight.w400,
                fontSize: 14,
              ),
            ],
          ),
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, NotificationsScreen.routeName);
              NotificationsCubit.get(context)
                  .getNotifications(page: 1, isFirst: true);
            },
            child: const Icon(
              Icons.notifications_none,
              color: TColor.white,
            ),
          ),
        ],
      ),
    );
  }
}
