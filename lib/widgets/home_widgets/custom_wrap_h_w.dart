import 'package:bus_driver/constant/path_route_name.dart';
import 'package:bus_driver/translations/local_keys.g.dart';
import 'package:bus_driver/widgets/home_widgets/custom_container_r_w.dart';
import 'package:bus_driver/widgets/home_widgets/custom_firest_item_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../bloc/cubit/my_bus_cubit/my_bus_cubit.dart';

class CustomWrapHW extends StatelessWidget {
  const CustomWrapHW({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 10.w,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 10.w),
          child: CustomFirstListItemW(
            onTap: () {
               Navigator.pushNamed(context, PathRouteName.trips);
            },
          ),
        ),
        CustomContainerRW(
          onTap: () {
            Navigator.pushNamed(context, PathRouteName.setting);
          },
          imageName: "setting.png",
          name: AppStrings.setting.tr(),
        ),
        CustomContainerRW(
          onTap: () {
            BlocProvider.of<MyBusCubit>(context).getMyBus();
            Navigator.pushNamed(context, PathRouteName.busData);
          },
          imageName: "bus-dr.png",
          name: AppStrings.myBus.tr(),
        ),
      ],
    );
  }
}
