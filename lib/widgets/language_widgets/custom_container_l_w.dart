import 'package:bus_driver/config/theme_colors.dart';
import 'package:bus_driver/utils/assets_utils.dart';
import 'package:bus_driver/utils/sized_box.dart';
import 'package:bus_driver/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class CustomContainerLW extends StatelessWidget {
  final String? name;
  final String? url;
  final Function()? onTap;
  final Widget? icons;
  const CustomContainerLW({
    Key? key,
    this.name,
    this.url,
    this.onTap,
    this.icons,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    print(context.locale.toString());
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: 1.sw,
          height: 45.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            border: Border.all(color: TColor.dialogName, width: 1.w),
          ),
          child: Row(
            children: [
              Row(
                children: [
                  const Sbox(w: 15),
                  Image.asset(
                    assetsImages(url!),
                    width: 35.w,
                    height: 35.w,
                  ),
                  const Sbox(w: 15),
                  CustomText(
                    text: name,
                    fontW: FontWeight.w500,
                    fontSize: 17,
                  ),
                ],
              ),
              const Spacer(),
              icons!,
              const Sbox(w: 15),
            ],
          ),
        ),
      ),
    );
  }
}
