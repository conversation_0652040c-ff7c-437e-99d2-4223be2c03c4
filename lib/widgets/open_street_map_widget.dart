import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:bus_driver/config/config_base.dart';
import 'package:bus_driver/config/global_variable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:location/location.dart' as locationLib;
import 'package:socket_io_client/socket_io_client.dart' as io;

import '../config/theme_colors.dart';
import '../constant/path_route_name.dart';
import '../translations/local_keys.g.dart';
import '../utils/assets_utils.dart';
import '../views/custom_widgets/custom_text.dart';
import 'custom_appbar.dart';

class OpenStreetMapWidget extends StatefulWidget {
  static const String routeName = PathRouteName.openStreetMap;
  const OpenStreetMapWidget({Key? key}) : super(key: key);

  @override
  State<OpenStreetMapWidget> createState() => _OpenStreetMapWidgetState();
}

class _OpenStreetMapWidgetState extends State<OpenStreetMapWidget> {
  // init the position using the user location
  final mapController = MapController.withUserPosition(
    trackUserLocation: const UserTrackingOption(
      enableTracking: true,
      unFollowUser: false,
    ),
  );

  // To store the current location data
  GeoPoint? currentLocation;
  GeoPoint? previousLocation;
  StreamSubscription<locationLib.LocationData>? _positionStreamSubscription;

  final io.Socket socket = io.io(ConfigBase.socketUrl, <String, dynamic>{
    'query': {
      'token': socketToken,
    },
    'transports': ['websocket'],
  });

  @override
  void initState() {
    super.initState();
    startLocationTracking();
    socket.connect();
    receiveLocationFromSocket();
  }

  @override
  void dispose() {
    stopLocationTracking();
    socket.disconnect();
    socket.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.busLocation.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 20.w,
                  height: 20.w,
                ),
              ),
      ),
      body: OSMFlutter(
        controller: mapController,
        mapIsLoading: const SizedBox(
          height: 10,
          width: 10,
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        osmOption: OSMOption(
          enableRotationByGesture: true,
          showZoomController: true,
          zoomOption: const ZoomOption(
            initZoom: 15,
            minZoomLevel: 3,
            maxZoomLevel: 19,
            stepZoom: 1.0,
          ),
          userLocationMarker: UserLocationMaker(
            personMarker: const MarkerIcon(
              icon: Icon(
                Icons.circle,
                color: Colors.blueAccent,
                size: 60,
              ),
            ),
            directionArrowMarker: const MarkerIcon(
              icon: Icon(
                Icons.circle,
                color: Colors.blueAccent,
                size: 60,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void startLocationTracking() {
    print("startLocationTracking method starts");
    locationLib.Location location = locationLib.Location();

    // Configure for significant changes only
    location.changeSettings(
      accuracy: locationLib.LocationAccuracy.balanced,
      interval: 10000, // 10 seconds minimum interval
      distanceFilter: 10, // Only update if moved 100 meters
    );

    _positionStreamSubscription = location.onLocationChanged.listen(
      (locationLib.LocationData locationData) async {
        if (mounted) {
          setState(() {
            // Save the current location data
            currentLocation = GeoPoint(
              latitude: locationData.latitude!,
              longitude: locationData.longitude!,
            );
          });
          print("Location Updated");
          // Only send location if significant change occurred
          // if (_isSignificantChange(currentLocation!, previousLocation)) {
          sendLocationToSocket();
          previousLocation = currentLocation;
          // }
          await mapController.goToLocation(currentLocation!);
        }
      },
    );
    print("startLocationTracking method ends");
  }

  void stopLocationTracking() {
    _positionStreamSubscription?.cancel();
  }

  void sendLocationToSocket() {
    print("sendLocationToSocket method starts");
    if (currentLocation != null) {
      print("currentLocation: $currentLocation");

      final locationJson = jsonEncode({
        'latitude': currentLocation!.latitude.toString(),
        'longitude': currentLocation!.longitude.toString(),
        'type': type,
        'bus_id': busId,
        "trip_id": tripId,
      });

      final String? sendEvent = type;
      debugPrint("================3=================================");
      debugPrint(" sendEvent: $sendEvent,");
      debugPrint(" type: $type,");
      debugPrint("==================3===============================");

      if (sendEvent == null) {
        throw ArgumentError("sendEvent (type) is null");
      }

      try {
        if (!socket.connected) {
          socket.onConnect((_) {
            print('sendLocationToSocket: Connected to WebSocket');
            socket.emit(sendEvent, [busId, locationJson]);
          });

          socket.onConnectError((error) {
            print('sendLocationToSocket: Connection Error - $error');
            socket.connect();
            _showConnectionErrorSnackBar(error);
          });

          socket.on('connect_timeout', (_) {
            print('sendLocationToSocket: Connection Timeout');
            socket.connect(); // Retry the connection if timeout
          });
        } else {
          socket.emit(sendEvent, [busId, locationJson]);
        }

        socket.onReconnect((data) {
          print('sendLocationToSocket: On Reconnect..... data: $data');
        });

        socket.onDisconnect((reason) {
          print('sendLocationToSocket: Disconnected from WebSocket - $reason');
        });
      } catch (e, stackTrace) {
        print(stackTrace);
        print('sendLocationToSocket: Error - $e');
      }
    }
    print("sendLocationToSocket method ends");
  }

  void _showConnectionErrorSnackBar(dynamic error) {
    SnackBar snackBar = SnackBar(
      backgroundColor: TColor.redAccent,
      content: CustomText(
        text: "Connection Error - $error",
        color: TColor.white,
        maxLine: 3,
      ),
    );
    snackBarKey.currentState?.showSnackBar(snackBar);
  }

  void receiveLocationFromSocket() {
    print("receiveLocationFromSocket method starts");

    final receiveEvent = busId;
    socket.onConnect((_) {
      print('receiveLocationFromSocket: Connected to WebSocket');
    });

    socket.onDisconnect((reason) {
      print('receiveLocationFromSocket: Disconnected from WebSocket - $reason');
    });

    socket.on('$receiveEvent', (data) async {
      print('>>>>>>>>>>> receiveBus Event');
      final locationJson = jsonDecode(data);
      final latitude = double.parse(locationJson['latitude']);
      final longitude = double.parse(locationJson['longitude']);
      final currentLocation =
          GeoPoint(latitude: latitude, longitude: longitude);
      final type = locationJson['type'];
      final busId = locationJson['bus_id'];

      print('******************');
      print('Latitude: $latitude');
      print('Longitude: $longitude');
      print('currentLocation: $currentLocation');
      print('previousLocation: $previousLocation');
      print('type: $type');
      print('bus id: $busId');
      print('******************');

      // Remove the previous marker before adding a new one
      if (previousLocation != null) {
        mapController.removeMarker(previousLocation!);
      }

      // Update the map with the new location
      await mapController.addMarker(
        currentLocation,
        markerIcon: const MarkerIcon(
          icon: Icon(
            Icons.directions_bus_rounded,
            color: TColor.mainColor,
            size: 35,
          ),
        ),
      );

      // Update the previous location
      previousLocation = currentLocation;
    });
    print("receiveLocationFromSocket method ends");
  }

  // Helper method to determine if location change is significant
  // bool _isSignificantChange(GeoPoint current, GeoPoint? previous) {
  // if (previous == null) return true;

  // Calculate distance between points (rough approximation)
  // final double distance = _calculateDistance(
  // current.latitude,
  // current.longitude,
  // previous.latitude,
  // previous.longitude,
  // );

  // Consider it significant if moved more than 100 meters
  // return distance > 100;
  // }

  // Simple distance calculation using Haversine formula
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371000; // meters
    final double dLat = _toRadians(lat2 - lat1);
    final double dLon = _toRadians(lon2 - lon1);

    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(lat1)) *
            math.cos(_toRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _toRadians(double degree) {
    return degree * math.pi / 180;
  }
}
