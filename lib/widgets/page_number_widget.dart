import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../bloc/cubit/notifications_cubit/notifications_cubit.dart';
import '../config/theme_colors.dart';
import '../utils/assets_utils.dart';

class PageNumberWidget extends StatefulWidget {
  final void Function()? onTapNext;
  final void Function()? onTapPrevious;
  final int? lastPage;
  final int? currentPage;

  const PageNumberWidget({
    Key? key,
    this.onTapNext,
    this.onTapPrevious,
    this.lastPage,
    this.currentPage,
  }) : super(key: key);

  @override
  State<PageNumberWidget> createState() => _PageNumberWidgetState();
}

class _PageNumberWidgetState extends State<PageNumberWidget> {
  int selectIndex = 0;

  changeSelectIndex(int index) {
    setState(() {
      selectIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      margin: const EdgeInsets.symmetric(horizontal: 18.0),
      // width: double.infinity,
      height: 50.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: TColor.white,
        borderRadius: BorderRadius.circular(5.0),
        boxShadow: [
          BoxShadow(
            color: TColor.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 1,
            offset: const Offset(0, 1.5), // changes position of shadow
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: widget.onTapNext,
            child: SvgPicture.asset(
              AppAssets.forwardArrow,
              colorFilter:
                  const ColorFilter.mode(TColor.tabColors, BlendMode.srcIn),
              width: 15,
              height: 15,
            ),
          ),
          5.horizontalSpace,
          Expanded(
            child: ListView(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
              primary: false,
              controller: ScrollController(initialScrollOffset: widget.currentPage!.toDouble()),
              children: List.generate(
                widget.lastPage!,
                (index) => InkWell(
                  onTap: () {
                    changeSelectIndex(index);
                    if (widget.currentPage != index + 1){
                      NotificationsCubit.get(context).getNotifications(page: 1,  isFirst: true);
                    }
                  },
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 5.0,
                          vertical: 3.0,
                        ),
                        decoration: BoxDecoration(
                          color: selectIndex == index
                              ? TColor.mainColor
                              : TColor.white,
                          borderRadius: BorderRadius.circular(5.0),
                        ),
                        child: Text(
                          '${index + 1 == 8 || index + 1 == 9 || index + 1 == 10 || index + 1 == 11 ? '*' : index + 1}',
                          style: TextStyle(
                            color: selectIndex == index
                                ? TColor.white
                                : TColor.tabColors,
                          ),
                        ),
                      ),
                      5.horizontalSpace,
                    ],
                  ),
                ),
              ),
          )
          ),
          InkWell(
            onTap: widget.onTapPrevious,
            child: SvgPicture.asset(
              AppAssets.arrowBack,
              colorFilter:
                  const ColorFilter.mode(TColor.tabColors, BlendMode.srcIn),
              width: 15,
              height: 15,
            ),
          ),
        ],
      ),
    );
  }
}
