import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bus_driver/data/models/app_update_status_model.dart';
import 'package:bus_driver/services/app_update_service.dart';
import 'package:bus_driver/widgets/maintenance_screen.dart';

void main() {
  group('AppUpdateStatusModel Tests', () {
    test('should create model from JSON - app available', () {
      // Arrange
      final json = {
        'success': true,
        'name': 'test-app',
        'is_updating': false,
        'message': 'App is available'
      };

      // Act
      final model = AppUpdateStatusModel.fromJson(json);

      // Assert
      expect(model.success, true);
      expect(model.name, 'test-app');
      expect(model.isUpdating, false);
      expect(model.message, 'App is available');
    });

    test('should create model from JSON - app under maintenance', () {
      // Arrange
      final json = {
        'success': true,
        'name': 'test-app',
        'is_updating': true,
        'message': 'App is under maintenance'
      };

      // Act
      final model = AppUpdateStatusModel.fromJson(json);

      // Assert
      expect(model.success, true);
      expect(model.name, 'test-app');
      expect(model.isUpdating, true);
      expect(model.message, 'App is under maintenance');
    });

    test('should create model from JSON - error response', () {
      // Arrange
      final json = {'success': false, 'message': 'Invalid or missing app name'};

      // Act
      final model = AppUpdateStatusModel.fromJson(json);

      // Assert
      expect(model.success, false);
      expect(model.name, null);
      expect(model.isUpdating, null);
      expect(model.message, 'Invalid or missing app name');
    });

    test('should convert model to JSON', () {
      // Arrange
      final model = AppUpdateStatusModel(
        success: true,
        name: 'test-app',
        isUpdating: false,
        message: 'App is available',
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['success'], true);
      expect(json['name'], 'test-app');
      expect(json['is_updating'], false);
      expect(json['message'], 'App is available');
    });

    test('should support copyWith method', () {
      // Arrange
      final original = AppUpdateStatusModel(
        success: true,
        name: 'test-app',
        isUpdating: false,
        message: 'App is available',
      );

      // Act
      final updated = original.copyWith(
        isUpdating: true,
        message: 'App is under maintenance',
      );

      // Assert
      expect(updated.success, true);
      expect(updated.name, 'test-app');
      expect(updated.isUpdating, true);
      expect(updated.message, 'App is under maintenance');
    });

    test('should support equality comparison', () {
      // Arrange
      final model1 = AppUpdateStatusModel(
        success: true,
        name: 'test-app',
        isUpdating: false,
        message: 'App is available',
      );

      final model2 = AppUpdateStatusModel(
        success: true,
        name: 'test-app',
        isUpdating: false,
        message: 'App is available',
      );

      final model3 = AppUpdateStatusModel(
        success: true,
        name: 'test-app',
        isUpdating: true,
        message: 'App is under maintenance',
      );

      // Assert
      expect(model1 == model2, true);
      expect(model1 == model3, false);
    });
  });

  group('AppType Tests', () {
    test('should have correct app types', () {
      expect(AppType.attendants.toString(), 'AppType.attendants');
      expect(AppType.parents.toString(), 'AppType.parents');
      expect(AppType.schools.toString(), 'AppType.schools');
    });
  });

  group('AppUpdateResult Tests', () {
    test('should create result correctly', () {
      // Arrange & Act
      final result = AppUpdateResult(
        isSuccess: true,
        isUpdating: false,
        message: 'App is available',
        appName: 'test-app',
      );

      // Assert
      expect(result.isSuccess, true);
      expect(result.isUpdating, false);
      expect(result.message, 'App is available');
      expect(result.appName, 'test-app');
    });

    test('should have correct toString representation', () {
      // Arrange
      final result = AppUpdateResult(
        isSuccess: true,
        isUpdating: false,
        message: 'App is available',
        appName: 'test-app',
      );

      // Act
      final stringRepresentation = result.toString();

      // Assert
      expect(stringRepresentation, contains('isSuccess: true'));
      expect(stringRepresentation, contains('isUpdating: false'));
      expect(stringRepresentation, contains('message: App is available'));
      expect(stringRepresentation, contains('appName: test-app'));
    });
  });

  group('MaintenanceScreen Widget Tests', () {
    testWidgets('should display maintenance screen with message',
        (WidgetTester tester) async {
      // Arrange
      const testMessage = 'التطبيق قيد التحديث';
      const testAppName = 'test-app';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MaintenanceScreen(
            message: testMessage,
            appName: testAppName,
          ),
        ),
      );

      // Assert
      expect(find.text('تحت الصيانة'), findsOneWidget);
      expect(find.text(testAppName), findsOneWidget);
      expect(find.text(testMessage), findsOneWidget);
      expect(find.byIcon(Icons.build_rounded), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display default message when message is empty',
        (WidgetTester tester) async {
      // Arrange
      const testAppName = 'test-app';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MaintenanceScreen(
            message: '',
            appName: testAppName,
          ),
        ),
      );

      // Assert
      expect(
          find.text(
              'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.'),
          findsOneWidget);
    });

    testWidgets('should show retry button when onRetry is provided',
        (WidgetTester tester) async {
      // Arrange
      bool retryPressed = false;
      void onRetry() {
        retryPressed = true;
      }

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MaintenanceScreen(
            message: 'Test message',
            appName: 'test-app',
            onRetry: onRetry,
          ),
        ),
      );

      // Assert
      expect(find.text('إعادة المحاولة'), findsOneWidget);

      // Tap retry button
      await tester.tap(find.text('إعادة المحاولة'));
      await tester.pump();

      expect(retryPressed, true);
    });

    testWidgets('should not show retry button when onRetry is null',
        (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MaintenanceScreen(
            message: 'Test message',
            appName: 'test-app',
          ),
        ),
      );

      // Assert
      expect(find.text('إعادة المحاولة'), findsNothing);
    });
  });
}
